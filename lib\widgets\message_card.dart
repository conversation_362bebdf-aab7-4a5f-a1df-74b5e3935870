import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/data/models/base_message.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:intl/intl.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:hyt/view_models/message_view_model.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/widgets/unified_confirm_dialog.dart';
import 'package:hyt/l10n/app_localizations.dart';
import 'package:hyt/utils/enums.dart';
import 'package:hyt/styles/app_theme.dart';

class MessageCard extends StatelessWidget {
  final BaseMessage message;
  final MessageViewModel messageViewModel;
  final AppUserViewModel appUserViewModel;

  const MessageCard({
    super.key,
    required this.message,
    required this.messageViewModel,
    required this.appUserViewModel,
  });

  // 构建消息标题（与订单卡统一：顶部为短标题，正文展示内容）
  String _buildTitle(BuildContext context) {
    switch (message.type) {
      case MessageType.order:
        return AppLocalizations.of(context)?.orderStatusChange ?? '订单状态变更';
      case MessageType.system:
        return AppLocalizations.of(context)?.systemMessage ?? '系统消息';
      case MessageType.activity:
        return AppLocalizations.of(context)?.activityMessage ?? '活动消息';
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppCard(
      margin: EdgeInsets.only(bottom: AppInsets.gap4()),
      padding: EdgeInsets.zero,
      borderRadius: 12,
      elevation: 0,
      outlineAlpha: 0.6,
      child: InkWell(
        onTap: () {
          if (!message.isRead) {
            messageViewModel.markAsReadLocal(
              message.id ?? '',
              appUserViewModel.currentUser!,
            );
          }

          showConfirmDialog(
            context,
            title: AppLocalizations.of(context)?.messageDetail ?? '消息详情',
            content: message.content,
            okText: AppLocalizations.of(context)!.close,
          );
        },
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(
                  left: 12.w, right: 12.w, top: 1.h, bottom: 8.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        message.type.icon,
                        color: Theme.of(context).colorScheme.secondary,
                        size: 16.sp,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Row(
                                    children: [
                                      Text(
                                        _buildTitle(context),
                                        style: AppTextStyles.bodyMedium
                                            .copyWith(
                                                fontWeight: FontWeight.bold,
                                                height: 1.0),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      DateFormat('yyyy-MM-dd HH:mm')
                                          .format(message.createdAt),
                                      style: AppTextStyles.labelSmall.copyWith(
                                        height: 1.0,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurfaceVariant,
                                      ),
                                    ),
                                    SizedBox(width: 8.w),
                                    IconButton(
                                      onPressed: () {
                                        messageViewModel.deleteMessageLocal(
                                          message.id ?? '',
                                          appUserViewModel.currentUser!,
                                        );
                                      },
                                      icon: Icon(
                                        Icons.delete_outline,
                                        size: 16.sp,
                                        color:
                                            Theme.of(context).colorScheme.error,
                                      ),
                                      padding: EdgeInsets.zero,
                                      constraints: BoxConstraints(
                                        minWidth: 28.w,
                                        minHeight: 28.h,
                                      ),
                                      tooltip: AppLocalizations.of(context)
                                              ?.delete ??
                                          '删除',
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 0),
                  Text(
                    message.content,
                    style: AppTextStyles.bodySmall.copyWith(height: 1.1),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            if (!message.isRead)
              Positioned(
                left: 0,
                top: 0,
                bottom: 0,
                child: Container(
                  width: 2.w,
                  color: message.type.primaryColor(context).withAlpha(60),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
