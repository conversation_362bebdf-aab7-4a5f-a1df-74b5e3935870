import 'package:hyt/data/models/base_message.dart';
import 'package:hyt/utils/enums.dart';

abstract class BaseMessageRepo {
  // 获取所有消息
  Future<List<BaseMessage>> getAllUserMessages({required String userId});
  Future<List<BaseMessage>> getMessagesByOrderId(String orderId);
  // 根据消息类型获取消息
  Future<List<BaseMessage>> getMessagesByType({
    required String userId,
    required MessageType messageType,
  });

  // 获取未读消息数量
  Future<int> getUnreadMessageCount({required String userId});

  // 获取单条消息详情
  Future<BaseMessage?> getMessageById({required String messageId});

  // 标记消息为已读
  Future<bool> markMessageAsRead({required String messageId});

  // 发送订单消息，司机抢单成功后，系统给用户发送消息，通知已经被接单

// 发送订单消息
  Future<bool> sendOrderMessage({required BaseMessage message});
  //删除订单消息
  Future<bool> deleteOrderMessage({required String messageId});
  // 清空所有消息
  Future<bool> clearAllMessages({required String userId});
  // 删除单条消息
  Future<bool> deleteMessage({required String messageId});
  // 批量删除所有消息（逻辑删除）
  Future<bool> deleteAllMessages({required String userId});
}
