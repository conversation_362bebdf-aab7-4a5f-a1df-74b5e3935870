import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hyt/data/models/order.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/view_models/order_view_model.dart';
import 'package:hyt/styles/app_theme.dart';

import 'package:hyt/utils/snackbar_utils.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:hyt/widgets/unified_button.dart';

class PaymentScreen extends StatefulWidget {
  final Order order;

  const PaymentScreen({super.key, required this.order});

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  bool _isProcessing = false;
  String _selectedPaymentMethod = '微信支付'; // 默认选择微信支付
  late OrderViewModel _orderViewModel; // 保存OrderProvider引用

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 在这里获取Provider引用，避免在dispose后访问
    _orderViewModel = Provider.of<OrderViewModel>(context, listen: false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('订单支付'),
        centerTitle: true,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 订单信息卡片
              _buildOrderInfoCard(),
              SizedBox(height: 20.h),

              // 支付方式选择
              Text(
                '选择支付方式',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 12.h),
              _buildPaymentMethodSelector(),

              SizedBox(height: 40.h),

              // 支付按钮
              UnifiedButton.elevated(
                text: '立即支付 ￥${widget.order.price}',
                onPressed: _handlePayment,
                size: UnifiedButtonSize.large,
                isFullWidth: true,
                isLoading: _isProcessing,
                color: const Color(0xFF1E88E5),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderInfoCard() {
    return AppCard(
      padding: EdgeInsets.all(16.w),
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '订单信息',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '订单号: ${widget.order.orderId}',
                style: AppTextStyles.labelSmall,
              ),
            ],
          ),
          Divider(height: 24.h, color: Colors.grey[200]),
          _buildInfoRow('装货地点', widget.order.pickupLocationName),
          SizedBox(height: 8.h),
          _buildInfoRow('卸货地点', widget.order.deliveryLocationName),
          SizedBox(height: 8.h),
          _buildInfoRow('装货时间',
              DateFormat('yyyy-MM-dd HH:mm').format(widget.order.loadingTime)),
          SizedBox(height: 8.h),
          _buildInfoRow('司机', widget.order.driverName ?? '未分配'),
          SizedBox(height: 8.h),
          _buildInfoRow('车牌号', widget.order.vehiclePlate ?? '未分配'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
            width: 80.w, child: Text(label, style: AppTextStyles.labelMedium)),
        Expanded(
          child: Text(
            value,
            style: AppTextStyles.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethodSelector() {
    return Column(
      children: [
        _buildPaymentMethodTile('微信支付', FontAwesomeIcons.weixin, Colors.green),
        SizedBox(height: 12.h),
        _buildPaymentMethodTile('支付宝', FontAwesomeIcons.alipay, Colors.blue),
        SizedBox(height: 12.h),
        _buildPaymentMethodTile(
            '银行卡支付', FontAwesomeIcons.creditCard, Colors.orange),
      ],
    );
  }

  Widget _buildPaymentMethodTile(String method, IconData icon, Color color) {
    return InkWell(
      onTap: () {
        setState(() {
          _selectedPaymentMethod = method;
        });
      },
      child: AppCard(
        padding: AppInsets.h16().add(AppInsets.v12()),
        color: Colors.white,
        margin: EdgeInsets.zero,
        child: Row(
          children: [
            Icon(icon, color: color, size: 24.w),
            SizedBox(width: AppInsets.gapW16()),
            Text(
              method,
              style: AppTextStyles.bodyMedium,
            ),
            const Spacer(),
            if (_selectedPaymentMethod == method)
              Icon(
                Icons.check_circle,
                color: const Color(0xFF1E88E5),
                size: 20.w,
              ),
          ],
        ),
      ),
    );
  }

  // 处理支付操作
  Future<void> _handlePayment() async {
    if (!mounted) return; // 确保widget仍然挂载

    setState(() {
      _isProcessing = true;
    });

    try {
      // 模拟支付过程
      await Future.delayed(const Duration(seconds: 2));
      // 使用已保存的订单提供者引用
      // 更新订单状态为已完成(假设状态码3为已完成)
      final success =
          await _orderViewModel.processPayment(widget.order.orderId);
      if (success) {
        // 显示支付成功对话框
        await _showPaymentSuccessDialog();
      } else {
        // 显示支付失败提示
        if (mounted) {
          SnackBarUtils.showErrorSnackBar(context, '支付失败，请稍后重试');
        }
      }
    } catch (e) {
      if (!mounted) return; // 确保widget仍然挂载
      // 显示错误提示
      SnackBarUtils.showErrorSnackBar(context, '支付出错：${e.toString()}');
    }
  }

  // 显示支付成功对话框
  Future<void> _showPaymentSuccessDialog() async {
    if (!mounted) return; // 确保widget仍然挂载

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  color: Colors.green,
                  size: 60.w,
                ),
                SizedBox(height: 16.h),
                Text(
                  '支付成功',
                  style: AppTextStyles.headline4.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  '您已成功支付订单',
                  style: AppTextStyles.bodyMedium,
                ),
                SizedBox(height: 24.h),
                SizedBox(
                  width: double.infinity,
                  child: UnifiedButton.elevated(
                    text: '确定',
                    isFullWidth: true,
                    size: UnifiedButtonSize.large,
                    color: const Color(0xFF1E88E5),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
