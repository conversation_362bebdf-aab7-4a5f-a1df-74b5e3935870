import 'package:flutter/services.dart';

/// 输入格式化器工具类
class InputFormatters {
  /// 价格输入格式化器
  /// 限制只能输入数字和小数点，最多2位小数，最大值999999.99
  static TextInputFormatter priceFormatter() => _PriceInputFormatter();

  /// 手机号输入格式化器
  /// 限制只能输入数字，最多11位
  static TextInputFormatter phoneFormatter() =>
      FilteringTextInputFormatter.allow(RegExp(r'^\d{0,11}$'));

  /// 数字输入格式化器
  /// 只允许输入数字
  static TextInputFormatter digitsOnly() =>
      FilteringTextInputFormatter.digitsOnly;

  /// 字母和数字输入格式化器
  /// 只允许输入字母和数字
  static TextInputFormatter alphanumeric() =>
      FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]'));

  /// 中文、字母和数字输入格式化器
  /// 允许输入中文、字母和数字
  static TextInputFormatter chineseAlphanumeric() =>
      FilteringTextInputFormatter.allow(RegExp(r'[\u4e00-\u9fa5a-zA-Z0-9]'));
}

/// 自定义价格输入格式化器
class _PriceInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // 如果新值为空，允许
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // 防止以0开头的多位整数（如078、0123等）
    if (newValue.text.length > 1 &&
        newValue.text.startsWith('0') &&
        !newValue.text.startsWith('0.')) {
      return oldValue;
    }

    // 只允许数字和一个小数点
    final RegExp regExp = RegExp(r'^\d*\.?\d{0,2}$');

    // 检查是否符合格式
    if (regExp.hasMatch(newValue.text)) {
      // 防止以小数点开头
      if (newValue.text.startsWith('.')) {
        return TextEditingValue(
          text: '0${newValue.text}',
          selection: TextSelection.collapsed(offset: newValue.text.length + 1),
        );
      }

      // 防止多个小数点
      if (newValue.text.split('.').length > 2) {
        return oldValue;
      }

      // 防止输入过大的数值（最大999999.99）
      final double? value = double.tryParse(newValue.text);
      if (value != null && value > 999999.99) {
        return oldValue;
      }

      return newValue;
    }

    // 不符合格式，返回旧值
    return oldValue;
  }
}
