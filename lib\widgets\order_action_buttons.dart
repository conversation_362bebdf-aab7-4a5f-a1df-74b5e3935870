import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/styles/app_theme.dart';
import 'package:hyt/constants/order_status_constants.dart';
import 'package:hyt/data/models/order.dart';
import 'package:hyt/widgets/unified_button.dart';
import 'package:hyt/l10n/app_localizations.dart';

enum ButtonType {
  elevated,
  outlined,
  tonal,
  text,
}

class _ActionBtn {
  final String text;
  final Color color;
  final VoidCallback? onPressed;
  final ButtonType type;
  _ActionBtn(this.text, this.color, this.onPressed, this.type);
}

class OrderActionButtons extends StatelessWidget {
  final Order order;
  final String role;
  final String scene;
  final bool isLoading;
  final VoidCallback? onAcceptOrder;
  final VoidCallback? onPay;
  final VoidCallback? onContact;
  final VoidCallback? onDelete;
  final VoidCallback? onPickup;
  final VoidCallback? onDeliver;
  final VoidCallback? onCancel;

  const OrderActionButtons({
    super.key,
    required this.order,
    required this.role,
    required this.scene,
    this.isLoading = false,
    this.onAcceptOrder,
    this.onPay,
    this.onContact,
    this.onDelete,
    this.onPickup,
    this.onDeliver,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final List<_ActionBtn> btns = [];
    void addBtn(String text, Color color, VoidCallback? cb, ButtonType type) {
      if (cb != null) {
        btns.add(_ActionBtn(
          text,
          color,
          cb, // 始终保持回调函数，在按钮内部处理加载状态
          type,
        ));
      }
    }

    if (scene == 'detail') {
      if (role == 'shipper') {
        switch (order.statusCode) {
          case OrderStatusConstants.STATUS_WAITING:
            addBtn(
                AppLocalizations.of(context)?.cancelOrder ?? '取消订单',
                Theme.of(context).colorScheme.error,
                onCancel,
                ButtonType.elevated);
            break;
          case OrderStatusConstants.STATUS_ACCEPTED:
            // 货主在已接单状态下也可以取消订单
            addBtn(
                AppLocalizations.of(context)?.cancelOrder ?? '取消订单',
                Theme.of(context).colorScheme.error,
                onCancel,
                ButtonType.outlined);
            break;
          case OrderStatusConstants.STATUS_DELIVERED:
            addBtn(
                AppLocalizations.of(context)?.payNow ?? '立即支付',
                Theme.of(context).colorScheme.primary,
                onPay,
                ButtonType.elevated);
            break;
          case OrderStatusConstants.STATUS_COMPLETED:
          case OrderStatusConstants.STATUS_CANCELED:
            addBtn(
                AppLocalizations.of(context)?.deleteOrder ?? '删除订单',
                Theme.of(context).colorScheme.onSurfaceVariant,
                onDelete,
                ButtonType.elevated);
            break;
        }
      } else if (role == 'driver') {
        switch (order.statusCode) {
          case OrderStatusConstants.STATUS_WAITING:
            addBtn(
                AppLocalizations.of(context)?.grabNow ?? '立即抢单',
                Theme.of(context).colorScheme.primary,
                onAcceptOrder,
                ButtonType.elevated);
            break;
          case OrderStatusConstants.STATUS_ACCEPTED:
            addBtn(
                AppLocalizations.of(context)?.pickedUp ?? '已装货',
                Theme.of(context).colorScheme.primary,
                onPickup,
                ButtonType.elevated);
            // 司机在已接单状态下也可以取消订单
            addBtn(
                AppLocalizations.of(context)?.cancelOrder ?? '取消订单',
                Theme.of(context).colorScheme.error,
                onCancel,
                ButtonType.outlined);
            break;
          case OrderStatusConstants.STATUS_TRANSPORTING:
            addBtn(
                AppLocalizations.of(context)?.deliveredDone ?? '已送达',
                Theme.of(context).colorScheme.secondary,
                onDeliver,
                ButtonType.elevated);
            break;
          case OrderStatusConstants.STATUS_COMPLETED:
          case OrderStatusConstants.STATUS_CANCELED:
            addBtn(
                AppLocalizations.of(context)?.deleteOrder ?? '删除订单',
                Theme.of(context).colorScheme.onSurfaceVariant,
                onDelete,
                ButtonType.elevated);
            break;
        }
      }
    }

    final validBtns = btns.where((b) => b.onPressed != null).toList();
    if (validBtns.isEmpty) return const SizedBox.shrink();
    if (scene == 'detail' && validBtns.isNotEmpty) {
      // 详情页底部按钮横向平分宽度
      return Row(
        children: [
          for (int i = 0; i < validBtns.length; i++) ...[
            Expanded(child: _buildActionButton(validBtns[i], context)),
            if (i != validBtns.length - 1) SizedBox(width: AppInsets.gapW12()),
          ]
        ],
      );
    }
    // 其它场景自适应
    if (validBtns.length == 1) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildActionButton(validBtns[0], context),
        ],
      );
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        for (int i = 0; i < validBtns.length; i++) ...[
          _buildActionButton(validBtns[i], context),
          if (i != validBtns.length - 1) SizedBox(width: AppInsets.gapW12()),
        ]
      ],
    );
  }

  Widget _buildActionButton(_ActionBtn btn, BuildContext context) {
    // 将 ButtonType 映射到 UnifiedButtonType
    UnifiedButtonType unifiedType;
    switch (btn.type) {
      case ButtonType.elevated:
        unifiedType = UnifiedButtonType.elevated;
        break;
      case ButtonType.outlined:
        unifiedType = UnifiedButtonType.outlined;
        break;
      case ButtonType.text:
        unifiedType = UnifiedButtonType.text;
        break;
      case ButtonType.tonal:
        unifiedType = UnifiedButtonType.tonal;
        break;
    }

    // 使用 UnifiedButton 组件，保持完全相同的样式和功能
    return UnifiedButton(
      text: btn.text,
      onPressed: isLoading ? null : btn.onPressed,
      type: unifiedType,
      size: UnifiedButtonSize.medium, // 保持 40.h 高度
      color: btn.color,
      isLoading: isLoading,
    );
  }
}
