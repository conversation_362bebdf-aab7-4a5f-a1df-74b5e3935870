//订单状态历史记录模型
import 'package:hyt/utils/timezone_utils.dart';

class OrderStatusHistory {
  final String orderId; // 订单id，必填项
  final int statusCode; // 状态代码
  final DateTime timestamp; // 更新订单状态的时间戳
  final String? remark;

  OrderStatusHistory({
    required this.orderId,
    required this.statusCode,
    required this.timestamp,
    this.remark,
  });

  // 从Map创建OrderStatusHistory对象
  factory OrderStatusHistory.fromMap(Map<String, dynamic> map) {
    return OrderStatusHistory(
      orderId: map['order_id'],
      statusCode: map['status_code'],
      timestamp:
          TimezoneUtils.parseFromDatabase(map['timestamp']) ?? DateTime.now(),
      remark: map['remark'],
    );
  }

  // 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'order_id': orderId,
      'status_code': statusCode,
      'timestamp': TimezoneUtils.toDatabase(timestamp),
      'remark': remark,
    };
  }
}
