//底部导航栏页面，包含首页，订单，消息，我的四个页面
import 'package:flutter/material.dart';
import 'package:hyt/screens/home/<USER>';
import 'package:hyt/screens/profile/profile_screen.dart';
import 'package:hyt/screens/order/order_list_screen.dart';
import 'package:hyt/screens/message/message_center_screen.dart';
import 'package:hyt/l10n/app_localizations.dart';

class UserNavScreen extends StatefulWidget {
  const UserNavScreen({super.key});

  @override
  State<UserNavScreen> createState() => _UserNavScreenState();
}

class _UserNavScreenState extends State<UserNavScreen> {
  int _currentIndex = 0;
  final List<Widget> _pages = [
    const UserHomeScreen(),
    const OrderListScreen(),
    const MessageCenterScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_currentIndex],
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            selectedItemColor: Theme.of(context).colorScheme.primary,
            unselectedItemColor: Colors.grey.shade700,
            items: [
              BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: AppLocalizations.of(context)?.home ?? '首页',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.assignment),
                label: AppLocalizations.of(context)?.orders ?? '订单',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.message),
                label: AppLocalizations.of(context)?.messages ?? '消息',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: AppLocalizations.of(context)?.profile ?? '我的',
              ),
            ],
          ),
        ],
      ),
    );
  }
}
