-- 创建订单表
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    order_id TEXT NOT NULL UNIQUE,
    status_code INTEGER NOT NULL,
    pickup_location_name TEXT NOT NULL,
    pickup_address TEXT NOT NULL,
    delivery_location_name TEXT NOT NULL,
    delivery_address TEXT NOT NULL,
    goods_type TEXT,
    weight TEXT,
    volume TEXT,
    is_deleted_by_customer BOOLEAN DEFAULT FALSE,
    is_deleted_by_driver BOOLEAN DEFAULT FALSE,
    create_time TIMESTAMP
    WITH
        TIME ZONE NOT NULL DEFAULT NOW(),
        price DOUBLE PRECISION,
        customer_id TEXT NOT NULL,
        customer_name TEXT NOT NULL,
        customer_phone TEXT NOT NULL,
        remark TEXT,
        service_type TEXT NOT NULL,
        loading_time TIMESTAMP
    WITH
        TIME ZONE,
        vehicle_type TEXT NOT NULL,
        driver_id TEXT,
        driver_name TEXT,
        driver_phone TEXT,
        vehicle_plate TEXT,
        is_urgent BOOLEAN DEFAULT FALSE,
        driver_earnings DOUBLE PRECISION,
        platform_fee DOUBLE PRECISION,
        accept_time TIMESTAMP
    WITH
        TIME ZONE,
        estimated_arrival_time TIMESTAMP
    WITH
        TIME ZONE,
        actual_arrival_time TIMESTAMP
    WITH
        TIME ZONE,
        completion_time TIMESTAMP
    WITH
        TIME ZONE,
        cancel_time TIMESTAMP
    WITH
        TIME ZONE,
        cancel_reason TEXT,
        cancelled_by TEXT,
        cancel_fee DOUBLE PRECISION,
        created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX idx_orders_order_id ON orders (order_id);

CREATE INDEX idx_orders_status_code ON orders (status_code);

CREATE INDEX idx_orders_driver_id ON orders (driver_id);

CREATE INDEX idx_orders_cancelled_by ON orders (cancelled_by);

CREATE INDEX idx_orders_cancel_time ON orders (cancel_time);

-- 创建触发器函数更新updated_at字段
CREATE OR REPLACE FUNCTION update_order_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER trigger_update_order_updated_at
BEFORE UPDATE ON orders
FOR EACH ROW
EXECUTE FUNCTION update_order_updated_at();

-- 添加字段注释
COMMENT ON TABLE orders IS '订单表';

COMMENT ON COLUMN orders.cancel_time IS '订单取消时间';

COMMENT ON COLUMN orders.cancel_reason IS '取消原因';

COMMENT ON COLUMN orders.cancelled_by IS '取消发起方：shipper(货主) 或 driver(司机)';

COMMENT ON COLUMN orders.cancel_fee IS '取消费用（元）';