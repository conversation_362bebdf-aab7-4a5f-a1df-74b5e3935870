open router api key: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

# 快运通应用 分层架构实施指南

## 架构概述

我们正在将快运通应用从当前的架构优化为基于 Provider 的分层架构模式。这种架构模式有以下优势：

1.清晰的职责分离：各层仅关注自身逻辑
2.高效的状态管理：Provider 自动处理依赖和重建
3.强健的错误处理：统一捕获并显示异常
4.可扩展的架构：轻松添加新功能（如第三方登录）

## 目录结构


## 示例代码
##用户登录和注册的典型代码：
1.数据模型 (user_model.dart)
// core/models/user_model.dart
@JsonSerializable()
class User {
  final String id;
  final String email;
  final String? username;
  final DateTime? createdAt;

  User({
    required this.id,
    required this.email,
    this.username,
    this.createdAt,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}

2.全局状态管理 (user_provider.dart)
// core/providers/user_provider.dart
import 'package:flutter/foundation.dart';
import 'package:your_app/core/models/user_model.dart';

class UserProvider with ChangeNotifier {
  User? _currentUser;
  AuthStatus _authStatus = AuthStatus.unknown;

  User? get currentUser => _currentUser;
  AuthStatus get authStatus => _authStatus;

  // 更新用户状态
  void setUser(User? user) {
    _currentUser = user;
    _authStatus = user != null ? AuthStatus.authenticated : AuthStatus.unauthenticated;
    notifyListeners();
  }

  // 清除用户状态
  void clear() {
    _currentUser = null;
    _authStatus = AuthStatus.unauthenticated;
    notifyListeners();
  }
}

enum AuthStatus { unknown, authenticed, unauthenticated }
3.数据访问层 (auth_repository.dart)
// data/repositories/auth_repository.dart
import 'package:your_app/core/models/user_model.dart';

abstract class AuthRepository {
  Future<User> register(String email, String password, [String? username]);
  Future<User> login(String email, String password);
  Future<void> logout();
}

// 实现类（以 Supabase 为例）
class SupabaseAuthRepository implements AuthRepository {
  final SupabaseClient _supabase;

  SupabaseAuthRepository(this._supabase);

  @override
  Future<User> register(String email, String password, [String? username]) async {
    final response = await _supabase.auth.signUp(
      email: email,
      password: password,
      data: {'username': username},
    );

    if (response.user == null) throw Exception('Registration failed');
    
    return User(
      id: response.user!.id,
      email: email,
      username: username,
      createdAt: DateTime.now(),
    );
  }

  @override
  Future<User> login(String email, String password) async {
    final response = await _supabase.auth.signInWithPassword(
      email: email,
      password: password,
    );

    if (response.user == null) throw Exception('Login failed');
    
    return User(
      id: response.user!.id,
      email: email,
      username: response.user?.userMetadata?['username'],
    );
  }

  @override
  Future<void> logout() async {
    await _supabase.auth.signOut();
  }
}
4.业务逻辑层 (register_view_model.dart)
// domain/view_models/register_view_model.dart
import 'package:flutter/foundation.dart';
import 'package:your_app/core/providers/user_provider.dart';
import 'package:your_app/data/repositories/auth_repository.dart';

class RegisterViewModel with ChangeNotifier {
  final AuthRepository _authRepo;
  final UserProvider _userProvider;

  // UI 状态
  bool _isLoading = false;
  String? _errorMessage;

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  RegisterViewModel(this._authRepo, this._userProvider);

  Future<void> register(String email, String password, [String? username]) async {
    try {
      _startLoading();
      
      // 执行注册
      final user = await _authRepo.register(email, password, username);
      
      // 更新全局状态
      _userProvider.setUser(user);
    } catch (e) {
      _setError(e.toString());
    } finally {
      _stopLoading();
    }
  }

  void _startLoading() {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();
  }

  void _stopLoading() {
    _isLoading = false;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }
}
5.登录页面逻辑 (login_page.dart)
// presentation/pages/login_page.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:your_app/core/providers/user_provider.dart';
import 'package:your_app/domain/view_models/login_view_model.dart';

class LoginPage extends StatelessWidget {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final viewModel = context.watch<LoginViewModel>();
    final userProvider = context.watch<UserProvider>();

    // 登录成功自动跳转
    if (userProvider.authStatus == AuthStatus.authenticated) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.pushReplacementNamed(context, '/home');
      });
    }

    return Scaffold(
      appBar: AppBar(title: const Text('登录')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: AuthForm(
          emailController: _emailController,
          passwordController: _passwordController,
          isLoading: viewModel.isLoading,
          errorMessage: viewModel.errorMessage,
          onSubmit: () => viewModel.login(
            _emailController.text,
            _passwordController.text,
          ),
          submitText: '登录',
        ),
      ),
    );
  }
}
4.依赖注入配置
// main.dart
void main() {
  runApp(
    MultiProvider(
      providers: [
        // 全局状态
        ChangeNotifierProvider(create: (_) => UserProvider()),
        
        // 数据层
        Provider(create: (_) => SupabaseAuthRepository(Supabase.instance.client)),
        
        // ViewModel
        ChangeNotifierProxyProvider<UserProvider, LoginViewModel>(
          create: (_) => LoginViewModel(
            authRepo: SupabaseAuthRepository(Supabase.instance.client),
            userProvider: UserProvider(),
          ),
          update: (_, userProvider, vm) => vm!..updateDependencies(userProvider),
        ),
      ],
      child: const MyApp(),
    ),
  );
}

## 迁移策略

1. **渐进式迁移**：不要一次性重构所有代码，而是逐步迁移
2. **先创建核心组件**：先创建 Repository 和 ViewModel，然后再重构 View
3. **保持向后兼容**：确保新架构与现有代码兼容
4. **充分测试**：每完成一个模块的重构，进行充分的测试

## 注意事项

1. 在迁移过程中，可能需要同时维护旧架构和新架构的代码
2. 确保新架构的代码与现有代码的命名约定一致
3. 在迁移完成后，可以逐步删除旧架构的代码
4. 保持良好的文档记录，便于团队成员理解新架构

通过遵循本指南，我们将逐步将快运通应用迁移到基于 Provider + MVVC 的架构，提高代码质量和可维护性。