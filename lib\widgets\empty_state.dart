import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/styles/app_theme.dart';

class EmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? description;
  final Widget? action;
  final EdgeInsetsGeometry? padding;
  final bool useCard; // 是否以卡片样式展示
  final EdgeInsetsGeometry? cardMargin; // 卡片外边距

  const EmptyState({
    super.key,
    this.icon = Icons.inbox_outlined,
    required this.title,
    this.description,
    this.action,
    this.padding,
    this.useCard = false,
    this.cardMargin,
  });

  @override
  Widget build(BuildContext context) {
    final scheme = Theme.of(context).colorScheme;
    final content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 48.sp, color: scheme.outline),
        SizedBox(height: 12.h),
        Text(title, style: Theme.of(context).textTheme.titleMedium),
        if (description != null) ...[
          SizedBox(height: 4.h),
          Text(
            description!,
            textAlign: TextAlign.center,
            style: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(color: scheme.onSurfaceVariant),
          ),
        ],
        if (action != null) ...[
          SizedBox(height: 16.h),
          action!,
        ]
      ],
    );

    if (useCard) {
      return AppCard(
        margin: cardMargin ?? EdgeInsets.zero,
        padding: padding ?? AppInsets.h16().add(AppInsets.v12()),
        borderRadius: 12,
        elevation: 0,
        outlineAlpha: 0.6,
        child: Center(child: content),
      );
    }

    return Padding(
      padding: padding ??
          EdgeInsets.symmetric(
              horizontal: AppInsets.gapW24(), vertical: AppInsets.gap24()),
      child: Center(child: content),
    );
  }
}
