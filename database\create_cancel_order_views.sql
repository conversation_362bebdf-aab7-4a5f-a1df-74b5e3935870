-- 创建取消订单相关的视图和函数
-- 用于统计和查询取消订单数据

-- 1. 创建取消订单统计视图
CREATE OR REPLACE VIEW cancel_order_stats AS
SELECT 
    cancelled_by,
    DATE_TRUNC('month', cancel_time) as cancel_month,
    COUNT(*) as cancel_count,
    AVG(cancel_fee) as avg_cancel_fee,
    SUM(cancel_fee) as total_cancel_fee
FROM orders 
WHERE status_code = 3 -- 已取消状态
AND cancel_time IS NOT NULL
GROUP BY cancelled_by, DATE_TRUNC('month', cancel_time)
ORDER BY cancel_month DESC, cancelled_by;

-- 2. 创建用户取消订单统计视图
CREATE OR REPLACE VIEW user_cancel_stats AS
SELECT 
    CASE 
        WHEN cancelled_by = 'shipper' THEN customer_id
        WHEN cancelled_by = 'driver' THEN driver_id
        ELSE NULL
    END as user_id,
    cancelled_by as user_role,
    DATE_TRUNC('month', cancel_time) as cancel_month,
    COUNT(*) as cancel_count,
    SUM(cancel_fee) as total_cancel_fee
FROM orders 
WHERE status_code = 3 -- 已取消状态
AND cancel_time IS NOT NULL
AND cancelled_by IS NOT NULL
GROUP BY 
    CASE 
        WHEN cancelled_by = 'shipper' THEN customer_id
        WHEN cancelled_by = 'driver' THEN driver_id
        ELSE NULL
    END,
    cancelled_by,
    DATE_TRUNC('month', cancel_time)
ORDER BY cancel_month DESC, user_id;

-- 3. 创建获取用户月度取消次数的函数
CREATE OR REPLACE FUNCTION get_user_cancel_count(
    p_user_id TEXT,
    p_user_role TEXT,
    p_month DATE DEFAULT CURRENT_DATE
) RETURNS INTEGER AS $$
DECLARE
    cancel_count INTEGER;
    month_start DATE;
    month_end DATE;
BEGIN
    -- 计算月份的开始和结束日期
    month_start := DATE_TRUNC('month', p_month);
    month_end := month_start + INTERVAL '1 month' - INTERVAL '1 day';
    
    -- 查询取消次数
    SELECT COUNT(*)
    INTO cancel_count
    FROM orders
    WHERE status_code = 3 -- 已取消状态
    AND cancelled_by = p_user_role
    AND cancel_time >= month_start
    AND cancel_time <= month_end + INTERVAL '1 day'
    AND (
        (p_user_role = 'shipper' AND customer_id = p_user_id) OR
        (p_user_role = 'driver' AND driver_id = p_user_id)
    );
    
    RETURN COALESCE(cancel_count, 0);
END;
$$ LANGUAGE plpgsql;

-- 4. 创建检查订单是否可以取消的函数
CREATE OR REPLACE FUNCTION can_cancel_order(
    p_order_id TEXT,
    p_user_id TEXT,
    p_user_role TEXT
) RETURNS BOOLEAN AS $$
DECLARE
    order_record RECORD;
    can_cancel BOOLEAN := FALSE;
BEGIN
    -- 获取订单信息
    SELECT status_code, customer_id, driver_id
    INTO order_record
    FROM orders
    WHERE order_id = p_order_id;
    
    -- 检查订单是否存在
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- 检查订单状态是否允许取消
    -- 运输中(11)、已送达(12)、已完成(2)状态不允许取消
    IF order_record.status_code IN (11, 12, 2) THEN
        RETURN FALSE;
    END IF;
    
    -- 检查用户权限
    IF p_user_role = 'shipper' AND order_record.customer_id = p_user_id THEN
        can_cancel := TRUE;
    ELSIF p_user_role = 'driver' AND order_record.driver_id = p_user_id THEN
        can_cancel := TRUE;
    END IF;
    
    RETURN can_cancel;
END;
$$ LANGUAGE plpgsql;

-- 5. 创建取消原因统计视图
CREATE OR REPLACE VIEW cancel_reason_stats AS
SELECT 
    cancel_reason,
    cancelled_by,
    COUNT(*) as reason_count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (PARTITION BY cancelled_by), 2) as percentage
FROM orders 
WHERE status_code = 3 -- 已取消状态
AND cancel_reason IS NOT NULL
AND cancelled_by IS NOT NULL
GROUP BY cancel_reason, cancelled_by
ORDER BY cancelled_by, reason_count DESC;

-- 添加视图和函数的注释
COMMENT ON VIEW cancel_order_stats IS '取消订单统计视图：按角色和月份统计取消订单数量和费用';
COMMENT ON VIEW user_cancel_stats IS '用户取消订单统计视图：按用户和月份统计取消订单数据';
COMMENT ON VIEW cancel_reason_stats IS '取消原因统计视图：统计各种取消原因的频率';
COMMENT ON FUNCTION get_user_cancel_count IS '获取用户指定月份的取消订单次数';
COMMENT ON FUNCTION can_cancel_order IS '检查订单是否可以被指定用户取消';
