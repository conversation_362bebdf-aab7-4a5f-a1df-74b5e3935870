import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/view_models/profile_view_model.dart';
import 'package:hyt/utils/snackbar_utils.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

/// 司机实名认证页面
class DriverVerificationScreen extends StatefulWidget {
  const DriverVerificationScreen({super.key});

  @override
  State<DriverVerificationScreen> createState() =>
      _DriverVerificationScreenState();
}

class _DriverVerificationScreenState extends State<DriverVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _vehiclePlateController = TextEditingController();

  // 证件照片
  File? _idCardFrontImage; // 身份证正面
  File? _idCardBackImage; // 身份证背面
  File? _driverLicenseImage; // 驾驶证
  File? _vehicleLicenseImage; // 行驶证

  bool _isSubmitting = false;

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _vehiclePlateController.dispose();
    super.dispose();
  }

  // 选择图片
  Future<void> _pickImage(
      ImageSource source, Function(File?) onImagePicked) async {
    try {
      final picker = ImagePicker();
      final pickedFile =
          await picker.pickImage(source: source, imageQuality: 80);

      if (pickedFile != null) {
        setState(() {
          onImagePicked(File(pickedFile.path));
        });
      }
    } catch (e) {
      // 处理错误
      if (!mounted) return;
      SnackBarUtils.showErrorSnackBar(context, '选择图片失败: $e');
    }
  }

  // 显示选择图片来源的底部弹窗
  void _showImageSourceActionSheet(
      BuildContext context, Function(File?) onImagePicked) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('从相册选择'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.gallery, onImagePicked);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('拍照'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.camera, onImagePicked);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // 提交认证信息
  Future<void> _submitVerification() async {
    if (_formKey.currentState!.validate()) {
      // TODO: 暂时不做判断，后续补充证件校验算法后开启验证
      // 检查是否上传了所有必要的证件照片
      // if (_idCardFrontImage == null ||
      //     _idCardBackImage == null ||
      //     _driverLicenseImage == null ||
      //     _vehicleLicenseImage == null) {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     const SnackBar(content: Text('请上传所有必要的证件照片')),
      //   );
      //   return;
      // }

      setState(() {
        _isSubmitting = true;
      });

      try {
        final viewModel = Provider.of<ProfileViewModel>(context, listen: false);

        // 创建认证信息对象
        final verificationData = {
          'name': _nameController.text,
          'phone': _phoneController.text,
          'vehiclePlate': _vehiclePlateController.text,
          // TODO：暂时不做判断，后续补充证件校验算法后开启验证
          // 'idCardFront': _idCardFrontImage!.path,
          // 'idCardBack': _idCardBackImage!.path,
          // 'driverLicense': _driverLicenseImage!.path,
          // 'vehicleLicense': _vehicleLicenseImage!.path,
        };

        // 调用ViewModel中的提交认证方法
        final success =
            await viewModel.submitDriverVerification(verificationData);
        if (!mounted) return;
        if (success) {
          // 认证成功
          SnackBarUtils.showSuccessSnackBar(context, '认证信息提交成功，等待审核');
          Navigator.pop(context); // 返回上一页
        } else {
          // 认证失败
          SnackBarUtils.showErrorSnackBar(
              context, viewModel.error ?? '认证失败，请重试');
        }
      } catch (e) {
        if (context.mounted) {
          SnackBarUtils.showErrorSnackBar(context, '提交失败: $e');
        }
      } finally {
        if (mounted) {
          setState(() {
            _isSubmitting = false;
          });
        }
      }
    }
  }

  // 构建图片上传卡片
  Widget _buildImageUploadCard(
      String title, File? image, Function(File?) onImagePicked) {
    return GestureDetector(
      onTap: () => _showImageSourceActionSheet(context, onImagePicked),
      child: AppCard(
        margin: EdgeInsets.only(bottom: 16.h),
        color: Colors.grey.shade100,
        padding: EdgeInsets.zero,
        child: SizedBox(
          width: double.infinity,
          height: 80.h,
          child: image == null
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.add_photo_alternate_outlined,
                    size: 24.sp,
                    color: Colors.grey.shade600,
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    title,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Colors.grey.shade700,
                    ),
                  ),
                ],
              )
            : Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8.r),
                    child: Image.file(
                      image,
                      width: double.infinity,
                      height: 80.h,
                      fit: BoxFit.cover,
                    ),
                  ),
                  Positioned(
                    top: 8.h,
                    right: 8.w,
                    child: Container(
                      padding: EdgeInsets.all(4.w),
                      decoration: const BoxDecoration(
                        color: Colors.black54,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.edit,
                        color: Colors.white,
                        size: 16.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ),
      );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('司机实名认证'),
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(12.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 表单说明
                Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.blue.shade700,
                        size: 20.sp,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          '请填写真实信息并上传清晰的证件照片，审核通过后将获得接单权限',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 12.h),

                // 基本信息
                Text(
                  '基本信息',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),

                // 姓名
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: '真实姓名',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    prefixIcon: const Icon(Icons.person_outline),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入真实姓名';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),

                // 手机号
                TextFormField(
                  controller: _phoneController,
                  decoration: InputDecoration(
                    labelText: '联系电话',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    prefixIcon: const Icon(Icons.phone_android),
                  ),
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入联系电话';
                    }
                    // 简单的手机号验证
                    if (value.length != 11 ||
                        !RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                      return '请输入有效的手机号';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 16.h),

                // 车牌号
                TextFormField(
                  controller: _vehiclePlateController,
                  decoration: InputDecoration(
                    labelText: '车牌号',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    prefixIcon: const Icon(Icons.directions_car_outlined),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入车牌号';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 24.h),

                // 证件照片
                Text(
                  '证件照片',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),

                // 证件照片 - 身份证正反面
                Row(
                  children: [
                    Expanded(
                      child: _buildImageUploadCard(
                        '上传身份证正面照片',
                        _idCardFrontImage,
                        (file) => setState(() => _idCardFrontImage = file),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildImageUploadCard(
                        '上传身份证背面照片',
                        _idCardBackImage,
                        (file) => setState(() => _idCardBackImage = file),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12.h),

                // 证件照片 - 驾驶证和行驶证
                Row(
                  children: [
                    Expanded(
                      child: _buildImageUploadCard(
                        '上传驾驶证照片',
                        _driverLicenseImage,
                        (file) => setState(() => _driverLicenseImage = file),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: _buildImageUploadCard(
                        '上传行驶证照片',
                        _vehicleLicenseImage,
                        (file) => setState(() => _vehicleLicenseImage = file),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 12.h),

                // 提交按钮
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isSubmitting ? null : _submitVerification,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      padding: EdgeInsets.symmetric(vertical: 14.h),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    child: _isSubmitting
                        ? SizedBox(
                            height: 20.h,
                            width: 20.w,
                            child: const CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                              strokeWidth: 2.0,
                            ),
                          )
                        : Text(
                            '提交认证',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
                SizedBox(height: 32.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
