import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/styles/text_styles.dart';

/// 统一按钮类型枚举
enum UnifiedButtonType {
  elevated, // 主要按钮
  outlined, // 次要按钮
  text, // 文本按钮
  tonal, // 色调按钮
}

/// 统一按钮尺寸枚举
enum UnifiedButtonSize {
  small, // 小按钮 (32.h)
  medium, // 中等按钮 (40.h) - 默认
  large, // 大按钮 (50.h)
}

/// 统一的按钮组件
/// 基于 OrderActionButtons 的样式规范，确保全项目按钮样式一致
class UnifiedButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final UnifiedButtonType type;
  final UnifiedButtonSize size;
  final Color? color;
  final bool isLoading;
  final bool isDestructive;
  final bool isFullWidth;
  final EdgeInsetsGeometry? padding;
  final Widget? icon;

  const UnifiedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = UnifiedButtonType.elevated,
    this.size = UnifiedButtonSize.medium,
    this.color,
    this.isLoading = false,
    this.isDestructive = false,
    this.isFullWidth = false,
    this.padding,
    this.icon,
  });

  // 便捷构造函数
  const UnifiedButton.elevated({
    super.key,
    required this.text,
    this.onPressed,
    this.size = UnifiedButtonSize.medium,
    this.color,
    this.isLoading = false,
    this.isDestructive = false,
    this.isFullWidth = false,
    this.padding,
    this.icon,
  }) : type = UnifiedButtonType.elevated;

  const UnifiedButton.outlined({
    super.key,
    required this.text,
    this.onPressed,
    this.size = UnifiedButtonSize.medium,
    this.color,
    this.isLoading = false,
    this.isDestructive = false,
    this.isFullWidth = false,
    this.padding,
    this.icon,
  }) : type = UnifiedButtonType.outlined;

  const UnifiedButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.size = UnifiedButtonSize.medium,
    this.color,
    this.isLoading = false,
    this.isDestructive = false,
    this.isFullWidth = false,
    this.padding,
    this.icon,
  }) : type = UnifiedButtonType.text;

  const UnifiedButton.tonal({
    super.key,
    required this.text,
    this.onPressed,
    this.size = UnifiedButtonSize.medium,
    this.color,
    this.isLoading = false,
    this.isDestructive = false,
    this.isFullWidth = false,
    this.padding,
    this.icon,
  }) : type = UnifiedButtonType.tonal;

  @override
  Widget build(BuildContext context) {
    // 获取按钮尺寸
    final double btnHeight = _getButtonHeight();
    final BorderRadius borderRadius = BorderRadius.circular(10.r);

    // 确定按钮颜色
    final Color buttonColor = _getButtonColor(context);

    // 构建按钮内容
    Widget buttonChild = _buildButtonContent(context);

    // 包装为全宽度（如果需要）
    Widget button = _buildButtonByType(
        context, btnHeight, borderRadius, buttonColor, buttonChild);

    if (isFullWidth) {
      button = SizedBox(
        width: double.infinity,
        child: button,
      );
    }

    return button;
  }

  /// 获取按钮高度
  double _getButtonHeight() {
    switch (size) {
      case UnifiedButtonSize.small:
        return 32.h;
      case UnifiedButtonSize.medium:
        return 40.h; // 与 OrderActionButtons 保持一致
      case UnifiedButtonSize.large:
        return 50.h;
    }
  }

  /// 获取按钮颜色
  Color _getButtonColor(BuildContext context) {
    if (color != null) return color!;
    if (isDestructive) return Theme.of(context).colorScheme.error;
    return Theme.of(context).colorScheme.primary;
  }

  /// 构建按钮内容
  Widget _buildButtonContent(BuildContext context) {
    final scheme = Theme.of(context).colorScheme;

    if (isLoading) {
      final Color progressColor = (type == UnifiedButtonType.elevated)
          ? scheme.onPrimary
          : (color ?? scheme.primary);
      return Semantics(
        label: '处理中',
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 16.w,
              height: 16.h,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(progressColor),
              ),
            ),
            SizedBox(width: 8.w),
            Text('处理中...', style: AppTextStyles.bodyLarge),
          ],
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          SizedBox(width: 8.w),
          Text(text, style: AppTextStyles.bodyLarge),
        ],
      );
    }

    return Text(text, style: AppTextStyles.bodyLarge);
  }

  /// 根据类型构建按钮
  Widget _buildButtonByType(
    BuildContext context,
    double btnHeight,
    BorderRadius borderRadius,
    Color buttonColor,
    Widget buttonChild,
  ) {
    switch (type) {
      case UnifiedButtonType.elevated:
        return SizedBox(
          height: btnHeight,
          child: ElevatedButton(
            onPressed: isLoading ? null : onPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: buttonColor,
              foregroundColor: Colors.white,
              textStyle: AppTextStyles.bodyLarge,
              shape: RoundedRectangleBorder(borderRadius: borderRadius),
              minimumSize: Size(0, btnHeight),
              elevation: 0,
              disabledBackgroundColor: buttonColor.withAlpha(128),
              disabledForegroundColor: Colors.white.withAlpha(179),
              padding: padding,
            ),
            child: buttonChild,
          ),
        );

      case UnifiedButtonType.outlined:
        return SizedBox(
          height: btnHeight,
          child: OutlinedButton(
            onPressed: isLoading ? null : onPressed,
            style: OutlinedButton.styleFrom(
              foregroundColor: buttonColor,
              side: BorderSide(
                color: isLoading ? buttonColor.withAlpha(128) : buttonColor,
                width: 1.2,
              ),
              textStyle: AppTextStyles.bodyLarge,
              shape: RoundedRectangleBorder(borderRadius: borderRadius),
              minimumSize: Size(0, btnHeight),
              disabledForegroundColor: buttonColor.withAlpha(128),
              padding: padding,
            ),
            child: buttonChild,
          ),
        );

      case UnifiedButtonType.text:
        return SizedBox(
          height: btnHeight,
          child: TextButton(
            onPressed: isLoading ? null : onPressed,
            style: TextButton.styleFrom(
              foregroundColor: buttonColor,
              textStyle: AppTextStyles.bodyLarge,
              shape: RoundedRectangleBorder(borderRadius: borderRadius),
              minimumSize: Size(0, btnHeight),
              disabledForegroundColor: buttonColor.withAlpha(128),
              padding: padding,
            ),
            child: buttonChild,
          ),
        );

      case UnifiedButtonType.tonal:
        return SizedBox(
          height: btnHeight,
          child: FilledButton.tonal(
            onPressed: isLoading ? null : onPressed,
            style: FilledButton.styleFrom(
              foregroundColor: buttonColor,
              backgroundColor: buttonColor.withAlpha(31),
              textStyle: AppTextStyles.bodyLarge,
              shape: RoundedRectangleBorder(borderRadius: borderRadius),
              minimumSize: Size(0, btnHeight),
              padding: padding,
            ),
            child: buttonChild,
          ),
        );
    }
  }
}
