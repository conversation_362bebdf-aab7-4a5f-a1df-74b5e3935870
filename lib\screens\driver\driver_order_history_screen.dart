//司机订单历史页面
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/widgets/empty_state.dart';
import 'package:hyt/styles/app_theme.dart';
import 'package:hyt/constants/order_status_constants.dart';
import 'package:hyt/view_models/order_view_model.dart';
import 'package:provider/provider.dart';
import 'package:hyt/data/models/order.dart';
import 'package:hyt/widgets/search_app_bar.dart';
import 'package:hyt/widgets/order_card.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/constants/routes.dart';
import 'package:hyt/l10n/app_localizations.dart';

class DriverOrderHistoryScreen extends StatefulWidget {
  const DriverOrderHistoryScreen({super.key});

  @override
  State<DriverOrderHistoryScreen> createState() =>
      _DriverOrderHistoryScreenState();
}

class _DriverOrderHistoryScreenState extends State<DriverOrderHistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late List<String> _tabTitles;
  final TextEditingController _searchController = TextEditingController();
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    _tabTitles = const []; // 先占位，避免在initState里访问context
    _tabController = TabController(length: 6, vsync: this);
    _searchController.addListener(() {
      setState(() {}); // 搜索框变化触发过滤
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 在此安全地访问依赖上下文的本地化资源
    _tabTitles = [
      AppLocalizations.of(context)!.all,
      AppLocalizations.of(context)!.statusAccepted,
      AppLocalizations.of(context)!.statusTransporting,
      AppLocalizations.of(context)!.statusDelivered,
      AppLocalizations.of(context)!.statusCompleted,
      AppLocalizations.of(context)!.statusCanceled,
    ];
    if (_tabController.length != _tabTitles.length) {
      _tabController.dispose();
      _tabController = TabController(length: _tabTitles.length, vsync: this);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 获取订单数据
    final orderViewModel = Provider.of<OrderViewModel>(context);
    // 获取司机相关的订单
    final List<Order> driverOrders = orderViewModel.getDriverOrders();

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: SearchAppBar(
        searchController: _searchController,
        tabController: _tabController,
        tabTitles: _tabTitles,
        hintText:
            AppLocalizations.of(context)?.searchOrderHint ?? '搜索订单号/地址/货物',
        centerTitle: false,
        elevation: 0.5,
        backgroundColor: Theme.of(context).colorScheme.surface,
        // 统一 Tab 样式，参照消息中心
        tabLabelStyle:
            AppTextStyles.withWeight(AppTextStyles.bodyMedium, FontWeight.bold),
        tabUnselectedLabelStyle: AppTextStyles.bodyMedium,
        tabIndicatorWeight: 3,
        tabIsScrollable: true,
        tabAlignment: TabAlignment.center,
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          {'code': null},
          {'code': OrderStatusConstants.STATUS_ACCEPTED},
          {'code': OrderStatusConstants.STATUS_TRANSPORTING},
          {'code': OrderStatusConstants.STATUS_DELIVERED},
          {'code': OrderStatusConstants.STATUS_COMPLETED},
          {'code': OrderStatusConstants.STATUS_CANCELED},
        ].map((cfg) {
          // 根据标签和搜索关键字筛选订单
          List<Order> filteredOrders = driverOrders;

          // 1. 按 Tab 筛选（根据状态码而非文字）
          final int? code = cfg['code'];
          if (code != null) {
            filteredOrders = driverOrders
                .where((order) => order.statusCode == code)
                .toList();
          }

          // 2. 按搜索关键字筛选
          String searchTerm = _searchController.text.trim().toLowerCase();
          if (searchTerm.isNotEmpty) {
            filteredOrders = filteredOrders.where((order) {
              final id = order.orderId.toLowerCase();
              final pickup = order.pickupAddress.toLowerCase();
              final delivery = order.deliveryAddress.toLowerCase();

              return id.contains(searchTerm) ||
                  pickup.contains(searchTerm) ||
                  delivery.contains(searchTerm);
            }).toList();
          }

          return _buildOrderList(filteredOrders);
        }).toList(),
      ),
    );
  }

  // 构建订单列表
  Widget _buildOrderList(List<Order> orders) {
    if (orders.isEmpty) {
      return ListView(
        padding:
            AppInsets.pageGutter().add(EdgeInsets.only(top: AppInsets.gap2())),
        children: [
          SizedBox(height: AppInsets.gap4()),
          EmptyState(
            useCard: true,
            cardMargin: EdgeInsets.zero,
            title: AppLocalizations.of(context)?.noOrders ?? '暂无订单',
            description:
                AppLocalizations.of(context)?.orderListDesc ?? '这里会展示您的订单列表',
          ),
        ],
      );
    }

    return ListView.builder(
      padding:
          AppInsets.pageGutter().add(EdgeInsets.only(top: AppInsets.gap2())),
      itemCount: orders.length,
      cacheExtent: 120.h,
      itemBuilder: (context, index) {
        final order = orders[index];
        return _buildOrderCard(order);
      },
    );
  }

  // 构建订单卡片
  Widget _buildOrderCard(Order order) {
    return OrderCard(
      order: order,
      role: 'driver', // 司机角色
      onViewDetail: () {
        // 查看订单详情
        Navigator.pushNamed(
          context,
          Routes.orderDetail,
          arguments: order,
        );
      },
    );
  }
}
