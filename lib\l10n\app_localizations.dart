import 'package:flutter/material.dart';

class AppLocalizations {
  final Locale locale;

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  // 支持的语言列表
  static const List<Locale> supportedLocales = [
    Locale('zh', 'CN'), // 中文
    Locale('en', 'US'), // 英语
    Locale('km', 'KH'), // 高棉语
  ];

  // 检查是否支持该语言
  static bool isSupported(Locale locale) {
    return supportedLocales.any((supportedLocale) =>
        supportedLocale.languageCode == locale.languageCode);
  }

  // 本地化文本映射
  static const Map<String, Map<String, String>> _localizedValues = {
    'zh': {
      'settings': '设置',
      'general_settings': '通用设置',
      'appearance_settings': '外观设置',
      'notification_settings': '通知设置',
      'privacy_security': '隐私安全',
      'about_app': '关于应用',
      'language': '语言',
      'language_settings': '语言设置',
      'select_language': '选择语言',
      'current': '当前',
      'chinese': '中文',
      'english': 'English',
      'khmer': 'ខ្មែរ',
      'notifications': '通知',
      'push_notifications': '推送通知',
      'push_notifications_desc': '接收订单状态、系统消息等通知',
      'sound_alerts': '声音提醒',
      'sound_alerts_desc': '新消息时播放提示音',

      'email_notifications': '邮件通知',
      'privacy': '隐私',
      'privacy_policy': '隐私政策',
      'privacy_policy_desc': '查看隐私保护政策',
      'privacy_policy_content': '这里将显示详细的隐私政策内容...',
      'user_agreement': '用户协议',
      'user_agreement_desc': '查看用户服务协议',
      'user_agreement_content': '这里将显示详细的用户协议内容...',
      'account_security': '账户安全',
      'account_security_desc': '管理密码、绑定手机等',
      'account_security_developing': '账户安全功能开发中...',
      'app_version': '应用版本',
      'check_update': '检查更新',
      'latest_version': '当前已是最新版本',
      'rate_app': '评价应用',
      'rate_app_desc': '在应用商店给我们评分',
      'thank_you_support': '感谢您的支持！',
      'feedback': '意见反馈',
      'feedback_desc': '向我们反馈问题或建议',
      'feedback_hint': '请输入您的反馈内容...',
      'thank_you_feedback': '感谢您的反馈！',
      'submit': '提交',
      'data_usage': '数据使用',
      'location_services': '位置服务',
      'about': '关于',
      'version': '版本',
      'terms_of_service': '服务条款',
      'cancel': '取消',
      'confirm': '确定',
      'save': '保存',
      // 登录页面
      'app_name': '快运通',
      'app_description': '专业的物流运输平台',
      'phone_hint': '请输入手机号',
      'phone_required': '请输入手机号',
      'phone_invalid': '请输入有效的手机号',
      'nickname_hint': '请输入昵称',
      'nickname_required': '请输入昵称',
      'nickname_too_short': '昵称长度不能少于2位',
      'password_hint': '请输入密码',
      'password_hint_register': '密码需包含字母和数字',
      'password_required': '请输入密码',
      'password_too_short': '密码长度不能少于6位',
      'password_need_letter': '密码需要包含字母',
      'password_need_number': '密码需要包含数字',
      'remember_password': '记住密码',
      'forgot_password': '忘记密码？',
      'login': '登录',
      'register': '注册',
      'no_account': '还没有账号？',
      'register_now': '立即注册',
      'back_to_login': '返回登录',
      'login_failed': '登录失败',
      'register_success': '注册成功',
      'register_failed': '注册失败',
      'phone_invalid_format': '请输入有效的手机号',
      'password_strength_requirement': '密码需要至少6位，包含字母和数字',
      'already_have_account': '已有账号？',
      // 主页面
      'long_distance_freight': '长途货运',
      'same_city_express': '同城速运',
      'loading_address': '装',
      'unloading_address': '卸',
      'select_loading_address': '选择装货地址',
      'select_unloading_address': '选择卸货地址',
      'driver_join': '司机加入',
      'become_professional_fleet': '成为专业车队',
      'logistics_publish': '物流发布',
      'quick_publish_cargo': '快速发布货源',
      'call_car_process': '叫车流程',
      'common_questions': '常见问题',
      'step_select_car_address': '1. 选车选址',
      'step_fill_requirements': '2. 填写要求',
      'step_confirm_call': '3. 确认叫车',
      'hot_activities': '热门活动',
      'newbie_exclusive': '新人专享',
      'recharge_discount': '充值优惠',
      // 导航栏
      'home': '首页',
      'orders': '订单',
      'messages': '消息',
      'profile': '我的',
      // 订单页面
      'search_order_hint': '搜索订单号/地址/客户',
      'refresh_order_data': '刷新订单数据',
      'no_orders': '暂无订单',
      'order_no': '订单号',
      'order_list_desc': '这里会展示您的订单列表',
      'cancel_order': '取消订单',
      'confirm_cancel': '确认取消',
      'select_cancel_reason': '请选择取消原因',
      'pay_now': '立即支付',
      'delete_order': '删除订单',
      'grab_now': '立即抢单',
      'picked_up': '已装货',
      'delivered_done': '已送达',
      // 个人资料页面
      // 订单状态名称
      'status_waiting': '待接单',
      'status_accepted': '已接单',
      'status_transporting': '运输中',
      'status_delivered': '已送达',
      'status_completed': '已完成',
      'status_canceled': '已取消',
      // 订单状态描述
      'desc_status_waiting': '等待司机接单中，请耐心等待',
      'desc_status_accepted': '司机已接单，正在前往取货地点',
      'desc_status_transporting': '货物正在运输中',
      'desc_status_delivered': '货物已送达目的地',
      'desc_status_completed': '订单已完成',
      'desc_status_canceled': '订单已取消',
      'current_status': '当前状态',
      'logout': '退出登录',
      'not_logged_in': '未登录用户',
      'shipper': '货主',
      'driver': '司机',
      'credit_score': '信用分',
      'order_count': '下单数',
      'role_switch': '角色切换',
      'current_role': '当前角色',
      'switching_role': '正在切换角色...',
      'role_switch_failed': '角色切换失败',
      'account_management': '账户管理',
      'real_name_verification': '实名认证',
      'verified': '已认证',
      'not_verified': '未认证',
      're_verification_confirm': '您已完成认证，需要重新认证吗？',
      're_verify': '重新认证',
      'wallet_management': '钱包管理',
      'my_wallet': '我的钱包',
      'balance': '余额',
      'withdrawal_account': '提现账户',
      'invoice_management': '开票管理',
      'other_settings': '其他设置',
      'contact_customer_service': '联系客服',
      // 删除重复的 general_settings 键值对,因为在前面已经定义过了
      // 消息类型
      'system_message': '系统消息',
      'activity_message': '活动消息',
      'order_message': '订单消息',
      'system': '系统',
      'activity': '活动',

      // 消息中心页面
      'message_center': '消息中心',
      'all': '全部',
      'mark_all_as_read': '全部已读',
      'delete_all': '全部删除',
      'confirm_delete_all': '确认删除全部消息',
      'delete_all_message': '此操作将删除所有消息，是否继续？',
      'close': '关闭',
      'order_status_change': '订单状态变更',
      'mark_as_read': '标记已读',
      'delete': '删除',
      // 新增通用/消息中心文案
      'no_messages': '暂无消息',
      'message_detail': '消息详情',
      'delete_all_success': '已删除所有消息',
      'error_title': '出错了',
      'retry': '重试',
    },
    'en': {
      'settings': 'Settings',
      'general_settings': 'General Settings',
      'appearance_settings': 'Appearance Settings',
      'notification_settings': 'Notification Settings',
      'privacy_security': 'Privacy & Security',
      'about_app': 'About App',
      'language': 'Language',
      'language_settings': 'Language Settings',
      'select_language': 'Select Language',
      'current': 'Current',
      'chinese': '中文',
      'english': 'English',
      'khmer': 'ខ្មែរ',
      'notifications': 'Notifications',
      'push_notifications': 'Push Notifications',
      'push_notifications_desc':
          'Receive order status, system messages and other notifications',
      'sound_alerts': 'Sound Alerts',
      'sound_alerts_desc': 'Play notification sound for new messages',

      'email_notifications': 'Email Notifications',
      'privacy': 'Privacy',
      'privacy_policy': 'Privacy Policy',
      'privacy_policy_desc': 'View privacy protection policy',
      'privacy_policy_content':
          'Detailed privacy policy content will be displayed here...',
      'user_agreement': 'User Agreement',
      'user_agreement_desc': 'View user service agreement',
      'user_agreement_content':
          'Detailed user agreement content will be displayed here...',
      'account_security': 'Account Security',
      'account_security_desc': 'Manage password, phone binding, etc.',
      'account_security_developing':
          'Account security feature is under development...',
      'app_version': 'App Version',
      'check_update': 'Check Update',
      'latest_version': 'Already the latest version',
      'rate_app': 'Rate App',
      'rate_app_desc': 'Rate us in the app store',
      'thank_you_support': 'Thank you for your support!',
      'feedback': 'Feedback',
      'feedback_desc': 'Report issues or suggestions to us',
      'feedback_hint': 'Please enter your feedback...',
      'thank_you_feedback': 'Thank you for your feedback!',
      'submit': 'Submit',
      'data_usage': 'Data Usage',
      'location_services': 'Location Services',
      'about': 'About',
      'version': 'Version',
      'terms_of_service': 'Terms of Service',
      'cancel': 'Cancel',
      'confirm': 'Confirm',
      'save': 'Save',
      // 登录页面
      'app_name': 'Freight Express',
      'app_description': 'Professional logistics transport platform',
      'phone_hint': 'Enter phone number',
      'phone_required': 'Please enter phone number',
      'phone_invalid': 'Please enter a valid phone number',
      'nickname_hint': 'Enter nickname',
      'nickname_required': 'Please enter nickname',
      'nickname_too_short': 'Nickname must be at least 2 characters',
      'password_hint': 'Enter password',
      'password_hint_register': 'Password must contain letters and numbers',
      'password_required': 'Please enter password',
      'password_too_short': 'Password must be at least 6 characters',
      'password_need_letter': 'Password must contain letters',
      'password_need_number': 'Password must contain numbers',
      'remember_password': 'Remember password',
      'forgot_password': 'Forgot password?',
      'login': 'Login',
      'register': 'Register',
      'no_account': 'Don\'t have an account?',
      'register_now': 'Register now',
      'back_to_login': 'Back to login',
      'login_failed': 'Login failed',
      'register_success': 'Registration successful',
      'register_failed': 'Registration failed',
      'phone_invalid_format': 'Please enter a valid phone number',
      'password_strength_requirement':
          'Password must be at least 6 characters with letters and numbers',
      'already_have_account': 'Already have an account?',
      // 主页面
      'long_distance_freight': 'Long Distance Freight',
      'same_city_express': 'Same City Express',
      'loading_address': 'Load',
      'unloading_address': 'Unload',
      'select_loading_address': 'Select Loading Address',
      'select_unloading_address': 'Select Unloading Address',
      'driver_join': 'Driver Join',
      'become_professional_fleet': 'Become Professional Fleet',
      'logistics_publish': 'Logistics Publish',
      'quick_publish_cargo': 'Quick Publish Cargo',
      'call_car_process': 'Call Car Process',
      'common_questions': 'Common Questions',
      'step_select_car_address': '1. Select Car & Address',
      'step_fill_requirements': '2. Fill Requirements',
      'step_confirm_call': '3. Confirm Call',
      'hot_activities': 'Hot Activities',
      'newbie_exclusive': 'Newbie Exclusive',
      'recharge_discount': 'Recharge Discount',
      // 导航栏
      'home': 'Home',
      'orders': 'Orders',
      'messages': 'Messages',
      'profile': 'Profile',
      // 订单页面
      'search_order_hint': 'Search order number/address/customer',
      'refresh_order_data': 'Refresh order data',
      'no_orders': 'No orders',
      // Order Status Names
      'status_waiting': 'Waiting',
      'status_accepted': 'Accepted',
      'status_transporting': 'Transporting',
      'status_delivered': 'Delivered',
      'status_completed': 'Completed',
      'status_canceled': 'Canceled',
      // Order Status Descriptions
      'desc_status_waiting': 'Waiting for a driver to accept the order',
      'desc_status_accepted': 'Driver accepted, heading to pickup location',
      'desc_status_transporting': 'The cargo is being transported',
      'desc_status_delivered': 'The cargo has been delivered',
      'desc_status_completed': 'The order has been completed',
      'desc_status_canceled': 'The order has been canceled',
      'order_no': 'Order No',
      'order_list_desc': 'Your order list will be displayed here',
      'cancel_order': 'Cancel Order',
      'confirm_cancel': 'Confirm Cancel',
      'select_cancel_reason': 'Please select a cancel reason',
      'current_status': 'Current Status',
      'pay_now': 'Pay Now',
      // Message types
      'system_message': 'System Message',
      'activity_message': 'Activity Message',
      'order_message': 'Order Message',
      'system': 'System',
      'activity': 'Activity',
      'delete_order': 'Delete Order',
      'grab_now': 'Grab Now',
      'picked_up': 'Picked Up',
      'delivered_done': 'Delivered',
      // 个人资料页面
      'logout': 'Logout',
      'not_logged_in': 'Not logged in',
      'shipper': 'Shipper',
      'driver': 'Driver',
      'credit_score': 'Credit Score',
      'order_count': 'Orders',
      'role_switch': 'Role Switch',
      'current_role': 'Current Role',
      'switching_role': 'Switching role...',
      'role_switch_failed': 'Role switch failed',
      'account_management': 'Account Management',
      'real_name_verification': 'Real Name Verification',
      'verified': 'Verified',
      'not_verified': 'Not Verified',
      're_verification_confirm':
          'You are already verified. Do you want to re-verify?',
      're_verify': 'Re-verify',
      'wallet_management': 'Wallet Management',
      'my_wallet': 'My Wallet',
      'balance': 'Balance',
      'withdrawal_account': 'Withdrawal Account',
      'invoice_management': 'Invoice Management',
      'other_settings': 'Other Settings',
      'contact_customer_service': 'Contact Customer Service',

      // Message Center page
      'message_center': 'Message Center',
      'all': 'All',
      'mark_all_as_read': 'Mark All as Read',
      'delete_all': 'Delete All',
      'confirm_delete_all': 'Confirm Delete All Messages',
      'delete_all_message': 'This will delete all messages. Continue?',
      'close': 'Close',
      'order_status_change': 'Order Status Change',
      'mark_as_read': 'Mark as Read',
      'delete': 'Delete',
      // Added generic/message-center strings
      'no_messages': 'No messages',
      'message_detail': 'Message Detail',
      'delete_all_success': 'All messages deleted',
      'error_title': 'Error',
      'retry': 'Retry',
    },
    'km': {
      'settings': 'ការកំណត់',
      'general_settings': 'ការកំណត់ទូទៅ',
      'appearance_settings': 'ការកំណត់រូបរាង',
      'notification_settings': 'ការកំណត់ការជូនដំណឹង',
      'privacy_security': 'ភាពឯកជន និងសុវត្ថិភាព',
      // 消息类型
      'system_message': 'សារ​ប្រព័ន្ធ',
      'activity_message': 'សារសកម្មភាព',
      'order_message': 'សារកម្ម៉ង់',
      'system': 'ប្រព័ន្ធ',
      'activity': 'សកម្មភាព',
      'about_app': 'អំពីកម្មវិធី',
      'language': 'ភាសា',
      'language_settings': 'ការកំណត់ភាសា',
      'select_language': 'ជ្រើសរើសភាសា',
      'current': 'បច្ចុប្បន្ន',
      'chinese': '中文',
      'english': 'English',
      'khmer': 'ខ្មែរ',
      'notifications': 'ការជូនដំណឹង',
      'push_notifications': 'ការជូនដំណឹងរុញ',
      'push_notifications_desc':
          'ទទួលស្ថានភាពការបញ្ជាទិញ សារប្រព័ន្ធ និងការជូនដំណឹងផ្សេងៗ',
      'sound_alerts': 'ការជូនដំណឹងសំឡេង',
      'sound_alerts_desc': 'ចាក់សំឡេងជូនដំណឹងសម្រាប់សារថ្មី',

      'email_notifications': 'ការជូនដំណឹងអ៊ីមែល',
      'privacy': 'ភាពឯកជន',
      'privacy_policy': 'គោលការណ៍ភាពឯកជន',
      'privacy_policy_desc': 'មើលគោលការណ៍ការពារភាពឯកជន',
      'privacy_policy_content':
          'មាតិកាលម្អិតនៃគោលការណ៍ភាពឯកជននឹងត្រូវបានបង្ហាញនៅទីនេះ...',
      'user_agreement': 'កិច្ចព្រមព្រៀងអ្នកប្រើប្រាស់',
      'user_agreement_desc': 'មើលកិច្ចព្រមព្រៀងសេវាកម្មអ្នកប្រើប្រាស់',
      'user_agreement_content':
          'មាតិកាលម្អិតនៃកិច្ចព្រមព្រៀងអ្នកប្រើប្រាស់នឹងត្រូវបានបង្ហាញនៅទីនេះ...',
      // ឈ្មោះស្ថានភាពបញ្ជាទិញ
      'status_waiting': 'កំពុងរង់ចាំ',
      'status_accepted': 'បានទទួលការកម្ម៉ង់',
      'status_transporting': 'កំពុងដឹកជញ្ជូន',
      'status_delivered': 'បានដឹកជញ្ជូនដល់',
      'status_completed': 'បានបញ្ចប់',
      'status_canceled': 'បានបោះបង់',
      // សេចក្ដីពណ៌នាស្ថានភាព
      'desc_status_waiting': 'កំពុងរង់ចាំអ្នកបើកបរ​ទទួលការកម្ម៉ង់',
      'desc_status_accepted': 'អ្នកបើកបារបានទទួល កំពុងទៅកាន់ទីតាំងយកទំនិញ',
      'desc_status_transporting': 'ទំនិញកំពុងត្រូវបានដឹកជញ្ជូន',
      'desc_status_delivered': 'ទំនិញត្រូវបានដឹកជញ្ជូនដល់ទីតាំង',
      'desc_status_completed': 'ការកម្ម៉ង់ត្រូវបានបញ្ចប់',
      'desc_status_canceled': 'ការកម្ម៉ង់ត្រូវបានបោះបង់',

      'account_security': 'សុវត្ថិភាពគណនី',
      'account_security_desc': 'គ្រប់គ្រងពាក្យសម្ងាត់ ការភ្ជាប់ទូរស័ព្ទ ជាដើម',
      'account_security_developing':
          'មុខងារសុវត្ថិភាពគណនីកំពុងត្រូវបានអភិវឌ្ឍ...',
      'app_version': 'កំណែកម្មវិធី',
      'check_update': 'ពិនិត្យការអាប់ដេត',
      'latest_version': 'បច្ចុប្បន្នជាកំណែចុងក្រោយរួចហើយ',
      'rate_app': 'វាយតម្លៃកម្មវិធី',
      'rate_app_desc': 'វាយតម្លៃយើងនៅក្នុងហាងកម្មវិធី',
      'thank_you_support': 'សូមអរគុណចំពោះការគាំទ្រ!',
      'feedback': 'មតិកែលម្អ',
      'feedback_desc': 'រាយការណ៍បញ្ហា ឬសំណើរបស់អ្នកមកកាន់យើង',
      'feedback_hint': 'សូមបញ្ចូលមតិកែលម្អរបស់អ្នក...',
      'thank_you_feedback': 'សូមអរគុណចំពោះមតិកែលម្អរបស់អ្នក!',
      'submit': 'ដាក់ស្នើ',
      'data_usage': 'ការប្រើប្រាស់ទិន្នន័យ',
      'location_services': 'សេវាកម្មទីតាំង',
      'about': 'អំពី',
      'version': 'កំណែ',
      'terms_of_service': 'លក្ខខណ្ឌសេវាកម្ម',
      'cancel': 'បោះបង់',
      'confirm': 'បញ្ជាក់',
      'save': 'រក្សាទុក',
      // 登录页面
      'app_name': 'ដឹកជញ្ជូនទំនិញ',
      'app_description': 'វេទិកាដឹកជញ្ជូនវត្ថុធាតុដើមប្រកបដោយវិជ្ជាជីវៈ',
      'current_status': 'ស្ថានភាពបច្ចុប្បន្ន',
      'phone_hint': 'បញ្ចូលលេខទូរស័ព្ទ',
      'phone_required': 'សូមបញ្ចូលលេខទូរស័ព្ទ',
      'phone_invalid': 'សូមបញ្ចូលលេខទូរស័ព្ទត្រឹមត្រូវ',
      'nickname_hint': 'បញ្ចូលឈ្មោះហៅក្រៅ',
      'nickname_required': 'សូមបញ្ចូលឈ្មោះហៅក្រៅ',
      'nickname_too_short': 'ឈ្មោះហៅក្រៅត្រូវតែមានយ៉ាងតិច ២ តួអក្សរ',
      'password_hint': 'បញ្ចូលពាក្យសម្ងាត់',
      'password_hint_register': 'ពាក្យសម្ងាត់ត្រូវតែមានអក្សរ និងលេខ',
      'password_required': 'សូមបញ្ចូលពាក្យសម្ងាត់',
      'password_too_short': 'ពាក្យសម្ងាត់ត្រូវតែមានយ៉ាងតិច ៦ តួអក្សរ',
      'password_need_letter': 'ពាក្យសម្ងាត់ត្រូវតែមានអក្សរ',
      'password_need_number': 'ពាក្យសម្ងាត់ត្រូវតែមានលេខ',
      'remember_password': 'ចងចាំពាក្យសម្ងាត់',
      'forgot_password': 'ភ្លេចពាក្យសម្ងាត់?',
      'login': 'ចូល',
      'register': 'ចុះឈ្មោះ',
      'no_account': 'មិនទាន់មានគណនី?',
      'register_now': 'ចុះឈ្មោះឥឡូវនេះ',
      'back_to_login': 'ត្រលប់ទៅការចូល',
      'login_failed': 'ការចូលបានបរាជ័យ',
      'register_success': 'ការចុះឈ្មោះបានជោគជ័យ',
      'register_failed': 'ការចុះឈ្មោះបានបរាជ័យ',
      'phone_invalid_format': 'សូមបញ្ចូលលេខទូរស័ព្ទត្រឹមត្រូវ',
      'password_strength_requirement':
          'ពាក្យសម្ងាត់ត្រូវតែមានយ៉ាងតិច ៦ តួអក្សរ ជាមួយអក្សរ និងលេខ',
      'already_have_account': 'មានគណនីរួចហើយ?',
      // 主页面
      'long_distance_freight': 'ដឹកជញ្ជូនចម្ងាយឆ្ងាយ',
      'same_city_express': 'ដឹកជញ្ជូនក្នុងទីក្រុង',
      'loading_address': 'ផ្ទុក',
      'unloading_address': 'ចុះ',
      'select_loading_address': 'ជ្រើសរើសអាសយដ្ឋានផ្ទុក',
      'select_unloading_address': 'ជ្រើសរើសអាសយដ្ឋានចុះ',
      'driver_join': 'អ្នកបើកបរចូលរួម',
      'become_professional_fleet': 'ក្លាយជាក្រុមរថយន្តវិជ្ជាជីវៈ',
      'logistics_publish': 'បោះពុម្ពផ្សាយភស្តុភារ',
      'quick_publish_cargo': 'បោះពុម្ពផ្សាយទំនិញយ៉ាងរហ័ស',
      'call_car_process': 'ដំណើរការហៅរថយន្ត',
      'common_questions': 'សំណួរទូទៅ',
      'step_select_car_address': '១. ជ្រើសរថយន្ត និងអាសយដ្ឋាន',
      'step_fill_requirements': '២. បំពេញតម្រូវការ',
      'step_confirm_call': '៣. បញ្ជាក់ការហៅ',
      'hot_activities': 'សកម្មភាពពេញនិយម',
      'newbie_exclusive': 'ផ្តាច់មុខសម្រាប់អ្នកថ្មី',
      'recharge_discount': 'បញ្ចុះតម្លៃបញ្ចូលទឹកប្រាក់',
      // 导航栏
      'home': 'ទំព័រដើម',
      'orders': 'ការបញ្ជាទិញ',
      'messages': 'សារ',
      'profile': 'ប្រវត្តិរូប',
      // 订单页面
      'search_order_hint': 'ស្វែងរកលេខបញ្ជាទិញ/អាសយដ្ឋាន/អតិថិជន',
      'refresh_order_data': 'ធ្វើបច្ចុប្បន្នភាពទិន្នន័យបញ្ជាទិញ',
      'no_orders': 'គ្មានការបញ្ជាទិញ',
      'order_no': 'លេខបញ្ជាទិញ',
      'order_list_desc': 'បញ្ជីការបញ្ជាទិញរបស់អ្នកនឹងត្រូវបង្ហាញនៅទីនេះ',
      'cancel_order': 'បោះបង់ការបញ្ជាទិញ',
      'confirm_cancel': 'បញ្ជាក់ការបោះបង់',
      'select_cancel_reason': 'សូមជ្រើសរើសមូលហេតុបោះបង់',
      'pay_now': 'បង់ប្រាក់ឥឡូវ',
      'delete_order': 'លុបការបញ្ជាទិញ',
      'grab_now': 'យកការបញ្ជាទិញឥឡូវ',
      'picked_up': 'បានផ្ទុក',
      'delivered_done': 'បានដឹកជញ្ជូនដល់',
      // 个人资料页面
      'logout': 'ចាកចេញ',
      'not_logged_in': 'មិនបានចូល',
      'shipper': 'អ្នកដឹកជញ្ជូន',
      'driver': 'អ្នកបើកបរ',
      'credit_score': 'ពិន្ទុឥណទាន',
      'order_count': 'ចំនួនបញ្ជាទិញ',
      'role_switch': 'ប្តូរតួនាទី',
      'current_role': 'តួនាទីបច្ចុប្បន្ន',
      'switching_role': 'កំពុងប្តូរតួនាទី...',
      'role_switch_failed': 'ការប្តូរតួនាទីបរាជ័យ',
      'account_management': 'ការគ្រប់គ្រងគណនី',
      'real_name_verification': 'ការផ្ទៀងផ្ទាត់ឈ្មោះពិត',
      'verified': 'បានផ្ទៀងផ្ទាត់',
      'not_verified': 'មិនបានផ្ទៀងផ្ទាត់',
      're_verification_confirm':
          'អ្នកបានបញ្ចប់ការផ្ទៀងផ្ទាត់រួចហើយ។ តើអ្នកចង់ផ្ទៀងផ្ទាត់ម្តងទៀតទេ?',
      're_verify': 'ផ្ទៀងផ្ទាត់ម្តងទៀត',
      'wallet_management': 'ការគ្រប់គ្រងកាបូប',
      'my_wallet': 'កាបូបរបស់ខ្ញុំ',
      'balance': 'សមតុល្យ',
      'withdrawal_account': 'គណនីដកប្រាក់',
      'invoice_management': 'ការគ្រប់គ្រងវិក័យប័ត្រ',
      'other_settings': 'ការកំណត់ផ្សេងទៀត',
      'contact_customer_service': 'ទាក់ទងសេវាកម្មអតិថិជន',

      // ទំព័រមជ្ឈមណ្ឌលសារ
      'message_center': 'មជ្ឈមណ្ឌលសារ',
      'all': 'ទាំងអស់',
      'mark_all_as_read': 'សម្គាល់ទាំងអស់ថាបានអាន',
      'delete_all': 'លុបទាំងអស់',
      'confirm_delete_all': 'បញ្ជាក់ការលុបសារទាំងអស់',
      'delete_all_message': 'នេះនឹងលុបសារទាំងអស់។ បន្តទេ?',
      'close': 'បិទ',
      'order_status_change': 'ការផ្លាស់ប្តូរស្ថានភាពបញ្ជាទិញ',
      'mark_as_read': 'សម្គាល់ថាបានអាន',
      'delete': 'លុប',
      // បន្ថែមអត្ថបទទូទៅ/មជ្ឈមណ្ឌលសារ
      'no_messages': 'គ្មានសារ',
      'message_detail': 'ព័ត៌មានលម្អិត',
      'delete_all_success': 'បានលុបសារទាំងអស់',
      'error_title': 'មានកំហុស',
      'retry': 'សាកល្បងម្តងទៀត',
    },
  };

  // 获取本地化文本的方法
  String get settings =>
      _localizedValues[locale.languageCode]?['settings'] ?? 'Settings';
  String get generalSettings =>
      _localizedValues[locale.languageCode]?['general_settings'] ??
      'General Settings';
  String get appearanceSettings =>
      _localizedValues[locale.languageCode]?['appearance_settings'] ??
      'Appearance Settings';
  String get notificationSettings =>
      _localizedValues[locale.languageCode]?['notification_settings'] ??
      'Notification Settings';
  String get privacySecurity =>
      _localizedValues[locale.languageCode]?['privacy_security'] ??
      'Privacy & Security';
  String get aboutApp =>
      _localizedValues[locale.languageCode]?['about_app'] ?? 'About App';
  String get language =>
      _localizedValues[locale.languageCode]?['language'] ?? 'Language';
  String get languageSettings =>
      _localizedValues[locale.languageCode]?['language_settings'] ??
      'Language Settings';
  String get selectLanguage =>
      _localizedValues[locale.languageCode]?['select_language'] ??
      'Select Language';
  String get current =>
      _localizedValues[locale.languageCode]?['current'] ?? 'Current';
  String get chinese =>
      _localizedValues[locale.languageCode]?['chinese'] ?? '中文';
  String get english =>
      _localizedValues[locale.languageCode]?['english'] ?? 'English';
  String get khmer =>
      _localizedValues[locale.languageCode]?['khmer'] ?? 'ខ្មែរ';
  String get notifications =>
      _localizedValues[locale.languageCode]?['notifications'] ??
      'Notifications';
  String get pushNotifications =>
      _localizedValues[locale.languageCode]?['push_notifications'] ??
      'Push Notifications';
  String get pushNotificationsDesc =>
      _localizedValues[locale.languageCode]?['push_notifications_desc'] ??
      'Receive order status, system messages and other notifications';
  String get soundAlerts =>
      _localizedValues[locale.languageCode]?['sound_alerts'] ?? 'Sound Alerts';
  String get soundAlertsDesc =>
      _localizedValues[locale.languageCode]?['sound_alerts_desc'] ??
      'Play notification sound for new messages';

  String get emailNotifications =>
      _localizedValues[locale.languageCode]?['email_notifications'] ??
      'Email Notifications';
  String get privacy =>
      _localizedValues[locale.languageCode]?['privacy'] ?? 'Privacy';
  String get privacyPolicy =>
      _localizedValues[locale.languageCode]?['privacy_policy'] ??
      'Privacy Policy';
  String get privacyPolicyDesc =>
      _localizedValues[locale.languageCode]?['privacy_policy_desc'] ??
      'View privacy protection policy';
  String get privacyPolicyContent =>
      _localizedValues[locale.languageCode]?['privacy_policy_content'] ??
      'Detailed privacy policy content will be displayed here...';
  String get userAgreement =>
      _localizedValues[locale.languageCode]?['user_agreement'] ??
      'User Agreement';
  String get userAgreementDesc =>
      _localizedValues[locale.languageCode]?['user_agreement_desc'] ??
      'View user service agreement';
  String get userAgreementContent =>
      _localizedValues[locale.languageCode]?['user_agreement_content'] ??
      'Detailed user agreement content will be displayed here...';
  String get accountSecurity =>
      _localizedValues[locale.languageCode]?['account_security'] ??
      'Account Security';
  String get accountSecurityDesc =>
      _localizedValues[locale.languageCode]?['account_security_desc'] ??
      'Manage password, phone binding, etc.';
  String get accountSecurityDeveloping =>
      _localizedValues[locale.languageCode]?['account_security_developing'] ??
      'Account security feature is under development...';
  String get appVersion =>
      _localizedValues[locale.languageCode]?['app_version'] ?? 'App Version';
  String get checkUpdate =>
      _localizedValues[locale.languageCode]?['check_update'] ?? 'Check Update';
  String get latestVersion =>
      _localizedValues[locale.languageCode]?['latest_version'] ??
      'Already the latest version';
  String get rateApp =>
      _localizedValues[locale.languageCode]?['rate_app'] ?? 'Rate App';
  String get rateAppDesc =>
      _localizedValues[locale.languageCode]?['rate_app_desc'] ??
      'Rate us in the app store';
  String get thankYouSupport =>
      _localizedValues[locale.languageCode]?['thank_you_support'] ??
      'Thank you for your support!';
  String get feedback =>
      _localizedValues[locale.languageCode]?['feedback'] ?? 'Feedback';
  String get feedbackDesc =>
      _localizedValues[locale.languageCode]?['feedback_desc'] ??
      'Report issues or suggestions to us';
  String get feedbackHint =>
      _localizedValues[locale.languageCode]?['feedback_hint'] ??
      'Please enter your feedback...';
  String get thankYouFeedback =>
      _localizedValues[locale.languageCode]?['thank_you_feedback'] ??
      'Thank you for your feedback!';
  String get submit =>
      _localizedValues[locale.languageCode]?['submit'] ?? 'Submit';
  String get dataUsage =>
      _localizedValues[locale.languageCode]?['data_usage'] ?? 'Data Usage';
  String get locationServices =>
      _localizedValues[locale.languageCode]?['location_services'] ??
      'Location Services';
  String get about =>
      _localizedValues[locale.languageCode]?['about'] ?? 'About';
  String get version =>
      _localizedValues[locale.languageCode]?['version'] ?? 'Version';
  String get termsOfService =>
      _localizedValues[locale.languageCode]?['terms_of_service'] ??
      'Terms of Service';
  String get cancel =>
      _localizedValues[locale.languageCode]?['cancel'] ?? 'Cancel';
  String get confirm =>
      _localizedValues[locale.languageCode]?['confirm'] ?? 'Confirm';
  String get save => _localizedValues[locale.languageCode]?['save'] ?? 'Save';

  // 登录页面相关
  String get appName =>
      _localizedValues[locale.languageCode]?['app_name'] ?? 'Freight Express';
  String get appDescription =>
      _localizedValues[locale.languageCode]?['app_description'] ??
      'Professional logistics transport platform';
  String get phoneHint =>
      _localizedValues[locale.languageCode]?['phone_hint'] ??
      'Enter phone number';
  String get phoneRequired =>
      _localizedValues[locale.languageCode]?['phone_required'] ??
      'Please enter phone number';
  String get phoneInvalid =>
      _localizedValues[locale.languageCode]?['phone_invalid'] ??
      'Please enter a valid phone number';
  String get nicknameHint =>
      _localizedValues[locale.languageCode]?['nickname_hint'] ??
      'Enter nickname';
  String get nicknameRequired =>
      _localizedValues[locale.languageCode]?['nickname_required'] ??
      'Please enter nickname';
  String get nicknameTooShort =>
      _localizedValues[locale.languageCode]?['nickname_too_short'] ??
      'Nickname must be at least 2 characters';
  String get passwordHint =>
      _localizedValues[locale.languageCode]?['password_hint'] ??
      'Enter password';
  String get passwordHintRegister =>
      _localizedValues[locale.languageCode]?['password_hint_register'] ??
      'Password must contain letters and numbers';
  String get passwordRequired =>
      _localizedValues[locale.languageCode]?['password_required'] ??
      'Please enter password';
  String get passwordTooShort =>
      _localizedValues[locale.languageCode]?['password_too_short'] ??
      'Password must be at least 6 characters';
  String get passwordNeedLetter =>
      _localizedValues[locale.languageCode]?['password_need_letter'] ??
      'Password must contain letters';
  String get passwordNeedNumber =>
      _localizedValues[locale.languageCode]?['password_need_number'] ??
      'Password must contain numbers';
  String get rememberPassword =>
      _localizedValues[locale.languageCode]?['remember_password'] ??
      'Remember password';
  String get forgotPassword =>
      _localizedValues[locale.languageCode]?['forgot_password'] ??
      'Forgot password?';
  String get login =>
      _localizedValues[locale.languageCode]?['login'] ?? 'Login';
  String get register =>
      _localizedValues[locale.languageCode]?['register'] ?? 'Register';
  String get noAccount =>
      _localizedValues[locale.languageCode]?['no_account'] ??
      'Don\'t have an account?';
  String get registerNow =>
      _localizedValues[locale.languageCode]?['register_now'] ?? 'Register now';
  String get backToLogin =>
      _localizedValues[locale.languageCode]?['back_to_login'] ??
      'Back to login';
  String get loginFailed =>
      _localizedValues[locale.languageCode]?['login_failed'] ?? 'Login failed';
  String get registerSuccess =>
      _localizedValues[locale.languageCode]?['register_success'] ??
      'Registration successful';
  String get registerFailed =>
      _localizedValues[locale.languageCode]?['register_failed'] ??
      'Registration failed';
  String get phoneInvalidFormat =>
      _localizedValues[locale.languageCode]?['phone_invalid_format'] ??
      'Please enter a valid phone number';
  String get passwordStrengthRequirement =>
      _localizedValues[locale.languageCode]?['password_strength_requirement'] ??
      'Password must be at least 6 characters with letters and numbers';
  String get alreadyHaveAccount =>
      _localizedValues[locale.languageCode]?['already_have_account'] ??
      'Already have an account?';

  // 主页面相关
  String get longDistanceFreight =>
      _localizedValues[locale.languageCode]?['long_distance_freight'] ??
      'Long Distance Freight';
  String get sameCityExpress =>
      _localizedValues[locale.languageCode]?['same_city_express'] ??
      'Same City Express';
  String get loadingAddress =>
      _localizedValues[locale.languageCode]?['loading_address'] ?? 'Load';
  String get unloadingAddress =>
      _localizedValues[locale.languageCode]?['unloading_address'] ?? 'Unload';
  String get selectLoadingAddress =>
      _localizedValues[locale.languageCode]?['select_loading_address'] ??
      'Select Loading Address';
  String get selectUnloadingAddress =>
      _localizedValues[locale.languageCode]?['select_unloading_address'] ??
      'Select Unloading Address';
  String get driverJoin =>
      _localizedValues[locale.languageCode]?['driver_join'] ?? 'Driver Join';
  String get becomeProfessionalFleet =>
      _localizedValues[locale.languageCode]?['become_professional_fleet'] ??
      'Become Professional Fleet';
  String get logisticsPublish =>
      _localizedValues[locale.languageCode]?['logistics_publish'] ??
      'Logistics Publish';
  // Order status localized names
  String get statusWaiting =>
      _localizedValues[locale.languageCode]?['status_waiting'] ?? 'Waiting';
  String get statusAccepted =>
      _localizedValues[locale.languageCode]?['status_accepted'] ?? 'Accepted';
  String get statusTransporting =>
      _localizedValues[locale.languageCode]?['status_transporting'] ??
      'Transporting';
  String get statusDelivered =>
      _localizedValues[locale.languageCode]?['status_delivered'] ?? 'Delivered';
  String get statusCompleted =>
      _localizedValues[locale.languageCode]?['status_completed'] ?? 'Completed';
  String get statusCanceled =>
      _localizedValues[locale.languageCode]?['status_canceled'] ?? 'Canceled';

  // Order status localized descriptions
  String get descStatusWaiting =>
      _localizedValues[locale.languageCode]?['desc_status_waiting'] ??
      'Waiting for a driver to accept the order';
  String get descStatusAccepted =>
      _localizedValues[locale.languageCode]?['desc_status_accepted'] ??
      'Driver accepted, heading to pickup location';
  String get descStatusTransporting =>
      _localizedValues[locale.languageCode]?['desc_status_transporting'] ??
      'The cargo is being transported';
  String get descStatusDelivered =>
      _localizedValues[locale.languageCode]?['desc_status_delivered'] ??
      'The cargo has been delivered';
  String get descStatusCompleted =>
      _localizedValues[locale.languageCode]?['desc_status_completed'] ??
      'The order has been completed';
  String get descStatusCanceled =>
      _localizedValues[locale.languageCode]?['desc_status_canceled'] ??
      'The order has been canceled';
  String get quickPublishCargo =>
      _localizedValues[locale.languageCode]?['quick_publish_cargo'] ??
      'Quick Publish Cargo';
  String get callCarProcess =>
      _localizedValues[locale.languageCode]?['call_car_process'] ??
      'Call Car Process';
  String get commonQuestions =>
      _localizedValues[locale.languageCode]?['common_questions'] ??
      'Common Questions';
  String get stepSelectCarAddress =>
      _localizedValues[locale.languageCode]?['step_select_car_address'] ??
      '1. Select Car & Address';
  String get stepFillRequirements =>
      _localizedValues[locale.languageCode]?['step_fill_requirements'] ??
      '2. Fill Requirements';
  String get stepConfirmCall =>
      _localizedValues[locale.languageCode]?['step_confirm_call'] ??
      '3. Confirm Call';
  String get hotActivities =>
      _localizedValues[locale.languageCode]?['hot_activities'] ??
      'Hot Activities';
  String get newbieExclusive =>
      _localizedValues[locale.languageCode]?['newbie_exclusive'] ??
      'Newbie Exclusive';
  String get rechargeDiscount =>
      _localizedValues[locale.languageCode]?['recharge_discount'] ??
      'Recharge Discount';

  // 导航栏相关
  String get home => _localizedValues[locale.languageCode]?['home'] ?? 'Home';
  String get orders =>
      _localizedValues[locale.languageCode]?['orders'] ?? 'Orders';
  String get messages =>
      _localizedValues[locale.languageCode]?['messages'] ?? 'Messages';
  String get profile =>
      _localizedValues[locale.languageCode]?['profile'] ?? 'Profile';
  String get orderNo =>
      _localizedValues[locale.languageCode]?['order_no'] ?? 'Order No';
  String get orderListDesc =>
      _localizedValues[locale.languageCode]?['order_list_desc'] ??
      'Your order list will be displayed here';
  String get currentStatus =>
      _localizedValues[locale.languageCode]?['current_status'] ??
      'Current Status';
  String get cancelOrder =>
      _localizedValues[locale.languageCode]?['cancel_order'] ?? 'Cancel Order';
  String get confirmCancel =>
      _localizedValues[locale.languageCode]?['confirm_cancel'] ??
      'Confirm Cancel';
  String get selectCancelReason =>
      _localizedValues[locale.languageCode]?['select_cancel_reason'] ??
      'Please select a cancel reason';
  String get payNow =>
      _localizedValues[locale.languageCode]?['pay_now'] ?? 'Pay Now';
  String get deleteOrder =>
      _localizedValues[locale.languageCode]?['delete_order'] ?? 'Delete Order';
  String get grabNow =>
      _localizedValues[locale.languageCode]?['grab_now'] ?? 'Grab Now';
  String get pickedUp =>
      _localizedValues[locale.languageCode]?['picked_up'] ?? 'Picked Up';
  String get deliveredDone =>
      _localizedValues[locale.languageCode]?['delivered_done'] ?? 'Delivered';

  // 订单页面相关
  String get searchOrderHint =>
      _localizedValues[locale.languageCode]?['search_order_hint'] ??
      'Search order number/address/customer';
  String get refreshOrderData =>
      _localizedValues[locale.languageCode]?['refresh_order_data'] ??
      'Refresh order data';
  String get noOrders =>
      _localizedValues[locale.languageCode]?['no_orders'] ?? 'No orders';

  // 个人资料页面相关
  String get logout =>
      _localizedValues[locale.languageCode]?['logout'] ?? 'Logout';
  String get notLoggedIn =>
      _localizedValues[locale.languageCode]?['not_logged_in'] ??
      'Not logged in';
  String get shipper =>
      _localizedValues[locale.languageCode]?['shipper'] ?? 'Shipper';
  String get driver =>
      _localizedValues[locale.languageCode]?['driver'] ?? 'Driver';
  String get creditScore =>
      _localizedValues[locale.languageCode]?['credit_score'] ?? 'Credit Score';
  String get orderCount =>
      _localizedValues[locale.languageCode]?['order_count'] ?? 'Orders';
  String get roleSwitch =>
      _localizedValues[locale.languageCode]?['role_switch'] ?? 'Role Switch';
  String get currentRole =>
      _localizedValues[locale.languageCode]?['current_role'] ?? 'Current Role';
  String get switchingRole =>
      _localizedValues[locale.languageCode]?['switching_role'] ??
      'Switching role...';
  String get roleSwitchFailed =>
      _localizedValues[locale.languageCode]?['role_switch_failed'] ??
      'Role switch failed';
  String get accountManagement =>
      _localizedValues[locale.languageCode]?['account_management'] ??
      'Account Management';
  String get realNameVerification =>
      _localizedValues[locale.languageCode]?['real_name_verification'] ??
      'Real Name Verification';
  String get verified =>
      _localizedValues[locale.languageCode]?['verified'] ?? 'Verified';
  String get notVerified =>
      _localizedValues[locale.languageCode]?['not_verified'] ?? 'Not Verified';
  String get reVerificationConfirm =>
      _localizedValues[locale.languageCode]?['re_verification_confirm'] ??
      'You are already verified. Do you want to re-verify?';
  String get reVerify =>
      _localizedValues[locale.languageCode]?['re_verify'] ?? 'Re-verify';
  String get walletManagement =>
      _localizedValues[locale.languageCode]?['wallet_management'] ??
      'Wallet Management';
  String get myWallet =>
      _localizedValues[locale.languageCode]?['my_wallet'] ?? 'My Wallet';
  String get balance =>
      _localizedValues[locale.languageCode]?['balance'] ?? 'Balance';
  String get withdrawalAccount =>
      _localizedValues[locale.languageCode]?['withdrawal_account'] ??
      'Withdrawal Account';
  String get invoiceManagement =>
      _localizedValues[locale.languageCode]?['invoice_management'] ??
      'Invoice Management';
  String get otherSettings =>
      _localizedValues[locale.languageCode]?['other_settings'] ??
      'Other Settings';
  String get contactCustomerService =>
      _localizedValues[locale.languageCode]?['contact_customer_service'] ??
      'Contact Customer Service';

  // 消息中心页面
  String get messageCenter =>
      _localizedValues[locale.languageCode]?['message_center'] ??
      'Message Center';
  String get all => _localizedValues[locale.languageCode]?['all'] ?? 'All';
  String get markAllAsRead =>
      _localizedValues[locale.languageCode]?['mark_all_as_read'] ??
      'Mark All as Read';
  // Generic/message center additions
  String get noMessages =>
      _localizedValues[locale.languageCode]?['no_messages'] ?? 'No messages';
  String get messageDetail =>
      _localizedValues[locale.languageCode]?['message_detail'] ??
      'Message Detail';

  // 消息类型
  String get systemMessage =>
      _localizedValues[locale.languageCode]?['system_message'] ??
      'System Message';
  String get activityMessage =>
      _localizedValues[locale.languageCode]?['activity_message'] ??
      'Activity Message';
  String get orderMessage =>
      _localizedValues[locale.languageCode]?['order_message'] ??
      'Order Message';
  String get system =>
      _localizedValues[locale.languageCode]?['system'] ?? 'System';
  String get activity =>
      _localizedValues[locale.languageCode]?['activity'] ?? 'Activity';
  String get deleteAllSuccess =>
      _localizedValues[locale.languageCode]?['delete_all_success'] ??
      'All messages deleted';
  String get errorTitle =>
      _localizedValues[locale.languageCode]?['error_title'] ?? 'Error';
  String get retry =>
      _localizedValues[locale.languageCode]?['retry'] ?? 'Retry';
  String get deleteAll =>
      _localizedValues[locale.languageCode]?['delete_all'] ?? 'Delete All';
  String get confirmDeleteAll =>
      _localizedValues[locale.languageCode]?['confirm_delete_all'] ??
      'Confirm Delete All Messages';
  String get deleteAllMessage =>
      _localizedValues[locale.languageCode]?['delete_all_message'] ??
      'This will delete all messages. Continue?';
  String get close =>
      _localizedValues[locale.languageCode]?['close'] ?? 'Close';
  String get orderStatusChange =>
      _localizedValues[locale.languageCode]?['order_status_change'] ??
      'Order Status Change';
  String get markAsRead =>
      _localizedValues[locale.languageCode]?['mark_as_read'] ?? 'Mark as Read';
  String get delete =>
      _localizedValues[locale.languageCode]?['delete'] ?? 'Delete';
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => AppLocalizations.isSupported(locale);

  @override
  Future<AppLocalizations> load(Locale locale) async {
    return AppLocalizations(locale);
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}
