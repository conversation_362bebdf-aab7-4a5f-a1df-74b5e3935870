import 'package:flutter/material.dart';
import 'package:hyt/widgets/app_dialog.dart';

/// 统一确认对话框：返回 true(确定) 或 false(取消)
Future<bool> showConfirmDialog(
  BuildContext context, {
  required String title,
  required String content,
  String cancelText = '取消',
  String okText = '确定',
}) async {
  final result = await AppDialog.showConfirm(
    context: context,
    title: title,
    content: content,
    cancelText: cancelText,
    confirmText: okText,
  );
  return result ?? false;
}
