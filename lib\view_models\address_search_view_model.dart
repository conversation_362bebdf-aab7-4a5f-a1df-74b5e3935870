import 'package:flutter/material.dart';
import 'package:flutter_google_places_sdk/flutter_google_places_sdk.dart';
import 'package:hyt/view_models/address_view_model.dart';
import 'package:hyt/view_models/base_view_model.dart';
import 'package:hyt/utils/log_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';

/// 地址搜索页面的ViewModel，负责处理地址搜索和历史记录管理
class AddressSearchViewModel extends BaseViewModel {
  final AddressViewModel addressViewModel;
  AddressSearchViewModel(this.addressViewModel);
  // Google Places API 客户端
  late final places =
      FlutterGooglePlacesSdk('AIzaSyAPJDYo_fa7EaDXrh5p1654O3t5WgrSmHs');

  // 自动完成建议
  List<AutocompletePrediction> _autocompleteSuggestions = [];
  List<AutocompletePrediction> get autocompleteSuggestions =>
      _autocompleteSuggestions;

  // 自动完成防抖定时器
  Timer? _autocompleteDebounce;
  bool _isSearching = false;
  bool get isSearching => _isSearching;
  set isSearching(bool value) {
    if (_isSearching != value) {
      _isSearching = value;
      notifyListeners();
    }
  }

  // 搜索控制器
  final TextEditingController searchController = TextEditingController();

  // 搜索结果
  List<AutocompletePrediction> _searchResults = [];
  List<AutocompletePrediction> get searchResults => _searchResults;
  //选择的地址
  final Map<String, String> _selectedAddress = {};
  Map<String, String> get selectedAddress => _selectedAddress;

  // 搜索防抖定时器
  Timer? _debounce;

  // 地址历史记录
  List<Map<String, String>> _addressHistory = [];
  List<Map<String, String>> get addressHistory => _addressHistory;

  get initialPosition => null;

  /// 搜索地址
  Future<void> searchPlaces(String query) async {
    isSearching = true;
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    // 自动完成建议
    _getAutocompleteSuggestions(query);

    _debounce = Timer(const Duration(milliseconds: 800), () async {
      try {
        LogUtils.d('防抖定时器触发，准备搜索: $query');
        if (query.isEmpty) {
          LogUtils.d('查询为空，清空搜索结果');
          _searchResults = [];
          notifyListeners();
          return;
        }
        // 使用模拟数据替代真实API调用
        LogUtils.d('使用模拟数据替代真实API调用');
        _searchResults = _generateMockPredictions(query);
        LogUtils.d('模拟结果数量: ${_searchResults.length}');
        LogUtils.d('模拟结果: $_searchResults');
        notifyListeners();
      } catch (e, st) {
        LogUtils.e('搜索地址时出错: $e', e, st);
        _searchResults = [];
        notifyListeners();
        rethrow;
      } finally {
        isSearching = false;
        notifyListeners();
      }
    });
  }

  /// 生成模拟预测数据
  List<AutocompletePrediction> _generateMockPredictions(String query) {
    final mockAddresses = [
      {
        'primaryText': '北京$query',
        'fullText': '北京市朝阳区$query模拟地址',
        'placeId': 'mock1'
      },
      {
        'primaryText': '浦东$query',
        'fullText': '上海市浦东新区$query模拟地址',
        'placeId': 'mock2'
      },
      {
        'primaryText': '广州$query',
        'fullText': '广州市白云区$query模拟地址',
        'placeId': 'mock3'
      },
      {
        'primaryText': '深圳$query',
        'fullText': '深圳市南山区$query模拟地址',
        'placeId': 'mock4'
      },
      {
        'primaryText': '成都$query',
        'fullText': '成都市武侯区$query模拟地址',
        'placeId': 'mock5'
      },
    ];

    return mockAddresses
        .map((address) => AutocompletePrediction(
              placeId: address['placeId']!,
              distanceMeters: null,
              primaryText: address['primaryText']!,
              secondaryText: '',
              fullText: address['fullText']!,
            ))
        .toList();
  }

  /// 获取自动完成建议
  Future<void> _getAutocompleteSuggestions(String query) async {
    if (_autocompleteDebounce?.isActive ?? false) {
      _autocompleteDebounce!.cancel();
    }

    _autocompleteDebounce = Timer(const Duration(milliseconds: 300), () async {
      try {
        if (query.isEmpty) {
          _autocompleteSuggestions = [];
          notifyListeners();
          return;
        }

        // 使用模拟数据替代真实API调用
        LogUtils.d('使用模拟数据获取自动完成建议');
        _autocompleteSuggestions =
            _generateMockPredictions(query).take(5).toList();
        notifyListeners();
      } catch (e, st) {
        LogUtils.e('获取自动完成建议时出错: $e', e, st);
        _autocompleteSuggestions = [];
        notifyListeners();
        rethrow;
      }
    });
  }

  /// 处理地点选择
  Future<void> onPlaceSelected(
      AutocompletePrediction place, bool isLoading) async {
    try {
      // final details = await places.fetchPlace(
      //   place.placeId,
      //   fields: [PlaceField.AddressComponents, PlaceField.Location],
      // );
      // final latLng = details.place?.latLng;
      //暂时使用模拟数据
      LogUtils.d('使用模拟数据获取地点详情');
      LogUtils.d(place.primaryText);
      final latLng = LatLng(lat: 30.274084, lng: 120.155070); // 杭州默认坐标
      final address = {
        'name': place.primaryText,
        'address': place.fullText,
        'lat': latLng.lat.toString(),
        'lng': latLng.lng.toString(),
      };
      // 添加到历史记录
      await addToHistory(address);
      addressViewModel.setSelectedAddress(address: address);

      LogUtils.d("addressViewModel: ${addressViewModel.selectedAddress}");
    } catch (e, st) {
      LogUtils.e('获取地点详情时出错: $e', e, st);
    }
  }

  /// 处理历史记录项选择
  void onHistoryItemSelected(Map<String, String> address) {
    //更新address_view_model
    addressViewModel.setSelectedAddress(address: address);
  }

  /// 添加地址到历史记录
  Future<void> addToHistory(Map<String, String> address) async {
    // 检查是否已存在相同地址
    final exists = _addressHistory.any((item) =>
        item['name'] == address['name'] &&
        item['address'] == address['address']);

    if (!exists) {
      // 如果不存在，添加到历史记录列表开头
      _addressHistory.insert(0, address);

      // 限制历史记录数量为10条
      if (_addressHistory.length > 10) {
        _addressHistory = _addressHistory.sublist(0, 10);
      }
      // 保存到本地存储
      await saveAddressHistory();
      notifyListeners();
    } else {
      // 如果已存在，将其移到列表开头
      _addressHistory.removeWhere((item) =>
          item['name'] == address['name'] &&
          item['address'] == address['address']);
      _addressHistory.insert(0, address);

      // 保存到本地存储
      await saveAddressHistory();
      notifyListeners();
    }
  }

  /// 从历史记录中删除地址
  Future<void> removeFromHistory(Map<String, String> address) async {
    _addressHistory.removeWhere((item) =>
        item['name'] == address['name'] &&
        item['address'] == address['address']);

    // 保存到本地存储
    await saveAddressHistory();
    notifyListeners();
  }

  /// 清空历史记录
  Future<void> clearHistory() async {
    _addressHistory.clear();

    // 保存到本地存储
    await saveAddressHistory();
    notifyListeners();
  }

  /// 保存历史记录到本地存储
  Future<void> saveAddressHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = jsonEncode(_addressHistory);
      await prefs.setString('address_search_history', historyJson);
    } catch (e, st) {
      LogUtils.e('保存地址历史记录失败: $e', e, st);
    }
  }

  /// 从本地存储加载历史记录
  Future<void> loadAddressHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('address_search_history');

      if (historyJson != null && historyJson.isNotEmpty) {
        final List<dynamic> decoded = jsonDecode(historyJson);
        _addressHistory =
            decoded.map((item) => Map<String, String>.from(item)).toList();
        notifyListeners();
      }
    } catch (e, st) {
      LogUtils.e('加载地址历史记录失败: $e', e, st);
    }
  }

  /// 从历史记录中删除指定索引的地址
  Future<void> removeHistoryItem(int index) async {
    if (index >= 0 && index < _addressHistory.length) {
      _addressHistory.removeAt(index);
      // 保存到本地存储
      await saveAddressHistory();
      notifyListeners();
    }
  }

  //选择搜索结果并返回result
  LatLng selectSearchResult(AutocompletePrediction result) {
    // 模拟返回一个位置信息
    final mockPosition = LatLng(lat: 30.274084, lng: 120.155070); // 杭州默认坐标

    // 如果有结果，添加到历史记录
    final selectedAddress = {
      'name': result.primaryText,
      'address': result.fullText,
      'lat': mockPosition.lat.toString(),
      'lng': mockPosition.lng.toString(),
    };

    // 添加到历史记录
    addToHistory(selectedAddress);

    return mockPosition;
  }

  void clearSearchResults() {
    _searchResults.clear();
    notifyListeners();
  }
}
