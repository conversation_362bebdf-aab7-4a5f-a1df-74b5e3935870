---
description: 
globs: 
alwaysApply: false
---
# 需求文档生成规则

## 文档格式

根据用户输入，生成的需求文档应包含以下部分：

1. **项目概述**
   - 项目背景
   - 项目目标
   - 目标用户

2. **功能需求**
   - 核心功能列表
   - 功能详细描述
   - 用户流程

3. **非功能需求**
   - 性能要求
   - 安全要求
   - 可用性要求

4. **技术规格**
   - 技术栈
   - 系统架构
   - 第三方集成

5. **交付物**
   - 里程碑
   - 时间线
   - 验收标准

## 标准模板

用户提供信息时，请按照以下结构生成文档：

```markdown
# 项目名称：[项目名]

## 1. 项目概述
### 1.1 项目背景
[根据用户输入填写]

### 1.2 项目目标
[根据用户输入填写]

### 1.3 目标用户
[根据用户输入填写]

## 2. 功能需求
### 2.1 核心功能
- [功能1]
- [功能2]
- [功能3]

### 2.2 功能详细描述
#### 2.2.1 [功能1]
[详细描述]

#### 2.2.2 [功能2]
[详细描述]

### 2.3 用户流程
[描述用户如何使用系统完成主要任务]

## 3. 非功能需求
### 3.1 性能要求
[响应时间、并发用户数等]

### 3.2 安全要求
[数据安全、身份验证等]

### 3.3 可用性要求
[易用性、可访问性等]

## 4. 技术规格
### 4.1 技术栈
[前端、后端、数据库等]

### 4.2 系统架构
[系统组件和交互]

### 4.3 第三方集成
[需要集成的外部系统或API]

## 5. 交付：
### 5.1 里程碑
[主要项目阶段]

### 5.2 时间线
[各阶段的预计完成时间]

### 5.3 验收标准
[项目成功的衡量标准]