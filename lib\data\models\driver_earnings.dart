import 'package:hyt/utils/num_utils.dart';

class DriverEarnings {
  final double todayEarnings; // 今日收入
  final double weekEarnings; // 本周收入
  final double monthEarnings; // 本月收入
  final double totalEarnings; // 总收入
  final int completedOrders; // 已完成订单数
  final DateTime lastUpdated; // 最后更新时间

  DriverEarnings({
    required this.todayEarnings,
    required this.weekEarnings,
    required this.monthEarnings,
    required this.totalEarnings,
    required this.completedOrders,
    required this.lastUpdated,
  });

  // 从Map创建对象
  factory DriverEarnings.fromMap(Map<String, dynamic> map) {
    return DriverEarnings(
      todayEarnings: safeToDouble(map['today_earnings']),
      weekEarnings: safeToDouble(map['week_earnings']),
      monthEarnings: safeToDouble(map['month_earnings']),
      totalEarnings: safeToDouble(map['total_earnings']),
      completedOrders: map['completed_orders'] ?? 0,
      lastUpdated: map['last_updated'] != null
          ? DateTime.parse(map['last_updated'])
          : DateTime.now(),
    );
  }

  // 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'today_earnings': todayEarnings,
      'week_earnings': weekEarnings,
      'month_earnings': monthEarnings,
      'total_earnings': totalEarnings,
      'completed_orders': completedOrders,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  // 创建空对象
  factory DriverEarnings.empty() {
    return DriverEarnings(
      todayEarnings: 0.0,
      weekEarnings: 0.0,
      monthEarnings: 0.0,
      totalEarnings: 0.0,
      completedOrders: 0,
      lastUpdated: DateTime.now(),
    );
  }
}
