// 取消订单对话框
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/data/models/order.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/utils/cancel_order_utils.dart';
import 'package:hyt/view_models/order_view_model.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:provider/provider.dart';
import 'package:hyt/widgets/unified_button.dart';
import 'package:hyt/l10n/app_localizations.dart';

class CancelOrderDialog extends StatefulWidget {
  final Order order;

  const CancelOrderDialog({
    super.key,
    required this.order,
  });

  @override
  State<CancelOrderDialog> createState() => _CancelOrderDialogState();
}

class _CancelOrderDialogState extends State<CancelOrderDialog> {
  String? _selectedReason;
  final TextEditingController _customReasonController = TextEditingController();
  bool _isCustomReason = false;

  @override
  void dispose() {
    _customReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<OrderViewModel, AppUserViewModel>(
      builder: (context, orderViewModel, userViewModel, child) {
        final userId = userViewModel.currentUser?.id ?? '';
        final userRole = userViewModel.currentRole;

        // 检查是否可以取消
        final canCancel = orderViewModel.canCancelOrder(
            widget.order.orderId, userId, userRole);
        if (!canCancel) {
          return AlertDialog(
            title: const Text('无法取消'),
            content: const Text('当前订单状态不允许取消'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('确定'),
              ),
            ],
          );
        }

        // 获取费用信息
        final fee =
            orderViewModel.calculateCancelFee(widget.order.orderId, userRole);
        final feeDescription = orderViewModel.getCancelFeeDescription(
            widget.order.orderId, userRole);
        final reasons = CancelOrderUtils.getCancelReasons(userRole);

        return AlertDialog(
          title: Text(AppLocalizations.of(context)?.cancelOrder ?? '取消订单',
              style: AppTextStyles.headline4),
          contentPadding: EdgeInsets.fromLTRB(24.w, 20.h, 24.w, 0),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 订单信息
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                          '${AppLocalizations.of(context)?.orderNo ?? '订单号'}：${widget.order.orderId}',
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w500,
                          )),
                      SizedBox(height: 12.h),
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.w, vertical: 8.h),
                        decoration: BoxDecoration(
                          color: fee > 0
                              ? Theme.of(context)
                                  .colorScheme
                                  .error
                                  .withValues(alpha: 0.06)
                              : Theme.of(context)
                                  .colorScheme
                                  .secondary
                                  .withValues(alpha: 0.06),
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          feeDescription,
                          style: TextStyle(
                            fontSize: 13.sp,
                            color: fee > 0
                                ? Theme.of(context).colorScheme.error
                                : Theme.of(context).colorScheme.secondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 20.h),

                // 取消原因选择
                Text(
                    AppLocalizations.of(context)?.selectCancelReason ??
                        '请选择取消原因：',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w500,
                    )),
                SizedBox(height: 12.h),

                ...reasons.map((reason) => Container(
                      margin: EdgeInsets.only(bottom: 8.h),
                      child: InkWell(
                        onTap: () {
                          setState(() {
                            _selectedReason = reason;
                            _isCustomReason = reason == '其他原因';
                          });
                        },
                        borderRadius: BorderRadius.circular(8.r),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 12.w, vertical: 8.h),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: _selectedReason == reason
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey.shade300,
                              width: _selectedReason == reason ? 2 : 1,
                            ),
                            borderRadius: BorderRadius.circular(8.r),
                            color: _selectedReason == reason
                                ? Theme.of(context)
                                    .primaryColor
                                    .withValues(alpha: 0.05)
                                : Colors.transparent,
                          ),
                          child: Row(
                            children: [
                              Radio<String>(
                                value: reason,
                                groupValue: _selectedReason,
                                onChanged: (value) {
                                  setState(() {
                                    _selectedReason = value;
                                    _isCustomReason = reason == '其他原因';
                                  });
                                },
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                                visualDensity: VisualDensity.compact,
                              ),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Text(
                                  reason,
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: _selectedReason == reason
                                        ? Theme.of(context).primaryColor
                                        : Colors.grey.shade700,
                                    fontWeight: _selectedReason == reason
                                        ? FontWeight.w500
                                        : FontWeight.normal,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    )),

                // 自定义原因输入
                if (_isCustomReason) ...[
                  SizedBox(height: 16.h),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: TextField(
                      controller: _customReasonController,
                      decoration: InputDecoration(
                        hintText: '请详细说明取消原因...',
                        hintStyle: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 14.sp,
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.all(16.w),
                      ),
                      maxLines: 3,
                      style: TextStyle(fontSize: 14.sp),
                    ),
                  ),
                ],
              ],
            ),
          ),
          actionsPadding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 20.h),
          actions: [
            // 返回按钮 - 使用次要样式（OutlinedButton）
            UnifiedButton.outlined(
              text: AppLocalizations.of(context)?.cancel ?? '返回',
              onPressed: () => Navigator.of(context).pop(),
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(width: 12.w),
            // 确认取消按钮 - 使用主要样式（ElevatedButton），红色警告色
            Selector<OrderViewModel, bool>(
              selector: (_, vm) => vm.isButtonLoading,
              builder: (context, isLoading, child) {
                return UnifiedButton.elevated(
                  text: AppLocalizations.of(context)?.confirmCancel ?? '确认取消',
                  onPressed: isLoading ? null : () => _confirmCancel(context),
                  isLoading: isLoading,
                  isDestructive: true,
                );
              },
            ),
          ],
        );
      },
    );
  }

  void _confirmCancel(BuildContext context) async {
    if (_selectedReason == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择取消原因')),
      );
      return;
    }

    String finalReason = _selectedReason!;
    if (_isCustomReason && _customReasonController.text.trim().isNotEmpty) {
      finalReason = _customReasonController.text.trim();
    }

    final orderViewModel = context.read<OrderViewModel>();
    final userViewModel = context.read<AppUserViewModel>();
    final userId = userViewModel.currentUser?.id ?? '';
    final userRole = userViewModel.currentRole;

    final success = await orderViewModel.cancelOrderSimple(
      widget.order.orderId,
      userId,
      userRole,
      finalReason,
    );

    if (!context.mounted) return;

    if (success) {
      Navigator.of(context).pop(true); // 返回true表示取消成功
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('订单取消成功')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(orderViewModel.message ?? '取消失败')),
      );
    }
  }
}
