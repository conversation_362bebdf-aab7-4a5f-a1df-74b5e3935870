//用户中心页面，根据货主和司机不同角色显示不同的组件

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hyt/constants/routes.dart';
import 'package:hyt/screens/profile/driver_verification_screen.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/view_models/profile_view_model.dart';
import 'package:hyt/utils/snackbar_utils.dart';
import 'package:provider/provider.dart';
import 'package:hyt/l10n/app_localizations.dart';
import 'package:hyt/styles/app_theme.dart';

import 'package:hyt/widgets/unified_confirm_dialog.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/styles/app_theme.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // 初始化时加载用户评价统计数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ProfileViewModel>().loadUserReviewsAndStats();
    });
  }

  @override
  Widget build(BuildContext context) {
    // 使用Provider.of而不是context.read，确保在ViewModel状态变化时UI会更新
    final profileViewModel = Provider.of<ProfileViewModel>(context);
    final currentUser = profileViewModel.currentUser;
    final currentRole = profileViewModel.currentRole;

    return Scaffold(
      // 使用主题表层背景，保证明暗一致
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // 顶部个人信息
              _buildProfileHeader(context),

              // 角色切换卡片 - 仅当用户有多个角色时显示
              if (currentUser != null && currentUser.roles.length > 1)
                _buildRoleSwitchCard(context, currentUser.roles, currentRole),

              // 功能列表 - 调整卡片间距
              SizedBox(height: AppInsets.gap4()), // 统一间距
              _buildAccountManagement(),

              SizedBox(height: AppInsets.gap4()), // 统一间距
              _buildWalletManagement(),

              SizedBox(height: AppInsets.gap4()), // 统一间距
              _buildOtherSettings(),

              // 版本信息
              SizedBox(height: AppInsets.gap4()), // 统一间距
              Text(
                '${AppLocalizations.of(context)?.appName ?? '快运通'} v1.0.0',
                style: AppTextStyles.labelSmall.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              SizedBox(height: AppInsets.gap16()),

              // 添加退出登录按钮
              Padding(
                padding: AppInsets.h16(),
                child: SizedBox(
                  width: double.infinity,
                  child: Selector<ProfileViewModel, bool>(
                    selector: (_, vm) => vm.isLoading,
                    builder: (context, isLoading, child) {
                      return ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.error,
                          padding: EdgeInsets.symmetric(
                              vertical: AppInsets.gap16() - 2.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        onPressed: isLoading
                            ? null
                            : () async {
                                await profileViewModel.logout();
                                if (context.mounted) {
                                  Navigator.pushNamedAndRemoveUntil(
                                    context,
                                    Routes.login,
                                    (route) => false,
                                  );
                                }
                              },
                        child: isLoading
                            ? SizedBox(
                                width: 20.w,
                                height: 20.w,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Theme.of(context).colorScheme.onError,
                                  ),
                                ),
                              )
                            : Text(
                                AppLocalizations.of(context)?.logout ?? '退出登录',
                                style: AppTextStyles.bodyLarge.copyWith(
                                  color: Theme.of(context).colorScheme.onError,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      );
                    },
                  ),
                ),
              ),
              SizedBox(height: AppInsets.gap32()),
            ],
          ),
        ),
      ),
    );
  }

  // 顶部个人信息
  Widget _buildProfileHeader(BuildContext context) {
    // 使用Consumer包装，只在ProfileViewModel变化时重建此Widget
    return Consumer<ProfileViewModel>(
      builder: (context, profileViewModel, _) {
        final currentUser = profileViewModel.currentUser;
        final currentRole = profileViewModel.currentRole;
        final userStats = profileViewModel.userStats;
        return Container(
          padding: AppInsets.v12().add(AppInsets.h16()), // 调整垂直内边距
          decoration: BoxDecoration(
            // 可以考虑使用渐变色或更柔和的蓝色
            color: Theme.of(context).colorScheme.primary,
            // 可以添加底部圆角，如果希望与下方内容区有区分
            // borderRadius: BorderRadius.vertical(bottom: Radius.circular(20.r)),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  // 头像 - 可以稍微增大一点
                  Container(
                    width: 75.w, // 增大
                    height: 75.w, // 增大
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      shape: BoxShape.circle,
                      border: Border.all(
                          color: Theme.of(context).colorScheme.surface,
                          width: 2.w),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context)
                              .colorScheme
                              .shadow
                              .withAlpha(38),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      // 使用 ClipOval 保证圆形裁剪
                      child: Image.network(
                        'https://images.unsplash.com/photo-1633332755192-727a05c4013d?w=100',
                        fit: BoxFit.cover,
                        // 添加加载占位符
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              strokeWidth: 2.w,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  Theme.of(context)
                                      .colorScheme
                                      .onPrimary
                                      .withAlpha(178)),
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.person_outline, // 使用 outline 图标
                            size: 45.w, // 调整图标大小
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          );
                        },
                      ),
                    ),
                  ),
                  SizedBox(width: AppInsets.gapW16()),

                  // 用户信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              currentUser?.nickName ??
                                  (AppLocalizations.of(context)?.notLoggedIn ??
                                      '未登录用户'),
                              // 使用 AppTextStyles 或统一的标题样式
                              style: AppTextStyles.headline3.copyWith(
                                color: Theme.of(context).colorScheme.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(width: AppInsets.gapW8()),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8.w, vertical: 3.h), // 调整内边距
                              decoration: BoxDecoration(
                                // 使用更亮的背景色增加对比度
                                color: Theme.of(context)
                                    .colorScheme
                                    .onPrimary
                                    .withAlpha(64),
                                borderRadius:
                                    BorderRadius.circular(6.r), // 调整圆角
                              ),
                              child: Text(
                                currentRole == 'shipper'
                                    ? (AppLocalizations.of(context)?.shipper ??
                                        '货主')
                                    : (AppLocalizations.of(context)?.driver ??
                                        '司机'),
                                // 使用 AppTextStyles 或统一的小号标签样式
                                style: AppTextStyles.labelSmall.copyWith(
                                  color:
                                      Theme.of(context).colorScheme.onPrimary,
                                  fontWeight: FontWeight.w500, // 加一点字重
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // 编辑按钮 - 增加点击效果
                  InkWell(
                    onTap: () {
                      // TODO: 实现编辑个人信息逻辑
                    },
                    borderRadius: BorderRadius.circular(20.r), // 匹配形状
                    child: Container(
                      padding: EdgeInsets.all(10.w), // 增大点击区域
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .onPrimary
                            .withAlpha(51),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.edit_outlined,
                        color: Theme.of(context).colorScheme.onPrimary,
                        size: 20.w, // 调整图标大小
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: AppInsets.gap12()), // 调整间距
              // 数据统计
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem(
                      AppLocalizations.of(context)?.creditScore ?? '信用分',
                      userStats['creditScore']),
                  _buildStatItem(
                      AppLocalizations.of(context)?.orderCount ?? '下单数',
                      userStats['orderCount']),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  // 角色切换卡片
  Widget _buildRoleSwitchCard(
      BuildContext context, List<String> roles, String currentRole) {
    return Selector<ProfileViewModel, bool>(
      selector: (_, vm) => vm.isLoading,
      builder: (context, isLoading, child) {
        return AppCard(
          margin: AppInsets.pageGutter()
              .add(EdgeInsets.symmetric(vertical: AppInsets.gap6())),
          padding: AppInsets.h16().add(AppInsets.v12()),
          borderRadius: 12,
          elevation: 0,
          outlineAlpha: 0.6,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.swap_horiz,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20.sp,
                  ),
                  SizedBox(width: AppInsets.gapW8()),
                  Text(
                    AppLocalizations.of(context)?.roleSwitch ?? '角色切换',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (isLoading) ...[
                    SizedBox(width: 8.w),
                    SizedBox(
                      width: 16.w,
                      height: 16.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              SizedBox(height: AppInsets.gap16()),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildRoleOption(
                    context,
                    AppLocalizations.of(context)?.shipper ?? '货主',
                    Icons.business,
                    currentRole == 'shipper',
                    isLoading ? null : () => _switchRole(context, 'shipper'),
                  ),
                  _buildRoleOption(
                    context,
                    AppLocalizations.of(context)?.driver ?? '司机',
                    Icons.local_shipping,
                    currentRole == 'driver',
                    isLoading ? null : () => _switchRole(context, 'driver'),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  // 角色选项
  Widget _buildRoleOption(BuildContext context, String roleName, IconData icon,
      bool isSelected, VoidCallback? onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 140.w,
        padding: EdgeInsets.symmetric(vertical: AppInsets.gap16()),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.06)
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outlineVariant,
            width: 1.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
              size: 32.sp,
            ),
            SizedBox(height: AppInsets.gap8()),
            Text(
              roleName,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.bold,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            if (isSelected)
              Container(
                margin: EdgeInsets.only(top: AppInsets.gap8()),
                padding: EdgeInsets.symmetric(
                    horizontal: AppInsets.gapW8(), vertical: AppInsets.gap2()),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.12),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  AppLocalizations.of(context)?.currentRole ?? '当前角色',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 切换角色
  // 在_switchRole方法中添加加载状态和错误处理
  void _switchRole(BuildContext context, String newRole) async {
    final viewModel = Provider.of<ProfileViewModel>(context, listen: false);

    // 显示加载状态
    SnackBarUtils.showInfo(
        context, AppLocalizations.of(context)?.switchingRole ?? '正在切换角色...');

    final success = await viewModel.switchRole(newRole);

    if (context.mounted) {
      SnackBarUtils.hide(context);

      if (success) {
        // 角色切换成功后再进行页面跳转

        if (newRole == 'shipper') {
          Navigator.pushReplacementNamed(context, '/home');
        } else if (newRole == 'driver') {
          Navigator.pushReplacementNamed(context, '/driver_home');
        }
      } else {
        // 显示错误信息
        SnackBarUtils.showErrorSnackBar(
            context,
            viewModel.error ??
                (AppLocalizations.of(context)?.roleSwitchFailed ?? '角色切换失败'));
      }
    }
  }

  // 统计项
  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          // 使用 AppTextStyles 或统一的醒目数字样式
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).colorScheme.onPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: AppInsets.gap6()), // 调整间距
        Text(
          label,
          style: AppTextStyles.labelMedium.copyWith(
            color: Theme.of(context).colorScheme.onPrimary.withAlpha(178),
          ),
        ),
      ],
    );
  }

  // 账户管理
  Widget _buildAccountManagement() {
    return Consumer<ProfileViewModel>(
      builder: (context, profileViewModel, _) {
        final currentRole = profileViewModel.currentRole;

        return _buildFunctionCard(
          title: AppLocalizations.of(context)?.accountManagement ?? '账户管理',
          items: [
            FunctionItem(
              icon: FontAwesomeIcons.idCard,
              iconColor: Theme.of(context).colorScheme.primary, // 统一主题主色
              title:
                  AppLocalizations.of(context)?.realNameVerification ?? '实名认证',
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    profileViewModel.getVerificationStatus() == '已认证'
                        ? (AppLocalizations.of(context)?.verified ?? '已认证')
                        : (AppLocalizations.of(context)?.notVerified ?? '未认证'),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: profileViewModel.getVerificationStatus() == '已认证'
                          ? Theme.of(context).colorScheme.secondary
                          : Theme.of(context).colorScheme.error,
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 14.sp,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ],
              ),
              onTap: () async {
                // 如果当前角色是司机且未认证，则导航到司机认证页面
                if (profileViewModel.getVerificationStatus() == '未认证') {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DriverVerificationScreen(),
                    ),
                  );
                } else {
                  // 已认证状态下，用户可以选择放弃或者重新认证
                  final ok = await showConfirmDialog(
                    context,
                    title: AppLocalizations.of(context)?.reVerify ?? '重新认证',
                    content:
                        AppLocalizations.of(context)?.reVerificationConfirm ??
                            '您已完成认证，需要重新认证吗？',
                  );
                  if (!context.mounted) return;
                  if (ok) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DriverVerificationScreen(),
                      ),
                    );
                  }
                }
              },
            ),
            // 订单管理项 - 仅司机角色显示
            if (currentRole == 'driver')
              FunctionItem(
                icon: FontAwesomeIcons.clipboardList,
                iconColor: Theme.of(context).colorScheme.primary,
                title: '订单管理',
                onTap: () {
                  Navigator.pushNamed(context, Routes.orderHistory);
                },
              ),
          ],
        );
      },
    );
  }

  // 钱包管理
  Widget _buildWalletManagement() {
    return Consumer<ProfileViewModel>(
      builder: (context, profileViewModel, _) {
        return _buildFunctionCard(
          title: AppLocalizations.of(context)?.walletManagement ?? '钱包管理',
          items: [
            FunctionItem(
              icon: FontAwesomeIcons.wallet,
              iconColor: Theme.of(context).colorScheme.primary, // 使用主题色区分
              title: AppLocalizations.of(context)?.myWallet ?? '我的钱包',
              // 使用 AppTextStyles 或统一的辅助文本样式
              trailing: Text(
                  '${AppLocalizations.of(context)?.balance ?? '余额'}：${profileViewModel.getWalletBalance()}',
                  style: AppTextStyles.bodySmall),
            ),
            FunctionItem(
              icon: FontAwesomeIcons.creditCard,
              iconColor: Theme.of(context).colorScheme.primary,
              title: AppLocalizations.of(context)?.withdrawalAccount ?? '提现账户',
            ),
            FunctionItem(
              icon: FontAwesomeIcons.fileInvoiceDollar, // 更具体的图标
              iconColor: Theme.of(context).colorScheme.primary,
              title: AppLocalizations.of(context)?.invoiceManagement ?? '开票管理',
            ),
          ],
        );
      },
    );
  }

  // 其他设置
  Widget _buildOtherSettings() {
    return Builder(
      builder: (context) => _buildFunctionCard(
        title: AppLocalizations.of(context)?.otherSettings ?? '其他设置',
        items: [
          FunctionItem(
            icon: FontAwesomeIcons.headset,
            iconColor: Theme.of(context).colorScheme.primary,
            title:
                AppLocalizations.of(context)?.contactCustomerService ?? '联系客服',
          ),
          FunctionItem(
            icon: FontAwesomeIcons.gear, // 可以考虑换成 gear
            iconColor: Theme.of(context).colorScheme.primary,
            title: AppLocalizations.of(context)?.generalSettings ?? '通用设置',
            onTap: () {
              Navigator.pushNamed(context, Routes.settings);
            },
          ),
        ],
      ),
    );
  }

  // 功能卡片
  Widget _buildFunctionCard(
      {required String title, required List<FunctionItem> items}) {
    return AppCard(
      margin: AppInsets.pageGutter(),
      padding: AppInsets.functionCardPadding(),
      borderRadius: 12,
      elevation: 0,
      outlineAlpha: 0.6,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: AppInsets.functionCardHeaderPadding(), // 统一到主题
            child: Text(
              title,
              // 使用 AppTextStyles 或统一的卡片标题样式
              style: AppTextStyles.bodySmall.copyWith(
                // 或 titleSmall
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600, // 加粗一点
              ),
            ),
          ),
          ...items.asMap().entries.map((entry) {
            final item = entry.value;
            return Column(
              children: [
                ListTile(
                  dense: AppListTileTokens.dense,
                  visualDensity: AppListTileTokens.visualDensity,
                  contentPadding: AppListTileTokens.contentPadding(),
                  leading: Container(
                    width: AppListTileTokens.leadingSize(),
                    height: AppListTileTokens.leadingSize(),
                    decoration: BoxDecoration(
                      color: item.iconColor.withAlpha(26),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      item.icon,
                      color: item.iconColor,
                      size: AppListTileTokens.iconSize(),
                    ),
                  ),
                  title: Text(
                    item.title,
                    // 使用 AppTextStyles 或统一的列表项标题样式
                    style: AppTextStyles.bodyMedium.copyWith(
                      // 或 titleMedium
                      fontWeight: FontWeight.w500, // 标准字重
                    ),
                  ),
                  trailing: item.trailing ??
                      Icon(
                        Icons.chevron_right,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        size: AppListTileTokens.chevronSize(),
                      ),
                  onTap: item.onTap, // 直接传递 onTap
                  // 可以添加视觉反馈
                  hoverColor:
                      Theme.of(context).colorScheme.surfaceContainerHighest,
                  splashColor: item.iconColor.withAlpha(26),
                ),
                // 可以考虑移除或调整这里的分割线逻辑（如果之前有的话）
              ],
            );
          }),
        ],
      ),
    );
  }
}

// FunctionItem 类保持不变
class FunctionItem {
  final IconData icon;
  final Color iconColor;
  final String title;
  final Widget? trailing;
  final VoidCallback? onTap;

  FunctionItem({
    required this.icon,
    required this.iconColor,
    required this.title,
    this.trailing,
    this.onTap,
  });
}
