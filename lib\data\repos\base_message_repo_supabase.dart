import 'package:hyt/data/models/base_message.dart';
import 'package:hyt/data/repos/base_message_repo.dart';
import 'package:hyt/utils/enums.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class BaseMessageRepoSupabase implements BaseMessageRepo {
  final SupabaseClient _supabaseClient;
  final String _tableName = 'base_message';
  BaseMessageRepoSupabase(this._supabaseClient);

  /// 获取所有消息（包括已读和未读）
  @override
  Future<List<BaseMessage>> getAllUserMessages({required String userId}) async {
    try {
      // 根据message_id联合查询base_message和message_status表，获取所有消息
      final response = await _supabaseClient
          .from(_tableName)
          .select('''
            *,
            base_message_status!inner(is_read, is_deleted, user_id)
          ''')
          .eq('base_message_status.user_id', userId)
          .eq('base_message_status.is_deleted', false)
          .or('receiver_id.eq.$userId,receiver_id.is.null') // 获取发给当前用户的消息和公共消息（系统消息、活动消息）
          .order('created_at', ascending: false);

      // 处理查询结果
      return response.map((data) {
        final message = BaseMessage.fromMap(data);
        // 从user_message_status中获取is_read状态
        final statusData = data['base_message_status'] as List;
        if (statusData.isNotEmpty) {
          message.isRead = statusData[0]['is_read'] as bool;
          message.isDeleted = statusData[0]['is_deleted'] as bool;
        }
        return message;
      }).toList();
    } catch (e) {
      throw Exception('获取所有消息失败: $e');
    }
  }

  @override
  Future<BaseMessage?> getMessageById({required String messageId}) async {
    try {
      final response = await _supabaseClient
          .from(_tableName)
          .select()
          .eq('id', messageId)
          .single();

      return BaseMessage.fromMap(response);
    } catch (e) {
      throw Exception('获取消息详情失败: $e');
    }
  }

  /// 根据消息类型获取消息列表
  @override
  Future<List<BaseMessage>> getMessagesByType({
    required String userId,
    required MessageType messageType,
  }) async {
    try {
      // 基于getAllUserMessages方法，增加消息类型过滤条件
      final response = await _supabaseClient
          .from(_tableName)
          .select('''
            *,
            base_message_status!inner(is_read, is_deleted, user_id)
          ''')
          .eq('base_message_status.user_id', userId)
          .eq('base_message_status.is_deleted', false)
          .eq('type', messageType.name) // 按消息类型过滤
          .or('receiver_id.eq.$userId,receiver_id.is.null') // 获取发给当前用户的消息和公共消息
          .order('created_at', ascending: false);

      // 处理查询结果
      return response.map((data) {
        final message = BaseMessage.fromMap(data);
        // 从user_message_status中获取is_read状态
        final statusData = data['base_message_status'] as List;
        if (statusData.isNotEmpty) {
          message.isRead = statusData[0]['is_read'] as bool;
          message.isDeleted = statusData[0]['is_deleted'] as bool;
        }
        return message;
      }).toList();
    } catch (e) {
      throw Exception('获取${messageType.displayName}失败: $e');
    }
  }

  /// 获取与订单相关的消息
  @override
  Future<List<BaseMessage>> getMessagesByOrderId(String orderId) async {
    try {
      final response = await _supabaseClient
          .from(_tableName)
          .select()
          .eq('order_id', orderId)
          .order('created_at', ascending: false);

      return response.map((data) => BaseMessage.fromMap(data)).toList();
    } catch (e) {
      throw Exception('获取订单消息失败: $e');
    }
  }

  /// 清空所有消息
  @override
  Future<bool> clearAllMessages({required String userId}) async {
    try {
      await _supabaseClient.from(_tableName).delete().eq('receiver_id', userId);
      return true;
    } catch (e) {
      throw Exception('清空消息失败: $e');
    }
  }

  @override
  Future<bool> deleteMessage({required String messageId}) async {
    try {
      // 获取当前用户ID
      final userId = _supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('用户未登录');
      }

      // 更新message_status表中的is_deleted状态，而不是真正删除消息
      await _supabaseClient
          .from('base_message_status')
          .update({
            'is_deleted': true,
            'updated_at': DateTime.now(),
          })
          .eq('message_id', messageId)
          .eq('user_id', userId);

      return true;
    } catch (e) {
      throw Exception('删除消息失败: $e');
    }
  }

  @override
  Future<bool> deleteAllMessages({required String userId}) async {
    try {
      // 批量更新message_status表中的is_deleted状态，将该用户的所有消息标记为已删除
      await _supabaseClient
          .from('base_message_status')
          .update({
            'is_deleted': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('is_deleted', false); // 只更新未删除的消息

      return true;
    } catch (e) {
      throw Exception('批量删除消息失败: $e');
    }
  }

  @override
  Future<int> getUnreadMessageCount({required String userId}) async {
    try {
      // 联合查询base_message和message_status表，获取未读消息数量
      final response = await _supabaseClient
          .from(_tableName)
          .select('''
            id,
            base_message_status!inner(is_read, user_id)
          ''')
          .eq('base_message_status.user_id', userId)
          .eq('base_message_status.is_read', false)
          .eq('base_message_status.is_deleted', false)
          .or('receiver_id.eq.$userId,receiver_id.is.null'); // 获取发给当前用户的消息和公共消息

      // 返回未读消息数量
      return response.length;
    } catch (e) {
      throw Exception('获取未读消息数量失败: $e');
    }
  }

  @override
  Future<bool> markMessageAsRead({required String messageId}) async {
    try {
      // 获取当前用户ID
      final userId = _supabaseClient.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('用户未登录');
      }

      // 更新message_status表中的is_read状态
      await _supabaseClient
          .from('base_message_status')
          .update({'is_read': true})
          .eq('message_id', messageId)
          .eq('user_id', userId);

      return true;
    } catch (e) {
      throw Exception('标记消息为已读失败: $e');
    }
  }

  @override
  Future<bool> sendOrderMessage({required BaseMessage message}) async {
    try {
      // 从消息对象中获取必要的订单和接收者信息
      final orderId = message.orderId;
      final receiverId = message.receiverId;

      // 验证必要字段
      if (orderId == null || orderId.isEmpty) {
        throw Exception('订单消息必须包含orderId');
      }
      if (receiverId == null || receiverId.isEmpty) {
        throw Exception('订单消息必须包含receiverId');
      }

      // 准备消息数据
      final messageData = message.toMap(forInsert: true);
      messageData['order_id'] = orderId;
      messageData['receiver_id'] = receiverId;

      // 插入消息到数据库
      final response = await _supabaseClient
          .from(_tableName)
          .insert(messageData)
          .select()
          .single();

      // 获取插入后的消息ID
      final messageId = response['id'] as String;

      // 为接收者创建消息状态记录（标记为未读）
      await _supabaseClient.from('base_message_status').insert({
        'message_id': messageId,
        'user_id': receiverId,
        'is_read': false,
        'is_deleted': false
      });

      return true;
    } catch (e) {
      throw Exception('发送订单消息失败: $e');
    }
  }

  @override
  Future<bool> deleteOrderMessage({required String messageId}) async {
    try {
      // 删除消息
      await _supabaseClient.from(_tableName).delete().eq('id', messageId);

      // 删除消息状态记录
      await _supabaseClient
          .from('base_message_status')
          .delete()
          .eq('message_id', messageId);

      return true;
    } catch (e) {
      throw Exception('删除订单消息失败: $e');
    }
  }
}
