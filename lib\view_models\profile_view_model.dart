import 'package:hyt/data/models/appuser.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:hyt/data/repos/order_repo.dart';
import 'package:hyt/view_models/base_view_model.dart';
import 'package:hyt/utils/log_utils.dart';

/// 个人中心页面的ViewModel，处理用户个人信息和角色切换等业务逻辑
class ProfileViewModel extends BaseViewModel {
  final AppUserViewModel _appUserViewModel;
  final OrderRepo _orderRepository;

  // 构造函数，注入AppUserViewModel和OrderRepo
  ProfileViewModel(this._appUserViewModel, this._orderRepository);

  // 获取当前用户信息
  AppUser? get currentUser => _appUserViewModel.currentUser;

  // 获取当前角色
  String get currentRole => _appUserViewModel.currentRole;

  // 用户统计数据，现在从AppUser和计算后的评价数据获取
  Map<String, dynamic> get userStats {
    // '信用分' 直接使用AppUser的rating字段，并格式化为 X.X/5 样式
    final creditScore = currentUser?.rating != null
        ? '${currentUser!.rating!.toStringAsFixed(1)}/5.0'
        : 'N/A';

    // '下单数' 直接使用AppUser的completedOrders字段
    final orderCount = currentUser?.completedOrders?.toString() ?? '0';

    return {
      'creditScore': creditScore,
      'orderCount': orderCount,
    };
  }

  /// 加载并聚合当前用户收到的评价数据
  Future<void> loadUserReviewsAndStats() async {
    if (currentUser == null || currentUser!.id == null) {
      notifyListeners();
      return;
    }

    setLoading(true);
    clearError();

    try {
      // 获取当前用户收到的所有评价
      final reviews = await _orderRepository.getReviewsForUser(
        currentUser!.id!,
        role: currentRole, // 根据当前角色过滤评价
      );

      if (reviews.isNotEmpty) {
        // int countPositive = 0;
        // for (var review in reviews) {
        //   if (review.rating >= 4) {
        //     // 假设4星及以上为好评
        //     countPositive++;
        //   }
        // }
        // _positiveReviews = countPositive;
      } else {
        // _positiveReviews = 0;
      }
    } catch (e, st) {
      setError('加载评价失败: $e');
      LogUtils.e(error!, e, st);
    } finally {
      setLoading(false);
    }
  }

  /// 切换用户角色
  /// [newRole] 新的角色
  Future<bool> switchRole(String newRole) async {
    try {
      setLoading(true);
      clearError();
      final success = await _appUserViewModel.switchRole(newRole);
      if (success) {
        // 切换角色后，重新加载用户评价和统计数据
        await loadUserReviewsAndStats();
      }
      return success;
    } catch (e, st) {
      setError('角色切换失败: $e');
      LogUtils.e(error!, e, st);
      return false;
    } finally {
      setLoading(false);
    }
  }

  /// 更新用户信息
  /// [user] 更新后的用户信息
  Future<bool> updateUserInfo(AppUser user) async {
    try {
      setLoading(true);
      clearError();
      final result = await _appUserViewModel.updateUserInfo(user);
      // 更新用户信息后，刷新评价统计
      await loadUserReviewsAndStats();
      return result;
    } catch (e, st) {
      setError('更新用户信息失败: $e');
      LogUtils.e(error!, e, st);
      return false;
    } finally {
      setLoading(false);
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      setLoading(true);
      notifyListeners();
      await _appUserViewModel.logout();
      // 登出后清空统计数据
      // _totalReviews = 0;
      // _positiveReviews = 0;
    } catch (e, st) {
      setError('登出失败: $e');
      LogUtils.e(error!, e, st);
    } finally {
      setLoading(false);
    }
  }

  /// 获取用户实名认证状态
  /// 实际项目中应从后端获取
  String getVerificationStatus() {
    // 模拟获取认证状态
    if (_appUserViewModel.currentUser?.isVerified == false) {
      return '未认证';
    } else {
      return '已认证';
    }
  }

  /// 提交司机认证信息
  /// [verificationData] 包含司机认证所需的所有信息
  Future<bool> submitDriverVerification(
      Map<String, dynamic> verificationData) async {
    try {
      setLoading(true);
      clearError();

      await Future.delayed(const Duration(seconds: 2));

      if (currentUser != null) {
        // 这里假设updateUserInfo会触发AppUserViewModel的currentUser更新
        final updatedUser = AppUser(
          id: currentUser!.id,
          nickName: currentUser!.nickName,
          phone: currentUser!.phone,
          avatar: currentUser!.avatar,
          roles: currentUser!.roles,
          userRole: currentUser!.userRole,
          isVerified: true, // 设置为已认证
          vehicleId: currentUser!.vehicleId,
          rating: currentUser!.rating, // 保持现有评分
          completedOrders: currentUser!.completedOrders, // 保持现有完成订单数
          driverLicenseStatus: 'verified', // 设置驾驶证状态为已验证
          vehiclePlate: verificationData['vehiclePlate'], // 更新车牌号
          companyName: currentUser!.companyName,
          userType: currentUser!.userType,
          passwordHash: currentUser!.passwordHash,
        );
        final success = await _appUserViewModel.updateUserInfo(updatedUser);
        if (success) {
          await loadUserReviewsAndStats(); // 认证后刷新评价统计
        }
        return success;
      } else {
        setError('用户信息为空');
        return false;
      }
    } catch (e, st) {
      setError('提交认证信息失败: $e');
      LogUtils.e(error!, e, st);
      return false;
    } finally {
      setLoading(false);
    }
  }

  /// 获取钱包余额
  /// 实际项目中应从后端获取
  String getWalletBalance() {
    // 模拟获取钱包余额
    return '¥1,286';
  }
}
