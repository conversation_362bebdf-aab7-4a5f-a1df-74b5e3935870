name: hyt
description: "A new Flutter project."
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  # 地图和地址相关依赖
  google_maps_flutter: ^2.12.1
  flutter_screenutil: ^5.9.0
  provider: ^6.1.1
  flutter_easyloading: ^3.0.5
  font_awesome_flutter: ^10.6.0
  # Replace carousel_slider with another package
  flutter_carousel_widget: ^3.1.0
  http: ^1.4.0
  flutter_dotenv: ^5.2.1

  pull_to_refresh: ^2.0.0
  intl: ^0.19.0
  shared_preferences: ^2.2.2
  flutter_local_notifications: ^19.1.0
  url_launcher: ^6.2.5
  supabase_flutter: ^2.9.0
  uuid: ^4.5.1
  bcrypt: ^1.1.3
  flutter_google_places_sdk: ^0.4.2+1
  flutter_datetime_picker_plus: ^2.2.0
  image_picker: ^1.1.2
  permission_handler: ^12.0.0+1
  
  
  # 消消乐游戏依赖
  audioplayers: ^5.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - .env
  fonts:
    - family: AlibabaPuHuiTi
      fonts:
        - asset: assets/fonts/AlibabaPuHuiTi3.0-55Regular.ttf