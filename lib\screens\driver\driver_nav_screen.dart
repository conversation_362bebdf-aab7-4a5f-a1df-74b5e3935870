//司机专用底部导航栏页面，包含工作台，订单，消息，我的四个页面
import 'package:flutter/material.dart';
import 'package:hyt/screens/driver/driver_home_screen.dart';
import 'package:hyt/screens/driver/driver_order_pool_screen.dart';
import 'package:hyt/screens/profile/profile_screen.dart';
import 'package:hyt/screens/message/message_center_screen.dart';

class DriverNavScreen extends StatefulWidget {
  const DriverNavScreen({super.key});

  @override
  State<DriverNavScreen> createState() => _DriverNavScreenState();
}

class _DriverNavScreenState extends State<DriverNavScreen> {
  int _currentIndex = 0;
  final List<Widget> _pages = [
    const DriverHomeScreen(),
    const DriverOrderPoolScreen(),
    const MessageCenterScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_currentIndex],
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          BottomNavigationBar(
            currentIndex: _currentIndex,
            onTap: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
            type: BottomNavigationBarType.fixed,
            selectedItemColor: Theme.of(context).colorScheme.primary,
            unselectedItemColor: Colors.grey.shade700,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.dashboard),
                label: '工作台',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.assignment),
                label: '抢单',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.message),
                label: '消息',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: '我的',
              ),
            ],
          ),
        ],
      ),
    );
  }
}
