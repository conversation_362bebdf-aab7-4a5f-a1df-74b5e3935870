# hyt - 快运通

这是一个使用 Flutter 开发的货运信息管理移动应用程序，旨在连接货主与司机，提供便捷的货物运输服务。

## 项目目标

为货主提供一个易于使用的平台，可以快速发布货运需求、选择合适的车型和服务、管理订单、跟踪货物状态并进行支付。

## 当前状态

项目已基本完成核心功能开发，采用MVVM分层架构，使用Supabase作为后端数据库服务。主要功能包括用户认证、订单管理、消息中心、司机端功能等，详情见docs/开发进度.md。

## 技术架构

### 整体架构设计
项目严格遵循MVVM分层架构：
- **数据层（Data Layer）**: 包含实体模型和仓库，负责数据库交互操作
- **业务逻辑层（ViewModel Layer）**: 采用Flutter的Provider状态管理框架，处理业务逻辑
- **UI层（View Layer）**: 采用Flutter的Material Design 3设计规范，使用Dart语言编写用户界面

### 核心技术栈
- **框架**: Flutter (最新稳定版)
- **语言**: Dart (最新稳定版)
- **状态管理**: Provider
- **设计规范**: Material Design 3
- **后端服务**: Supabase
- **本地存储**: SharedPreferences
- **国际化**: 支持中文、英文、高棉语
- **通知服务**: flutter_local_notifications
- **地图服务**: Google Maps Flutter
- **权限管理**: permission_handler

## 数据持久化存储实现

本应用使用Supabase作为后端数据库服务，实现了数据的持久化存储。以下是实现细节：

### 1. 数据库配置

在`.env`文件中配置了Supabase的URL和API密钥：

```
SUPABASE_URL=https://ggufehcdbnkvxhmdtphj.supabase.co
SUPABASE_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. 数据表结构



## 订单状态更新和通知机制

### 功能概述

快运通应用实现了完善的订单状态更新和通知机制，确保订单状态实时更新并通知相关方。主要功能包括：

1. **订单状态管理**：系统自动跟踪和更新订单状态，包括待接单、进行中、已完成和已取消等状态。
2. **实时通知**：当订单状态发生变化时，系统会自动向相关用户（货主和司机）发送通知。
3. **司机抢单**：司机可以在抢单大厅查看并抢取待接单状态的订单，抢单成功后订单状态自动更新。
4. **订单取消**：货主可以取消待接单状态的订单，系统会通知相关司机。
5. **订单完成**：司机完成订单后，系统会更新订单状态并通知货主。

### 技术实现

#### 1. 订单状态管理

订单状态由`OrderProvider`管理，主要包括以下状态：

- **待接单**（statusCode: 0）：订单创建后的初始状态
- **已接单**（statusCode: 1）：司机接单后的状态
- **运输中**（statusCode: 11）：司机正在将货物运往目的地
- **已送达**（statusCode: 12）：司机已到达卸货地点并完成卸货
- **已完成**（statusCode: 2）：订单完成后的状态
- **已取消**（statusCode: 3）：订单被取消的状态

订单信息card按钮显示逻辑：
司机角色：订单状态为0,（待接单），显示"立即抢单"按钮
货主角色：订单状态为0,（待接单），显示"取消订单"按钮
司机角色：订单状态为1,（已接单），显示"确认取货、联系货主"按钮
货主角色：订单状态为1,（已接单），显示"确认取货、联系司机、取消订单（需要司机确认）"按钮，已经接单，司机可能已经在前往取货的途中，直接取消会导致司机白跑，因此需要司机确认，确认后再取消订单。
司机角色：订单状态为11,（运输中），显示"确认送达，联系货主"按钮
货主角色：订单状态为11,（运输中），显示"联系司机"按钮
司机角色：订单状态为12,（已送达），显示"提醒支付"按钮
货主角色：订单状态为12,（已送达），显示"立即支付"按钮
#### 2. 订单状态更新

- **updateOrderStatus**：更新订单状态，包括更新状态码和状态描述
订单发布后，订单状态默认为0，待接单。
司机接单后，订单状态更新为1。
司机完成取货后，订单状态更新为11。
货主确认取货后，订单状态更新为11。
司机完成卸货后，订单状态更新为12。
货主支付完成后，订单状态更新为2。
货主取消订单后，订单状态更新为3。

- **addOrderStatusHistory**：添加订单状态历史记录
- **getOrderStatusHistory**：获取订单状态历史记录        


#### 2. 通知服务

通知服务由`NotificationService`提供，使用`flutter_local_notifications`插件实现本地通知功能。主要通知类型包括：

- **订单接单通知**：司机抢单成功后，向货主和司机发送通知
- **订单取消通知**：订单被取消后，向相关方发送通知
- **订单完成通知**：订单完成后，向货主和司机发送通知

### 使用方法

#### 司机抢单流程

1. 司机在抢单大厅页面浏览可用订单
2. 点击"立即抢单"按钮
3. 在弹出的确认对话框中点击"确认抢单"
4. 抢单成功后，系统会自动更新订单状态并发送通知
5. 司机会被自动跳转到订单列表页面查看已抢到的订单

#### 订单取消流程

1. 货主在订单列表页面找到待接单状态的订单
2. 点击"取消订单"按钮
3. 系统会自动更新订单状态并发送通知

#### 司机订单跟踪与完成流程

1. 司机在订单列表页面或订单池页面找到已接单的订单
2. 点击订单进入司机订单详情页面
3. 根据当前订单状态，页面底部会显示相应的操作按钮：
   - 已接单状态：显示"确认已取货"按钮
   - 已取货状态：显示"开始运输"按钮
   - 运输中状态：显示"确认已送达"按钮
   - 已送达状态：显示"完成订单"按钮
4. 司机点击相应按钮更新订单状态
5. 每次状态更新都会记录在订单状态历史中，并向货主发送通知
6. 当订单状态更新为"已完成"时，整个订单流程结束

### 代码结构


### 注意事项

1. 通知功能需要用户授予应用通知权限
2. 在实际生产环境中，订单状态更新应该与后端服务同步
3. 为了提高用户体验，可以考虑添加推送通知功能，以便在应用未运行时也能接收通知


## 取消订单机制

### 功能概述

快运通应用实现了完善的取消订单机制，采用分层确认机制平衡各方利益。该机制考虑了不同订单状态下的取消权限、费用承担、确认流程和风险控制，确保平台运营的公平性和效率。

### 设计原则

1. **分层确认**：根据订单状态和取消方不同，采用不同的确认机制
2. **费用公平**：合理分担取消成本，保护各方合法权益
3. **风险控制**：防止恶意取消和频繁取消行为
4. **流程透明**：取消规则清晰明确，用户易于理解

### 取消权限设计

#### 货主取消权限
- **已接单状态(1)**：可直接取消，但需司机确认，支付空跑费
- **运输中状态(11)**：不可取消，仅限紧急情况，需平台客服介入处理
- **已送达状态(12)**：不可取消，订单即将完成

#### 司机取消权限
- **已接单状态(1)**：接单后30分钟内可取消，超时需提供理由
- **运输中状态(11)**：不可取消，仅限紧急情况，需平台客服介入处理
- **已送达状态(12)**：不可取消，等待货主确认收货

#### 紧急情况包括但不限于：
- 自然灾害（台风、地震、洪水等）
- 交通事故导致无法继续运输
- 货物损坏或安全隐患
- 其他不可抗力因素

### 费用承担机制

#### 已接单状态取消费用
```
司机主动取消：
- 30分钟内：无费用，记录取消次数
- 超过30分钟：需要合理理由，可能影响信用评分

货主主动取消：
- 支付司机空跑费：订单金额的10%，最低20元，最高100元
- 费用自动从货主账户扣除并转账给司机
```

#### 运输中状态取消费用
```
紧急情况取消：
- 平台客服评估后确定具体费用分担
- 特殊情况下平台承担部分损失
```

### 业务流程设计

#### 货主发起取消流程
1. **发起取消**：货主在订单详情页点击"取消订单"
2. **选择原因**：从预设原因中选择或填写自定义原因（必填）
3. **费用确认**：系统自动计算并显示需要支付的空跑费用
4. **确认支付**：货主确认支付空跑费用
5. **发送请求**：系统向司机发送取消确认请求
6. **司机确认**：司机在24小时内确认或拒绝取消请求
7. **完成取消**：确认后自动扣费转账，订单状态更新为已取消

#### 司机发起取消流程
1. **发起取消**：司机在订单详情页点击"取消订单"
2. **时间检查**：系统检查是否在30分钟免费取消窗口内
3. **选择原因**：填写取消原因（超时取消必填详细原因）
4. **发送请求**：系统向货主发送取消确认请求
5. **货主确认**：货主在24小时内确认或拒绝取消请求
6. **完成取消**：确认后更新订单状态，记录司机取消次数

### 风险控制机制

#### 取消次数限制
- **司机限制**：每月最多主动取消5次，超出后需要审核
- **货主限制**：每月最多主动取消10次，频繁取消影响信用评分
- **统计周期**：按自然月统计，每月1日重置计数

#### 信用评分影响
- **司机信用**：频繁取消降低信用评分，影响接单优先级
- **货主信用**：恶意取消影响信用评分，可能限制发单权限
- **恢复机制**：连续良好行为可逐步恢复信用评分

#### 黑名单机制
- **触发条件**：连续恶意取消、虚假理由取消、拒绝支付费用
- **限制措施**：暂停使用相关功能，严重者永久封禁
- **申诉渠道**：提供申诉机制，人工审核处理

### 客服仲裁机制

#### 争议处理
- **自动仲裁**：系统根据预设规则自动处理简单争议
- **人工介入**：复杂争议由客服人员介入调解
- **处理时效**：争议订单在48小时内给出处理结果

#### 紧急情况处理
- **紧急通道**：提供7×24小时紧急客服热线
- **快速响应**：紧急情况30分钟内响应处理
- **特殊政策**：自然灾害等不可抗力因素免除取消费用

### 技术实现要点

#### 状态管理
- 订单状态实时同步，确保取消操作的准确性
- 取消流程中的中间状态管理，防止重复操作

#### 费用计算
- 自动计算空跑费用，支持不同地区的费用标准
- 实时汇率和费率更新，确保计算准确性

#### 通知机制
- 取消请求实时推送通知相关方
- 短信、应用内通知多渠道确保信息送达

### 未来优化方向

1. **智能预测**：基于历史数据预测取消风险，提前干预
2. **动态定价**：根据供需关系动态调整取消费用
3. **个性化政策**：基于用户信用等级提供差异化取消政策
4. **AI客服**：引入AI客服处理标准化争议，提高处理效率


## 消息中心功能

### 功能概述

快运通应用实现了完善的消息中心功能，用于管理系统通知、订单消息和活动消息等。主要功能包括：

1. **消息管理**：系统自动管理各类消息，包括系统通知、订单消息和活动消息等。
2. **消息分类**：消息按类型分类，方便用户查看和管理。
3. **消息状态**：支持已读/未读状态管理，用户可以标记消息为已读或查看未读消息数量。
4. **订单关联**：消息可以关联到特定订单，用户可以通过消息快速查看相关订单。

### 技术实现

#### 1. 消息数据模型

消息数据由`Message`类定义，主要包括以下属性：

- **id**：消息唯一标识符
- **type**：消息类型（系统通知、订单消息、活动消息等）
- **title**：消息标题
- **content**：消息内容
- **time**：消息时间
- **orderId**：相关订单ID（可选）
- **icon**：消息图标
- **iconColor**：图标颜色
- **iconBgColor**：图标背景色
- **isUnread**：是否未读

#### 2. 消息状态管理
主要功能包括：
- **添加消息**：添加新消息到消息列表
- **获取消息**：获取所有消息、特定类型的消息或与特定订单相关的消息
- **标记已读**：标记单条消息、所有消息或特定类型的消息为已读
- **删除消息**：删除单条消息、所有消息或特定类型的消息

### 使用方法

#### 添加新消息

### 未来优化方向

2. 实现订单状态变更的实时同步功能，使用WebSocket或其他实时通信技术
3. 添加订单评价功能，让货主可以对司机的服务进行评价
4. 集成地图服务，实现实时订单跟踪功能，显示司机实时位置和预计到达时间
5. 优化通知设置，允许用户自定义通知方式和频率
6. 添加货物照片上传功能，司机可以在取货和送达时拍照上传，作为货物状态证明
7. 实现路线规划功能，为司机提供最优运输路线
8. 添加订单统计和分析功能，帮助司机了解收入情况和工作效率

## 功能规划 (根据原型)

*   **用户认证:**
    *   手机号/密码登录（完成）
    *   第三方登录 (待定)
    *   用户注册（待定）
    *   忘记密码（待定）
*   **核心下单流程:**
    *   服务类型选择 (长途货运 / 同城速运)
    *   车型选择 (根据服务类型动态展示)
    *   地址选择 (起点/终点，支持地图选点和搜索)
    *   装货/卸货地址确认 (包含详细信息：门牌号、联系人、电话)
    *   装货时间选择 (立即/预约/自定义)
    *   货物信息填写 (类型/重量)
    *   服务/价格选择 (拼车/快车/用户出价/特快)
    *   订单备注
    *   确认下单与支付 (集成支付宝/微信支付)
*   **订单管理:**
    *   订单列表展示 (按状态筛选：全部/待接单/进行中/待支付/已完成)
    *   订单详情查看 (不同状态下显示不同信息和操作)
    *   订单跟踪 (实时地图展示 - 模拟，运输进度时间轴)
    *   订单操作 (取消订单/修改订单/立即支付/评价)
*   **消息中心:**
    *   消息分类查看 (全部/系统通知/订单消息/活动消息)
    *   消息列表与详情
*   **个人中心:**
    *   用户基本信息展示 (头像/昵称/会员等级)
    *   账户信息 (积分/钱包/订单统计)
    *   地址簿管理 (增/删/改/查/选择)
    *   应用设置 (通用设置/检查更新/关于)
    *   联系客服
    *   分享应用
    *   收费标准查看
*   **角色路由系统:**
    *   司机和用户同一个app，不同角色加载不同页面
    *   支持用户拥有多种角色（货主shipper和司机driver）
    *   根据当前活动角色自动导航到对应界面
    *   拥有双重角色的用户可通过浮动按钮切换角色
    *   角色切换时自动导航到对应角色的主界面
*   **其他:**
    *   物流发布入口 (发布货源信息)

## 开发进度总览

### 已完成功能 ✅
- **用户认证系统**: 手机号/密码登录，角色管理，权限控制
- **完整下单流程**: 服务类型选择、车型选择、地址选择、货物信息填写、订单确认
- **订单管理**: 订单列表、详情查看、状态跟踪、操作管理
- **司机端功能**: 抢单大厅、订单池管理、收入统计、订单历史
- **消息中心**: 消息分类、状态管理、订单关联、通知推送
- **角色切换系统**: 货主/司机角色动态切换，不同角色页面导航
- **国际化支持**: 中文、英文、高棉语多语言支持
- **通知系统**: 本地通知、订单状态变更通知
- **数据持久化**: Supabase后端集成，完整的数据模型设计

### 开发中功能 🔄
- **支付功能**: 支付页面UI已完成，SDK集成中
- **订单评价**: UI设计完成，功能开发中
- **实时地图**: 静态地图已实现，实时定位对接中

### 待开发功能 📋
- **第三方登录**: 已预留入口，待对接
- **忘记密码**: UI已设计，功能待开发
- **联系客服**: UI已设计，功能开发中
- **分享应用**: 待开发
- **收费标准查看**: 待开发

## 代码质量特点

### 架构优势
1. **清晰分层**: 严格的MVVM架构，数据层、业务逻辑层、UI层职责分离
2. **状态管理**: 合理使用Provider进行状态管理，避免状态混乱
3. **组件复用**: 良好的组件抽象，如OrderCard、MessageCard等可复用组件
4. **错误处理**: 完善的异常处理机制和用户友好的错误提示

### 开发规范
1. **命名规范**: 数据库字段采用下划线命名，Dart代码采用驼峰命名
2. **代码注释**: 详细的代码注释，不占用单独行
3. **常量管理**: 统一的常量定义，如OrderStatusConstants
4. **国际化**: 完整的多语言支持框架

### 性能优化
1. **静态组件**: 默认添加const修饰符优化性能
2. **懒加载**: 合理的数据加载策略
3. **内存管理**: 正确的资源释放和生命周期管理

## 环境配置与运行

### 前置要求
1. Flutter SDK (>=3.0.0)
2. Dart SDK (>=3.0.0)
3. Android Studio / VS Code
4. Android SDK / Xcode (iOS开发)

### 环境配置
1. **Supabase配置**: 在项目根目录创建`.env`文件，配置Supabase连接信息：
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_API_KEY=your_supabase_api_key
   ```

2. **Google Maps配置**: 在`android/app/src/main/AndroidManifest.xml`中配置API Key：
   ```xml
   <meta-data
       android:name="com.google.android.geo.API_KEY"
       android:value="your_google_maps_api_key"/>
   ```

### 运行步骤
1. 确保已安装并配置好Flutter开发环境
2. 克隆项目代码到本地
3. 在项目根目录执行：`flutter pub get`
4. 配置`.env`文件和相关API密钥
5. 连接设备或启动模拟器
6. 运行：`flutter run`

### 数据库初始化
项目使用Supabase作为后端，相关SQL脚本位于`database/`目录：
- `create_app_user.sql`: 用户表结构
- `create_order.sql`: 订单表结构
- `create_base_message.sql`: 消息表结构
- `create_function.sql`: 数据库函数

## 核心功能实现

### 1. 用户认证与角色管理
- **双角色系统**: 支持货主（shipper）和司机（driver）两种角色
- **角色切换**: 同一用户可拥有多重角色，支持动态切换
- **权限控制**: 基于角色的页面导航和功能访问控制
- **登录认证**: 手机号/密码登录，使用BCrypt加密存储

### 2. 订单管理系统
- **完整生命周期**: 待接单 → 已接单 → 运输中 → 已送达 → 已完成
- **状态管理**: 统一的订单状态常量，包含状态码、颜色、图标定义
- **角色差异化**: 货主和司机看到不同的操作按钮和信息
- **历史追踪**: 完整的订单状态变更历史记录
- **逻辑删除**: 支持货主和司机分别删除订单（不影响对方）
- **自动数据刷新**: 货主进入订单页面时自动从后台刷新订单数据，确保信息实时性
- **取消订单机制**: 采用分层确认机制，平衡各方利益和风险控制

### 3. 司机端功能
- **抢单大厅**: 司机可查看并抢取待接单订单
- **订单池管理**: 管理已接订单，支持状态更新
- **收入统计**: 司机收入数据展示和历史记录
- **订单跟踪**: 支持确认取货、开始运输、确认送达等操作
- **自动数据刷新**: 司机进入首页时自动从后台刷新订单和收入数据，确保信息实时性

### 4. 消息中心
- **消息分类**: 系统通知、订单消息、活动消息分类管理
- **状态管理**: 已读/未读状态，未读消息数量统计
- **订单关联**: 消息可关联特定订单，支持快速跳转
- **实时通知**: 订单状态变更自动发送通知
- **自动数据刷新**: 进入消息中心时自动刷新消息数据，确保信息实时性

### 5. 通知系统
- **本地通知**: 使用flutter_local_notifications实现
- **状态通知**: 订单状态变更自动通知相关用户
- **多媒体支持**: 支持振动、声音提醒
- **消息持久化**: 通知消息存储到消息中心

## 数据模型设计

### 核心实体
1. **AppUser**: 用户实体，支持多角色，包含司机和货主特有属性
2. **Order**: 订单实体，包含完整的订单信息和状态管理
3. **BaseMessage**: 消息实体，支持分类和状态管理
4. **OrderStatusHistory**: 订单状态历史记录
5. **VehicleInfo**: 车辆信息模型
6. **DriverEarnings**: 司机收入统计模型

### 数据库设计原则
- 使用Supabase作为后端数据库服务
- 遵循小写下划线命名规范（如：status_code）
- 支持逻辑删除（is_deleted_by_customer, is_deleted_by_driver）
- 实体类采用驼峰命名（如：statusName方法）

## 代码结构

本项目采用MVVM分层架构设计，主要目录结构如下：

```
lib/
├── constants/               # 应用常量定义
│   ├── order_status_constants.dart  # 订单状态常量
│   └── routes.dart          # 路由配置
├── data/                    # 数据层
│   ├── models/             # 数据模型
│   │   ├── appuser.dart    # 用户模型
│   │   ├── order.dart      # 订单模型
│   │   ├── base_message.dart # 消息模型
│   │   ├── order_status_history.dart # 订单状态历史
│   │   ├── vehicle_info.dart # 车辆信息模型
│   │   └── driver_earnings.dart # 司机收入模型
│   ├── repos/              # 数据仓库
│   │   ├── appuser_repo_supabase.dart # 用户仓库
│   │   ├── order_repo_supabase.dart # 订单仓库
│   │   ├── base_message_repo_supabase.dart # 消息仓库
│   │   └── driver_earnings_repo_supabase.dart # 收入仓库
│   └── vehicle_type_data.dart # 车型数据
├── view_models/            # 视图模型（业务逻辑层）
│   ├── appuser_view_model.dart # 用户视图模型
│   ├── order_view_model.dart # 订单视图模型
│   ├── message_view_model.dart # 消息视图模型
│   ├── address_view_model.dart # 地址视图模型
│   ├── vehicle_info_view_model.dart # 车辆信息视图模型
│   └── driver_earnings_view_model.dart # 司机收入视图模型
├── screens/                # 页面组件（UI层）
│   ├── auth/               # 认证相关页面
│   │   ├── auth_wrapper.dart # 认证包装器
│   │   └── login_screen.dart # 登录页面
│   ├── home/               # 首页
│   │   ├── user_nav_screen.dart # 货主导航页面
│   │   └── user_home_screen.dart # 货主首页
│   ├── driver/             # 司机端页面
│   │   ├── driver_nav_screen.dart # 司机导航页面
│   │   ├── driver_home_screen.dart # 司机首页
│   │   ├── driver_order_pool_screen.dart # 抢单大厅
│   │   └── driver_order_history_screen.dart # 司机订单历史
│   ├── order/              # 订单相关页面
│   │   ├── order_list_screen.dart # 订单列表
│   │   ├── order_detail_screen.dart # 订单详情
│   │   ├── order_confirm_screen.dart # 订单确认
│   │   └── order_history_screen.dart # 订单历史
│   ├── message/            # 消息中心
│   │   └── message_center_screen.dart # 消息中心页面
│   ├── profile/            # 个人中心
│   │   ├── profile_screen.dart # 个人中心页面
│   │   └── driver_verification_screen.dart # 司机认证页面
│   ├── address/            # 地址相关页面
│   │   ├── address_search_screen.dart # 地址搜索
│   │   └── address_confirm_screen.dart # 地址确认
│   ├── payment/            # 支付相关页面
│   │   └── payment_screen.dart # 支付页面
│   └── settings/           # 设置页面
│       └── settings_screen.dart # 设置页面
├── services/               # 服务层
│   ├── notification_service.dart # 通知服务
│   ├── settings_manager.dart # 设置管理
│   └── sound_manager.dart  # 声音管理
├── widgets/                # 公共组件
│   ├── order_card.dart     # 订单卡片
│   ├── message_card.dart   # 消息卡片
│   ├── order_action_buttons.dart # 订单操作按钮
│   ├── cancel_order_dialog.dart # 取消订单对话框
│   └── search_app_bar.dart # 搜索栏
├── utils/                  # 工具类
│   ├── validators.dart     # 表单验证
│   ├── enums.dart         # 枚举定义
│   └── snackbar_utils.dart # 提示工具
├── styles/                 # 样式
│   └── text_styles.dart    # 文本样式
├── l10n/                   # 国际化
│   └── app_localizations.dart # 本地化文本
├── providers/              # Provider状态管理
│   └── language_provider.dart # 语言提供者
└── main.dart               # 应用入口
```

## 项目特色

### 1. 双角色系统设计
- 同一应用支持货主和司机两种角色
- 智能角色切换，无需重新登录
- 基于角色的权限控制和页面导航

### 2. 完整的订单生命周期管理
- 从发布订单到完成交付的全流程跟踪
- 实时状态更新和通知推送
- 详细的订单历史记录和状态追踪

### 3. 现代化技术架构
- MVVM分层架构，代码结构清晰
- Provider状态管理，响应式UI更新
- Supabase云数据库，数据安全可靠

### 4. 优秀的用户体验
- Material Design 3设计规范
- 多语言国际化支持
- 丰富的交互反馈和动画效果

## 未来规划

### 短期目标（1-3个月）
- [ ] 完成支付功能集成（支付宝、微信支付）
- [ ] 实现实时地图定位和轨迹跟踪
- [ ] 完善订单评价系统
- [ ] 添加推送通知功能

### 中期目标（3-6个月）
- [ ] 集成第三方登录（微信、QQ等）
- [ ] 实现在线客服系统
- [ ] 添加货物保险功能
- [ ] 开发Web管理后台

### 长期目标（6个月以上）
- [ ] AI智能调度系统
- [ ] 区块链货运溯源
- [ ] 大数据分析平台
- [ ] 国际化市场拓展

## 贡献指南

欢迎开发者为项目贡献代码！请遵循以下步骤：

1. Fork本项目
2. 创建功能分支：`git checkout -b feature/your-feature`
3. 提交更改：`git commit -am 'Add some feature'`
4. 推送分支：`git push origin feature/your-feature`
5. 提交Pull Request

### 代码规范
- 遵循Flutter官方代码规范
- 使用有意义的变量和函数命名
- 添加必要的代码注释
- 确保代码通过所有测试

## 许可证

本项目采用MIT许可证，详情请查看[LICENSE](LICENSE)文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: [GitHub Issues](https://github.com/sqlilei/hyt/issues)
- 邮箱: <EMAIL>

---

**注意**: 本项目仅供学习和研究使用，商业使用请联系作者获得授权。
