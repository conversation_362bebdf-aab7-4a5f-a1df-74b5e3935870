<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快运通-原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .mockup-frame {
            display: inline-block;
            width: 375px;
            height: 667px;
            border: 2px dashed #ccc;
            border-radius: 30px;
            margin-right: 20px;
            overflow: hidden;
            position: relative;
            background-color: #f8fafc;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .step-item {
            position: relative;
            flex: 1;
            text-align: center;
        }

        .step-item:not(:last-child)::after {
            content: '\f061';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            top: 50%;
            right: -10%;
            color: #94a3b8;
            transform: translateY(-50%);
            font-size: 14px;
        }
    </style>
    <script>
        let selectedVehicle = null;
        const vehicleTypes = {
            'local': [
                { id: 'local-1', icon: 'fas fa-truck', name: '厢式货车', description: '3.5吨' },
                { id: 'local-2', icon: 'fas fa-truck-moving', name: '平板车', description: '4.2米' },
                { id: 'local-3', icon: 'fas fa-truck-pickup', name: '高栏车', description: '6.8米' },
                { id: 'local-4', icon: 'fas fa-truck', name: '冷藏车', description: '4.2米' }
            ],
            'long': [
                { id: 'long-1', icon: 'fas fa-running', name: '跑腿', description: '小件配送' },
                { id: 'long-2', icon: 'fas fa-cube', name: '四轮小件', description: '面包车' },
                { id: 'long-3', icon: 'fas fa-truck-pickup', name: '微面', description: '1.5吨' },
                { id: 'long-4', icon: 'fas fa-truck', name: '小面', description: '2吨' }
            ]
        };

        function selectVehicle(vehicleId) {
            selectedVehicle = vehicleId;
            document.querySelectorAll('.vehicle-type-container button').forEach(btn => {
                if (btn.dataset.vehicleId === vehicleId) {
                    btn.classList.remove('bg-gray-50', 'hover:bg-gray-100');
                    btn.classList.add('bg-blue-100', 'border-2', 'border-blue-500');
                } else {
                    btn.classList.remove('bg-blue-100', 'border-2', 'border-blue-500');
                    btn.classList.add('bg-gray-50', 'hover:bg-gray-100');
                }
            });
        }

        function updateVehicleTypes(type) {
            selectedVehicle = null;
            const vehicleContainer = document.querySelector('.vehicle-type-container');
            const vehicles = vehicleTypes[type];
            let html = '';
            vehicles.forEach(vehicle => {
                html += `
                    <button 
                        class="bg-gray-50 hover:bg-gray-100 rounded-lg p-2 text-center transition-colors flex flex-col items-center justify-center"
                        data-vehicle-id="${vehicle.id}"
                        onclick="selectVehicle('${vehicle.id}')"
                    >
                        <i class="${vehicle.icon} mb-1 text-blue-500 text-lg"></i>
                        <div class="text-xs">${vehicle.name}</div>
                        <div class="text-xs text-gray-500">${vehicle.description}</div>
                    </button>
                `;
            });
            vehicleContainer.innerHTML = html;
        }

        function switchServiceType(type) {
            const buttons = document.querySelectorAll('.service-type-btn');
            buttons.forEach(btn => {
                if (btn.getAttribute('data-type') === type) {
                    btn.classList.remove('text-gray-500');
                    btn.classList.add('bg-blue-600', 'text-white');
                } else {
                    btn.classList.remove('bg-blue-600', 'text-white');
                    btn.classList.add('text-gray-500');
                }
            });
            updateVehicleTypes(type);
        }

        // 处理日期选择功能
        function toggleDatePicker(selectElement) {
            // 这里可以实现日历选择功能
            // 简单模拟：点击后显示/隐藏日历选择器
            console.log('日期选择器被点击');
            // 实际项目中可以集成第三方日期选择器
        }


    </script>
</head>

<body class="p-10 bg-gray-100 overflow-x-auto whitespace-nowrap">
    <!-- 登录页面 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col bg-white">
            <!-- 顶部Logo区域 -->
            <div class="flex-1 flex flex-col items-center justify-center p-6">
                <div class="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center mb-6">
                    <i class="fas fa-truck text-white text-4xl"></i>
                </div>
                <div class="text-2xl font-bold text-gray-800 mb-2">快运通</div>
                <div class="text-sm text-gray-500 mb-8">专业的物流运输平台</div>

                <!-- 登录表单 -->
                <div class="w-full space-y-4">
                    <div class="relative">
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <input type="tel" placeholder="请输入手机号"
                            class="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:border-blue-500">
                    </div>

                    <div class="relative">
                        <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            <i class="fas fa-lock"></i>
                        </div>
                        <input type="password" placeholder="请输入密码"
                            class="w-full pl-10 pr-4 py-3 border rounded-lg focus:outline-none focus:border-blue-500">
                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-500">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>

                    <div class="flex justify-between items-center text-sm">
                        <div class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-gray-600">记住密码</span>
                        </div>
                        <a href="#" class="text-blue-600">忘记密码？</a>
                    </div>

                    <button class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium text-lg">
                        登录
                    </button>

                    <div class="flex justify-center space-x-4 mt-4">
                        <a href="#" class="text-gray-500">
                            <i class="fab fa-weixin text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-500">
                            <i class="fab fa-qq text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-500">
                            <i class="fab fa-weibo text-xl"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 底部注册区域 -->
            <div class="p-4 border-t">
                <div class="flex justify-center items-center text-sm">
                    <span class="text-gray-500">还没有账号？</span>
                    <a href="#" class="text-blue-600 ml-1">立即注册</a>
                </div>
            </div>
        </div>
    </div>
    <!-- 首页 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col">
            <!-- 顶部导航 -->
            <div class="bg-white shadow-sm">
                <div class="p-3">
                    <div class="flex items-center justify-between">
                        <div class="font-bold text-lg tracking-wide text-gray-800">快运通</div>
                        <div class="flex space-x-4 text-gray-600">
                            <i class="fas fa-bell text-lg hover:text-blue-500 transition-colors"></i>
                            <i class="fas fa-user-circle text-lg hover:text-blue-500 transition-colors"></i>
                        </div>
                    </div>
                </div>
                <!-- 服务类型选择 -->
                <div class="px-3 py-2 border-b bg-white">
                    <div class="flex space-x-2 scrollbar-hide">
                        <a href="javascript:void(0)" onclick="switchServiceType('local')" data-type="local"
                            class="service-type-btn flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm transition-colors">
                            <i class="fas fa-truck-moving text-sm mr-1.5"></i>
                            <span>长途货运</span>
                        </a>
                        <a href="javascript:void(0)" onclick="switchServiceType('long')" data-type="long"
                            class="service-type-btn flex items-center px-4 py-2 text-gray-500 rounded-lg text-sm transition-colors">
                            <i class="fas fa-shipping-fast text-sm mr-1.5"></i>
                            <span>同城速运</span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="p-2 flex-1 overflow-y-auto bg-gray-50">
                <!-- 车型选择 -->
                <div class="px-2 py-3 bg-white rounded-t-xl">
                    <div class="grid grid-cols-4 gap-2 text-gray-600 text-sm vehicle-type-container"></div>
                    <script>updateVehicleTypes('local');</script>
                </div>

                <!-- 地址选择栏 -->
                <div class="p-2">
                    <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                        <!-- 当前位置 -->
                        <div class="flex items-center justify-between mb-5">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-50 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-map-marker-alt text-lg text-blue-500"></i>
                                </div>
                                <div>
                                    <div class="text-xs text-gray-500 mb-1">当前位置</div>
                                    <div class="font-medium text-gray-800 truncate max-w-[240px]">北京市海淀区中关村</div>
                                </div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                        <!-- 搜索框 -->
                        <div class="bg-gray-50 rounded-xl flex items-center p-3 text-gray-600">
                            <i class="fas fa-search text-blue-500 text-lg mr-2"></i>
                            <input type="text" placeholder="输入目的地，快速下单"
                                class="flex-1 outline-none bg-transparent placeholder-gray-400 text-sm">
                        </div>
                    </div>
                </div>

                <!-- 快捷功能入口 -->
                <div class="p-2">
                    <div class="bg-white rounded-xl p-3">
                        <div class="grid grid-cols-2 gap-3">
                            <a href="#"
                                class="flex items-center p-3 bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-user-plus text-blue-600 text-lg"></i>
                                </div>
                                <div>
                                    <div class="font-medium">司机加入</div>
                                    <div class="text-xs text-gray-500">成为专业车队</div>
                                </div>
                            </a>
                            <a href="#"
                                class="flex items-center p-3 bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors">
                                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                    <i class="fas fa-boxes text-blue-600 text-lg"></i>
                                </div>
                                <div>
                                    <div class="font-medium">物流发布</div>
                                    <div class="text-xs text-gray-500">快速发布货源</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 叫车流程说明 -->
                <div class="p-2">
                    <div class="bg-blue-50 rounded-xl p-3">
                        <div class="flex justify-between items-center mb-2">
                            <div class="font-bold text-gray-800 text-sm">叫车流程</div>
                            <a href="#" class="text-blue-600 text-xs">常见问题</a>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="step-item text-sm font-medium text-gray-700">1. 选车选址</div>
                            <div class="step-item text-sm font-medium text-gray-700">2. 填写要求</div>
                            <div class="step-item text-sm font-medium text-gray-700">3. 确认叫车</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航 -->
            <div class="flex justify-around items-center p-3 bg-white border-t">
                <div class="text-center text-blue-600">
                    <i class="fas fa-home block text-xl"></i>
                    <div class="text-xs mt-1">首页</div>
                </div>
                <div class="text-center text-gray-500">
                    <i class="fas fa-file-alt block text-xl"></i>
                    <div class="text-xs mt-1">订单</div>
                </div>
                <div class="text-center text-gray-500">
                    <i class="fas fa-comment block text-xl"></i>
                    <div class="text-xs mt-1">消息</div>
                </div>
                <div class="text-center text-gray-500">
                    <i class="fas fa-user block text-xl"></i>
                    <div class="text-xs mt-1">我的</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认装货地址 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col bg-white">
            <!-- 顶部导航 -->
            <div class="px-4 py-3 bg-white flex items-center justify-between shadow-sm">
                <div class="flex items-center">
                    <i class="fas fa-xmark text-gray-600"></i>
                    <div class="font-bold ml-4">确认地址</div>
                </div>
            </div>

            <!-- 地图区域 -->
            <div class="h-64 bg-gray-100 relative">
                <div class="absolute inset-0 flex items-center justify-center text-gray-400">
                    <i class="fas fa-map-marked-alt text-4xl"></i>
                </div>
                <!-- 地图中心标记 -->
                <div class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 text-red-500">
                    <i class="fas fa-map-marker-alt text-3xl"></i>
                </div>
            </div>

            <!-- 地址信息表单 -->
            <div class="flex-1 p-2">
                <div class="bg-white rounded-lg">
                    <!-- 当前位置信息 -->
                    <div class="p-2 border-b">
                        <div class="flex items-center justify-around">
                            <i class="fas fa-map-marker-alt text-lg text-blue-500"></i>
                            <div class="text-xs text-base text-gray-800 truncate">杭州市西湖区文三路 478
                                号华星时代广场</div>
                        </div>
                    </div>

                    <!-- 详细地址信息 -->
                    <div class="p-4 space-y-4">
                        <div class="flex items-center justify-between">
                            <label class="text-sm text-gray-500 w-40">门牌号(可选)</label>
                            <input type="text" placeholder="如8栋801"
                                class="flex-1 p-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="block text-sm text-gray-500 w-40">联系人(可选)</label>
                            <input type="text" placeholder="联系人"
                                class="flex-1 p-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="block text-sm text-gray-500 w-40">手机号码(可选)</label>
                            <input type="tel" placeholder="请输入联系电话"
                                class="flex-1 p-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>

                    </div>
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="p-4 bg-white border-t">
                <button
                    class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    确认装货地址
                </button>
            </div>
        </div>
    </div>
    <!-- 确认卸货地址 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col bg-white">
            <!-- 顶部导航 -->
            <div class="px-4 py-3 bg-white flex items-center justify-between shadow-sm">
                <div class="flex items-center">
                    <i class="fas fa-xmark text-gray-600"></i>
                    <div class="font-bold ml-4">确认地址</div>
                </div>
            </div>

            <!-- 地图区域 -->
            <div class="h-64 bg-gray-100 relative">
                <div class="absolute inset-0 flex items-center justify-center text-gray-400">
                    <i class="fas fa-map-marked-alt text-4xl"></i>
                </div>
                <!-- 地图中心标记 -->
                <div class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 text-red-500">
                    <i class="fas fa-map-marker-alt text-3xl"></i>
                </div>
            </div>

            <!-- 地址信息表单 -->
            <div class="flex-1 p-2">
                <div class="bg-white rounded-lg">
                    <!-- 当前位置信息 -->
                    <div class="p-2 border-b">
                        <div class="flex items-center justify-around">
                            <i class="fas fa-map-marker-alt text-lg text-blue-500"></i>
                            <div class="text-xs text-base text-gray-800 truncate">杭州市滨江区沿江路128
                                宝胜工业园</div>
                        </div>
                    </div>

                    <!-- 详细地址信息 -->
                    <div class="p-4 space-y-4">
                        <div class="flex items-center justify-between">
                            <label class="text-sm text-gray-500 w-40">门牌号(可选)</label>
                            <input type="text" placeholder="如8栋801"
                                class="flex-1 p-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="block text-sm text-gray-500 w-40">联系人(可选)</label>
                            <input type="text" placeholder="联系人"
                                class="flex-1 p-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="block text-sm text-gray-500 w-40">手机号码(可选)</label>
                            <input type="tel" placeholder="请输入联系电话"
                                class="flex-1 p-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>

                    </div>
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="p-4 bg-white border-t">
                <button
                    class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    确认卸货地址
                </button>
            </div>
        </div>
    </div>
    <!-- 确认下单页面 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col bg-white">
            <!-- 顶部导航 -->
            <div class="px-4 py-3 bg-white flex items-center justify-between shadow-sm">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>


                </div>
            </div>
            <div class="h-full flex flex-col bg-white overflow-y-auto">
                <!-- 装货时间选择 -->
                <div class="p-4 bg-white border-b">
                    <div class="relative">
                        <select class="w-full p-3 border rounded-lg pl-10" id="pickupTime1">
                            <option value="now" selected>立即装货</option>
                            <option value="today">今天 (2小时内)</option>
                            <option value="tomorrow-am">明天上午 (09:00-12:00)</option>
                            <option value="tomorrow-pm">明天下午 (14:00-18:00)</option>
                            <option value="custom">自定义时间</option>
                        </select>
                        <i class="far fa-calendar-alt absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-500 cursor-pointer"
                            onclick="toggleDatePicker(document.getElementById('pickupTime1'))"></i>
                    </div>
                </div>
                <!-- 已选车型展示 -->
                <div class="p-4 bg-white border-b">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-truck text-blue-500"></i>
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="text-sm text-gray-500">已选车型</div>
                            <div class="font-medium truncate">厢式货车 - 3.5吨</div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <!-- 地址选择区域 -->
                <div class="p-4 bg-white border-b">
                    <div class="flex items-center mb-4">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-map-marker-alt text-white"></i>
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="text-sm text-gray-500">起点</div>
                            <div class="font-medium truncate">顺艺帝字画装裱-莲花街</div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-location-dot text-white"></i>
                        </div>
                        <div class="ml-3 flex-1">
                            <div class="text-sm text-gray-500">终点</div>
                            <div class="font-medium truncate">闲品淘鲜-满觉陇路</div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <!-- 货物信息区域 -->
                <div class="p-4 bg-white border-b">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm text-gray-500 mb-1">货物类型</label>
                            <div class="relative">
                                <select class="w-full p-2.5 border rounded-lg appearance-none bg-white">

                                    <option value="furniture">家具家电</option>
                                    <option value="office">办公用品</option>
                                    <option value="personal">个人物品</option>
                                    <option value="fragile">易碎品</option>
                                    <option value="food">食品</option>
                                    <option value="other">其他</option>
                                </select>
                                <i
                                    class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-500 mb-1">货物重量</label>
                            <div class="relative flex items-center">
                                <input type="number" class="w-full p-2 border rounded-lg" placeholder="请输入重量">
                                <span class="absolute right-8 text-gray-500">吨</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 车型价格选择 -->
                <div class="flex-1 p-4">
                    <div class="space-y-0">
                        <!-- 拼车选项 -->
                        <div class="p-4 border rounded-lg flex items-center">
                            <input type="radio" name="service-type" class="w-5 h-5 text-blue-600">
                            <div class="ml-4 flex-1">
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">拼车</div>
                                    <div class="text-lg font-bold">¥75</div>
                                </div>
                                <div class="text-sm text-gray-500 mt-1">省心不急 拼车划算</div>
                            </div>
                        </div>

                        <!-- 快车选项 -->
                        <div class="p-4 border rounded-lg flex items-center bg-blue-50">
                            <input type="radio" name="service-type" class="w-5 h-5 text-blue-600" checked>
                            <div class="ml-4 flex-1">
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">快车</div>
                                    <div class="text-lg font-bold">¥107.29</div>
                                </div>
                                <div class="text-sm text-gray-500 mt-1">司机多 到达快</div>
                            </div>
                        </div>

                        <!-- 用户出价选项 -->
                        <div class="p-4 border rounded-lg flex items-center">
                            <input type="radio" name="service-type" class="w-5 h-5 text-blue-600">
                            <div class="ml-4 flex-1">
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">用户出价</div>
                                    <div class="text-lg font-bold text-gray-400">请出价</div>
                                </div>
                                <div class="text-sm text-gray-500 mt-1">灵活议价 价格可协商</div>
                            </div>
                        </div>

                        <!-- 特快选项 -->
                        <div class="p-4 border rounded-lg flex items-center">
                            <input type="radio" name="service-type" class="w-5 h-5 text-blue-600">
                            <div class="ml-4 flex-1">
                                <div class="flex justify-between items-center">
                                    <div class="font-medium">特快</div>
                                    <div class="text-lg font-bold">¥129.82</div>
                                </div>
                                <div class="text-sm text-gray-500 mt-1">急用车 速度最快</div>
                            </div>
                        </div>
                    </div>

                    <!-- 订单信息 -->
                    <div class="mt-6 space-y-4">
                        <div class="flex items-center border-b pb-4">
                            <i class="fas fa-file-alt text-gray-400 w-6"></i>
                            <input type="text" placeholder="订单备注（选填）" class="ml-3 flex-1 outline-none text-sm">
                        </div>
                        <div class="flex items-center border-b pb-4">
                            <i class="fas fa-phone text-gray-400 w-6"></i>
                            <input type="tel" value="18867105036" class="ml-3 flex-1 outline-none text-sm">
                        </div>
                    </div>
                </div>
            </div>
            <!-- 底部下单栏 -->
            <div class="p-4 bg-white border-t">

                <div class="flex items-center">
                    <div class="flex-1">
                        <div class="text-xl font-bold">¥107.29</div>
                        <div class="text-xs text-gray-500">已优惠¥5</div>
                    </div>
                    <button class="bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-medium">确认下单</button>
                </div>
            </div>


        </div>
    </div>
    <!-- 订单列表页面 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col"> <!-- 顶部导航 -->
            <div class="p-4 bg-white flex items-center justify-between border-b">
                <div class="font-bold">订单列表</div>
                <div class="flex items-center space-x-4"> <i class="fas fa-search text-gray-500"></i> </div>
            </div> <!-- 状态筛选 -->
            <div class="p-4 border-b bg-white">
                <div class="flex space-x-2"> <button
                        class="px-3 py-2 bg-blue-600 text-white rounded-full text-sm">全部</button> <button
                        class="px-3 py-2 text-gray-500 rounded-full text-sm">待接单</button> <button
                        class="px-3 py-2 text-gray-500 rounded-full text-sm">进行中</button> <button
                        class="px-3 py-2 text-gray-500 rounded-full text-sm">待支付</button> <button
                        class="px-3 py-2 text-gray-500 rounded-full text-sm">已完成</button> </div>
            </div> <!-- 订单列表 -->
            <div class="flex-1 overflow-y-auto bg-gray-50">
                <div class="p-4"> <!-- 订单卡片 -->
                    <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
                        <div class="p-4 border-b">
                            <div class="flex justify-between items-center mb-2">
                                <div class="text-sm text-gray-500">订单编号：#SR20240120001</div>
                                <div class="text-blue-600">待接单</div>
                            </div>
                            <div class="flex items-center mb-2">
                                <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                                <div class="text-sm">杭州市西湖区 → 杭州市滨江区</div>
                            </div>
                            <div class="text-sm text-gray-500">
                                <div class="flex items-center mb-1">
                                    <i class="fas fa-box mr-2"></i>
                                    <span>普通货物 · 2吨 · 5立方米</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-2"></i>
                                    <span>装货时间：2024-01-20 14:30</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-4 flex justify-between items-center">
                            <div class="text-xl font-bold">¥580</div>
                            <div class="space-x-2">
                                <button class="px-4 py-1 bg-blue-600 text-white rounded-full text-sm">查看详情</button>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
                        <div class="p-4 border-b">
                            <div class="flex justify-between items-center mb-2">
                                <div class="text-sm text-gray-500">订单编号：#SR20240120002</div>
                                <div class="text-green-600">进行中</div>
                            </div>
                            <div class="flex items-center mb-2">
                                <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                                <div class="text-sm">杭州市上城区 → 杭州市拱墅区</div>
                            </div>
                            <div class="text-sm text-gray-500">
                                <div class="flex items-center mb-1">
                                    <i class="fas fa-box mr-2"></i>
                                    <span>大件货物 · 3吨 · 8立方米</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-2"></i>
                                    <span>装货时间：2024-01-20 15:00</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-4 flex justify-between items-center">
                            <div class="text-xl font-bold">¥880</div>
                            <div class="space-x-2">
                                <button class="px-4 py-1 bg-blue-600 text-white rounded-full text-sm">订单跟踪</button>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
                        <div class="p-4 border-b">
                            <div class="flex justify-between items-center mb-2">
                                <div class="text-sm text-gray-500">订单编号：#SR20240120003</div>
                                <div class="text-red-500">待支付</div>
                            </div>
                            <div class="flex items-center mb-2">
                                <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                                <div class="text-sm">杭州市上城区 → 杭州市拱墅区</div>
                            </div>
                            <div class="text-sm text-gray-500">
                                <div class="flex items-center mb-1">
                                    <i class="fas fa-box mr-2"></i>
                                    <span>大件货物 · 3吨 · 8立方米</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-2"></i>
                                    <span>装货时间：2024-01-20 15:00</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-4 flex justify-between items-center">
                            <div class="text-xl font-bold">¥880</div>
                            <div class="space-x-2">
                                <button class="px-4 py-1 bg-blue-600 text-white rounded-full text-sm">立即支付</button>
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
                        <div class="p-4 border-b">
                            <div class="flex justify-between items-center mb-2">
                                <div class="text-sm text-gray-500">订单编号：#SR20240120004</div>
                                <div class="text-gray-600">已完成</div>
                            </div>
                            <div class="flex items-center mb-2">
                                <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                                <div class="text-sm">杭州市上城区 → 杭州市拱墅区</div>
                            </div>
                            <div class="text-sm text-gray-500">
                                <div class="flex items-center mb-1">
                                    <i class="fas fa-box mr-2"></i>
                                    <span>大件货物 · 3吨 · 8立方米</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-2"></i>
                                    <span>装货时间：2024-01-20 15:00</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-4 flex justify-between items-center">
                            <div class="text-xl font-bold">¥880</div>
                            <div class="space-x-2">
                                <button class="px-4 py-1 border rounded-full text-sm">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <!-- 底部导航 -->
            <div class="flex justify-around items-center p-3 bg-white border-t">
                <div class="text-center text-gray-500">
                    <i class="fas fa-home block text-xl"></i>
                    <div class="text-xs mt-1">首页</div>
                </div>
                <div class="text-center text-blue-600">
                    <i class="fas fa-file-alt block text-xl"></i>
                    <div class="text-xs mt-1">订单</div>
                </div>

                <div class="text-center text-gray-500">
                    <i class="fas fa-comment block text-xl"></i>
                    <div class="text-xs mt-1">消息</div>
                </div>
                <div class="text-center text-gray-500">
                    <i class="fas fa-user block text-xl"></i>
                    <div class="text-xs mt-1">我的</div>
                </div>
            </div>
        </div>
    </div>


    <!-- 订单跟踪页面 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col bg-gray-50">
            <!-- 顶部导航 -->
            <div class="px-4 py-3 bg-white flex items-center justify-between shadow-sm">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                    <div class="font-bold ml-4">订单跟踪</div>
                </div>
            </div>

            <!-- 订单信息 -->
            <div class="p-4 bg-blue-50">
                <div class="flex items-center justify-between mb-2">
                    <div class="text-sm text-gray-500">订单号：HLX20231225001</div>
                    <div class="text-sm text-blue-600">复制</div>
                </div>
                <div class="flex items-center justify-between">
                    <div class="font-bold text-lg text-green-600">运输中</div>
                    <div class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">预计14:30到达</div>
                </div>
            </div>

            <!-- 地图区域 -->
            <div class="h-40 bg-gray-200 relative overflow-hidden">
                <div class="absolute inset-0 flex items-center justify-center">
                    <i class="fas fa-map-marked-alt text-4xl text-gray-400"></i>
                </div>
                <div
                    class="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-white px-3 py-1 rounded-full shadow-sm text-sm">
                    <i class="fas fa-car text-blue-500 mr-1"></i> 实时跟踪
                </div>
            </div>

            <!-- 运输进度 -->
            <div class="flex-1 overflow-y-auto">
                <div class="p-4">
                    <div class="relative">
                        <!-- 时间轴 -->
                        <div class="absolute left-2 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                        <!-- 进度节点 -->
                        <div class="relative pl-8 pb-8">
                            <div class="absolute left-0 w-4 h-4 rounded-full bg-green-600 border-2 border-white">
                            </div>
                            <div class="mb-1">
                                <span class="text-blue-600 font-medium">司机已装货，开始配送</span>
                                <span class="text-xs text-gray-500">13:45</span>
                            </div>
                            <div class="text-sm text-gray-500 truncate">司机张师傅已从北京市海淀区中关村大街1号装货</div>
                        </div>

                        <div class="relative pl-8 pb-8">
                            <div class="absolute left-0 w-4 h-4 rounded-full bg-blue-600 border-2 border-white">
                            </div>
                            <div class="mb-1">
                                <span class="font-medium">司机已到达装货点</span>
                                <span class="text-xs text-gray-500">13:40</span>
                            </div>
                            <div class="text-sm text-gray-500">司机已到达装货地点，等待装货</div>
                        </div>

                        <div class="relative pl-8">
                            <div class="absolute left-0 w-4 h-4 rounded-full bg-blue-600 border-2 border-white">
                            </div>
                            <div class="mb-1">
                                <span class="font-medium">订单已接单</span>
                                <span class="text-xs text-gray-500">13:30</span>
                            </div>
                            <div class="text-sm text-gray-500">司机张师傅已接单</div>
                        </div>
                    </div>
                </div>

                <!-- 货物信息 -->
                <div class="p-4 bg-white border-b">
                    <div class="font-medium mb-3">货物信息</div>
                    <div class="space-y-3">
                        <div class="flex justify-between text-sm">
                            <div class="text-gray-500">货物类型</div>
                            <div>家具家电</div>
                        </div>
                        <div class="flex justify-between text-sm">
                            <div class="text-gray-500">货物重量</div>
                            <div>2吨</div>
                        </div>
                        <div class="flex justify-between text-sm">
                            <div class="text-gray-500">备注</div>
                            <div>轻拿轻放，易碎品</div>
                        </div>
                    </div>
                </div>

                <!-- 司机信息 -->
                <div class="p-4 bg-white mb-4">
                    <div class="font-bold mb-3">司机信息</div>
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-gray-200 overflow-hidden mr-3">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
                                class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-medium">张师傅</div>
                                    <div class="text-xs text-yellow-500">★★★★☆ 4.8</div>
                                </div>
                                <div class="text-blue-600">
                                    <i class="fas fa-phone-alt mr-4"></i>
                                    <i class="fas fa-comment"></i>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">
                                <i class="fas fa-car mr-1"></i> 金杯车 · 京A12345
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单匹配页面 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col bg-white">
            <!-- 顶部导航 -->
            <div class="px-4 py-3 bg-white flex items-center justify-between shadow-sm sticky top-0 z-10">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                    <div class="font-bold ml-4">订单详情</div>
                </div>
                <div class="flex items-center space-x-4">
                    <i class="fas fa-share-alt text-gray-500"></i>
                    <i class="fas fa-ellipsis-v text-gray-500"></i>
                </div>
            </div>

            <!-- 订单状态 -->
            <div class="flex-1 overflow-y-auto">
                <div class="p-4 bg-blue-50">
                    <div class="flex justify-between items-center mb-2">
                        <div class="text-sm text-gray-500">订单号：#SR20240120001</div>
                        <div class="text-sm text-blue-600">复制</div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="font-bold text-lg text-blue-600">待接单</div>
                        <div class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">已发布</div>
                    </div>
                </div>

                <!-- 运输信息 -->
                <div class="p-4 bg-white border-b">
                    <div class="flex items-center mb-3">
                        <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                        <div class="text-sm">杭州市西湖区 → 杭州市滨江区</div>
                    </div>
                    <div class="text-sm text-gray-500">
                        <div class="flex items-center mb-1">
                            <i class="fas fa-box mr-2"></i>
                            <span>普通货物 · 2吨 · 5立方米</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <span>装货时间：2024-01-20 14:30</span>
                        </div>
                    </div>
                </div>

                <!-- 车型信息 -->
                <div class="p-4 bg-white border-b">
                    <div class="font-medium mb-3">车型信息</div>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-truck text-blue-600 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium">厢式货车</div>
                            <div class="text-sm text-gray-500">载重3.5吨 · 长4.2米 · 宽2.1米 · 高2.1米</div>
                        </div>
                    </div>
                </div>

                <!-- 货物信息 -->
                <div class="p-4 bg-white border-b">
                    <div class="font-medium mb-3">货物信息</div>
                    <div class="space-y-3">
                        <div class="flex justify-between text-sm">
                            <div class="text-gray-500">货物类型</div>
                            <div>家具家电</div>
                        </div>
                        <div class="flex justify-between text-sm">
                            <div class="text-gray-500">货物重量</div>
                            <div>2吨</div>
                        </div>
                        <div class="flex justify-between text-sm">
                            <div class="text-gray-500">备注</div>
                            <div>轻拿轻放，易碎品</div>
                        </div>
                    </div>
                </div>

                <!-- 费用信息 -->
                <div class="p-4 bg-white border-b">
                    <div class="font-medium mb-3">费用信息</div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <div class="text-gray-500">基础运费</div>
                            <div>¥580.00</div>
                        </div>
                    </div>
                </div>

            </div>
            <!-- 底部按钮 -->
            <div class="p-4 bg-white border-t">
                <div class="flex space-x-3">
                    <button class="flex-1 border-2 border-blue-500 py-2.5 rounded-lg font-medium">
                        取消订单
                    </button>
                    <button class="flex-1 bg-blue-600 text-white py-2.5 rounded-lg font-medium">
                        修改订单
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 已完成订单详情页面 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col bg-white">
            <!-- 顶部导航 - 固定 -->
            <div class="px-4 py-3 bg-white flex items-center justify-between shadow-sm sticky top-0 z-10">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                    <div class="font-bold  ml-4">订单详情</div>
                </div>
                <div class="flex items-center space-x-4">
                    <i class="fas fa-share-alt text-gray-500"></i>
                    <i class="fas fa-ellipsis-v text-gray-500"></i>
                </div>
            </div>

            <!-- 中间内容区域 - 可滚动 -->
            <div class="flex-1 overflow-y-auto">
                <!-- 订单状态 -->
                <div class="p-4 bg-blue-50">
                    <div class="flex justify-between items-center mb-2">
                        <div class="text-sm text-gray-500">订单号：#WL20240120001</div>
                        <div class="text-sm text-blue-600">复制</div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="font-bold text-lg text-gray-600">已完成</div>
                        <div class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">2024-02-20 14:30送达</div>
                    </div>
                </div>

                <!-- 运输信息 -->
                <div class="p-4 bg-white border-b">
                    <div class="flex items-center mb-3">
                        <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                        <div class="text-sm">杭州市西湖区 → 杭州市滨江区</div>
                    </div>
                    <div class="text-sm text-gray-500">
                        <div class="flex items-center mb-1">
                            <i class="fas fa-box mr-2"></i>
                            <span>普通货物 · 2吨 · 5立方米</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <span>装货时间：2024-01-20 14:30</span>
                        </div>
                    </div>
                </div>

                <!-- 司机信息 -->
                <div class="p-4 bg-white border-b">
                    <div class="font-medium mb-3">司机信息</div>
                    <div class="flex items-center">
                        <div class="w-12 h-12 rounded-full bg-gray-200 overflow-hidden mr-3">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
                                class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="font-medium">张师傅</div>
                                    <div class="text-xs text-yellow-500">★★★★☆ 4.8</div>
                                </div>
                                <div class="text-blue-600">
                                    <i class="fas fa-phone-alt mr-4"></i>
                                    <i class="fas fa-comment"></i>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">
                                <i class="fas fa-car mr-1"></i>
                                金杯车 · 浙A12345
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 费用信息 -->
                <div class="p-4 bg-white border-b">
                    <div class="font-medium mb-3">费用信息</div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <div class="text-gray-500">基础运费</div>
                            <div>¥580.00</div>
                        </div>
                        <div class="flex justify-between text-sm">
                            <div class="text-gray-500">优惠券</div>
                            <div class="text-green-600">-¥50.00</div>
                        </div>
                        <div class="flex justify-between text-sm pt-2 border-t">
                            <div class="font-medium">实付金额</div>
                            <div class="font-bold text-lg">¥530.00</div>
                        </div>
                    </div>
                </div>

                <!-- 订单信息 -->
                <div class="p-4 bg-white border-b">
                    <div class="font-medium mb-3">订单信息</div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <div class="text-gray-500">下单时间</div>
                            <div>2024-01-20 13:30</div>
                        </div>
                        <div class="flex justify-between">
                            <div class="text-gray-500">支付方式</div>
                            <div>支付宝</div>
                        </div>
                        <div class="flex justify-between">
                            <div class="text-gray-500">支付时间</div>
                            <div>2024-01-20 13:35</div>
                        </div>
                        <div class="flex justify-between">
                            <div class="text-gray-500">订单备注</div>
                            <div>轻拿轻放，易碎品</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 订单支付页面 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col bg-white">
            <!-- 顶部导航 -->
            <div class="px-4 py-3 bg-white flex items-center justify-between shadow-sm">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                    <div class="font-bold ml-4">订单支付</div>
                </div>
            </div>

            <!-- 支付金额 -->
            <!-- <div class="p-6 bg-blue-50">
                <div class="text-center">
                    <div class="text-sm text-gray-500 mb-2">支付金额</div>
                    <div class="text-3xl font-bold">¥580.00</div>
                </div>
            </div> -->

            <!-- 订单概要信息 -->
            <div class="p-4 bg-white border-b">
                <div class="flex items-center justify-between mb-3">
                    <div class="text-sm text-gray-500">订单号：#WL20240120001</div>
                    <div class="text-sm text-blue-600">复制</div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <div class="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                        <div class="text-sm">杭州市西湖区 → 杭州市滨江区</div>
                    </div>
                    <div class="text-sm text-gray-500">
                        <div class="flex items-center mb-1">
                            <i class="fas fa-box mr-2"></i>
                            <span>普通货物 · 2吨 · 5立方米</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-clock mr-2"></i>
                            <span>装货时间：2024-01-20 14:30</span>
                        </div>
                    </div>
                    <div class="flex items-center justify-between pt-2 border-t">
                        <div class="text-sm text-gray-500">运费</div>
                        <div class="text-lg font-bold">¥580.00</div>
                    </div>
                </div>
            </div>

            <!-- 支付方式 -->
            <div class="flex-1 overflow-y-auto p-4">
                <div class="bg-white rounded-lg border">
                    <div class="p-4 border-b">
                        <div class="font-medium">选择支付方式</div>
                    </div>
                    <div class="divide-y">
                        <div class="p-4 flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fab fa-alipay text-blue-500 text-xl mr-3"></i>
                                <div>
                                    <div class="font-medium">支付宝</div>
                                    <div class="text-sm text-gray-500">推荐使用支付宝支付</div>
                                </div>
                            </div>
                            <input type="radio" name="payment" class="w-5 h-5 text-blue-600" checked>
                        </div>
                        <div class="p-4 flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fab fa-weixin text-green-500 text-xl mr-3"></i>
                                <div>
                                    <div class="font-medium">微信支付</div>
                                    <div class="text-sm text-gray-500">推荐使用微信支付</div>
                                </div>
                            </div>
                            <input type="radio" name="payment" class="w-5 h-5 text-blue-600">
                        </div>
                    </div>
                </div>

                <!-- 优惠券 -->
                <!-- <div class="mt-4 bg-white rounded-lg border">
                    <div class="p-4 flex items-center justify-between">
                        <div class="font-medium">优惠券</div>
                        <div class="flex items-center text-blue-600">
                            <span class="text-sm mr-1">选择优惠券</span>
                            <i class="fas fa-chevron-right text-sm"></i>
                        </div>
                    </div>
                </div> -->
            </div>

            <!-- 底部按钮 -->
            <div class="p-4 bg-white border-t">
                <button class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium">
                    确认支付 ¥580.00
                </button>
            </div>
        </div>
    </div>
    <!-- 消息通知中心页面 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col bg-white">
            <!-- 顶部导航 -->
            <div class="px-4 py-3 bg-white flex items-center justify-between shadow-sm">
                <div class="font-bold text-lg">消息中心</div>
                <div class="flex items-center space-x-4">
                    <i class="fas fa-ellipsis-v text-gray-500"></i>
                </div>
            </div>

            <!-- 消息类型选择 -->
            <div class="px-4 py-2 bg-white border-b">
                <div class="flex space-x-2 overflow-x-auto">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-full text-sm">全部</button>
                    <button class="px-4 py-2 text-gray-500 rounded-full text-sm">系统通知</button>
                    <button class="px-4 py-2 text-gray-500 rounded-full text-sm">订单消息</button>
                    <button class="px-4 py-2 text-gray-500 rounded-full text-sm">活动消息</button>
                </div>
            </div>

            <!-- 消息列表 -->
            <div class="flex-1 overflow-y-auto">
                <div class="p-4">
                    <!-- 系统通知 -->
                    <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden border">
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <div
                                        class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-bell text-blue-600"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">系统通知</div>
                                        <div class="text-xs text-gray-500">2024-01-20 14:30</div>
                                    </div>
                                </div>
                                <div class="w-2 h-2 rounded-full bg-red-500"></div>
                            </div>
                            <div class="text-sm text-gray-600 ml-13">
                                您的账户已完成实名认证，现在可以使用更多功能。
                            </div>
                        </div>
                    </div>

                    <!-- 订单消息 -->
                    <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden border">
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <div
                                        class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-truck text-green-600"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">订单消息</div>
                                        <div class="text-xs text-gray-500">2024-01-20 13:45</div>
                                    </div>
                                </div>
                                <div class="w-2 h-2 rounded-full bg-red-500"></div>
                            </div>
                            <div class="text-sm text-gray-600 ml-13">
                                您的订单 #WL20240120001 已被司机接单，预计14:30送达。
                            </div>
                        </div>
                    </div>

                    <!-- 活动消息 -->
                    <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden border">
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <div
                                        class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-gift text-yellow-600"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">活动消息</div>
                                        <div class="text-xs text-gray-500">2024-01-20 10:00</div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 ml-13">
                                新用户专享优惠：首单立减50元，限时3天！
                            </div>
                            <div class="mt-3 flex justify-end">
                                <button class="px-4 py-1 bg-yellow-500 text-white rounded-full text-sm">
                                    立即领取
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 系统维护通知 -->
                    <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden border">
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <div
                                        class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-tools text-purple-600"></i>
                                    </div>
                                    <div>
                                        <div class="font-medium">系统维护通知</div>
                                        <div class="text-xs text-gray-500">2024-01-19 22:00</div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600 ml-13">
                                系统将于2024年1月20日凌晨2:00-4:00进行例行维护，期间可能影响部分功能使用。
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 底部导航 -->
            <div class="flex justify-around items-center p-3 bg-white border-t">
                <div class="text-center text-gray-500">
                    <i class="fas fa-home block text-xl"></i>
                    <div class="text-xs mt-1">首页</div>
                </div>
                <div class="text-center text-gray-500">
                    <i class="fas fa-file-alt block text-xl"></i>
                    <div class="text-xs mt-1">订单</div>
                </div>
                <div class="text-center text-blue-600">
                    <i class="fas fa-comment block text-xl"></i>
                    <div class="text-xs mt-1">消息</div>
                </div>
                <div class="text-center text-gray-500">
                    <i class="fas fa-user block text-xl"></i>
                    <div class="text-xs mt-1">我的</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 个人中心页面 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col">
            <!-- 顶部个人信息 -->
            <div class="p-4 bg-blue-600 text-white">
                <div class="flex items-center">
                    <div class="w-14 h-14 rounded-full bg-white overflow-hidden mr-3">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80"
                            class="w-full h-full object-cover">
                    </div>
                    <div>
                        <div class="font-bold text-lg mb-0.5">张三</div>
                        <div class="text-xs opacity-80">普通会员</div>
                    </div>
                    <div class="ml-auto">
                        <i class="fas fa-qrcode text-xl"></i>
                    </div>
                </div>
                <div class="flex justify-between mt-4 text-center">
                    <div>
                        <div class="text-xl font-bold">0</div>
                        <div class="text-xs opacity-80">积分</div>
                    </div>
                    <div>
                        <div class="text-xl font-bold">￥258</div>
                        <div class="text-xs opacity-80">钱包</div>
                    </div>
                    <div>
                        <div class="text-xl font-bold">89</div>
                        <div class="text-xs opacity-80">订单</div>
                    </div>
                </div>
            </div>

            <!-- 功能列表 -->
            <div class="flex-1 overflow-y-auto">
                <div class="p-3">
                    <div class="bg-white rounded-lg overflow-hidden">
                        <div class="divide-y">
                            <div class="p-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-address-card text-blue-600 w-5"></i>
                                    <span class="ml-2">地址管理</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>

                            <div class="p-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-share-alt text-blue-600 w-5"></i>
                                    <span class="ml-2">分享应用</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="p-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-calculator text-blue-600 w-5"></i>
                                    <span class="ml-2">收费标准</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="p-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-cog text-blue-600 w-5"></i>
                                    <span class="ml-2">通用设置</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="p-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-headset text-blue-600 w-5"></i>
                                    <span class="ml-2">联系客服</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            <div class="p-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-sync text-blue-600 w-5"></i>
                                    <span class="ml-2">检查更新</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-xs text-gray-500 mr-2">v1.0.0</span>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            </div>
                            <div class="p-3 flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-eject text-blue-600 w-5"></i>
                                    <span class="ml-2">关于</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航 -->
            <div class="flex justify-around items-center p-3 bg-white border-t">
                <div class="text-center text-gray-500">
                    <i class="fas fa-home block text-xl"></i>
                    <div class="text-xs mt-1">首页</div>
                </div>
                <div class="text-center text-gray-500">
                    <i class="fas fa-file-alt block text-xl"></i>
                    <div class="text-xs mt-1">订单</div>
                </div>
                <div class="text-center text-gray-500">
                    <i class="fas fa-comment block text-xl"></i>
                    <div class="text-xs mt-1">消息</div>
                </div>
                <div class="text-center text-blue-600">
                    <i class="fas fa-user block text-xl"></i>
                    <div class="text-xs mt-1">我的</div>
                </div>
            </div>
        </div>
    </div>
    <!-- 地址簿管理页面 -->
    <div class="mockup-frame">

        <div class="h-full flex flex-col" id="addressSelectPage">
            <div class="h-full flex flex-col bg-white">
                <!-- 顶部导航 -->
                <div class="px-4 py-3 bg-white flex items-center justify-between shadow-sm">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-left text-gray-600 text-lg"></i>
                        <div class="font-medium text-lg ml-4">地址簿</div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <i class="fas fa-share-alt text-gray-600 text-lg"></i>
                        <i class="fas fa-ellipsis-v text-gray-600 text-lg"></i>
                    </div>
                </div>

                <!-- 搜索栏 -->
                <div class="p-3 bg-white sticky top-0 z-10">
                    <div class="bg-gray-50 rounded-lg flex items-center p-3">
                        <i class="fas fa-search text-gray-400 text-base ml-1.5 mr-2"></i>
                        <input type="text" placeholder="搜索地址、姓名、电话、标签等"
                            class="flex-1 outline-none text-base bg-transparent placeholder-gray-400">
                    </div>
                </div>

                <!-- 地址列表 -->
                <div class="flex-1 overflow-y-auto">
                    <div class="divide-y">
                        <!-- 地址项 -->
                        <div class="p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex justify-between items-center">
                                <div class="flex-1 min-w-0 mr-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-user-circle text-blue-500 mr-2"></i>
                                        <div class="font-medium text-base text-gray-800">张珊</div>
                                    </div>
                                    <div class="text-sm text-gray-500 mt-1 truncate">杭州市-西湖街道-龙井路与清宵路交叉路口</div>
                                </div>
                                <div class="flex items-center space-x-2 shrink-0">
                                    <button class="p-1.5 text-blue-500 transition-colors">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                    <button class="p-1.5 text-blue-500 transition-colors">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="p-1.5 text-green-500 transition-colors">
                                        <i class="fas fa-check-circle"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- 地址项 -->
                        <div class="p-4">
                            <div class="flex justify-between items-center">
                                <div class="flex-1 min-w-0 mr-3">
                                    <div class="flex items-center">
                                        <i class="fas fa-user-circle text-blue-500 mr-2"></i>
                                        <div class="font-medium text-base text-gray-800">王世超</div>
                                    </div>
                                    <div class="text-sm text-gray-500 mt-1 truncate">杭州市-长河街道-长河街道乐和时代2栋202,207</div>
                                </div>
                                <div class="flex items-center space-x-2 shrink-0">
                                    <button class="p-1.5 text-blue-500 transition-colors">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                    <button class="p-1.5 text-blue-500 transition-colors">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="p-1.5 text-green-500 transition-colors">
                                        <i class="fas fa-check-circle"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="p-4 bg-white border-t flex space-x-4">

                    <button
                        class="flex-1 py-3 bg-blue-600 text-white rounded-lg font-medium transition-colors text-base shadow-lg">
                        新增地址
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 地址编辑页面 -->
    <div class="mockup-frame">
        <div class="h-full flex flex-col bg-white">
            <!-- 顶部导航 -->
            <div class="px-4 py-3 bg-white flex items-center justify-between shadow-sm">
                <div class="flex items-center">
                    <i class="fas fa-arrow-left text-gray-600"></i>
                    <div class="font-medium text-lg ml-4">编辑地址</div>
                </div>
            </div>

            <!-- 地址信息表单 -->
            <div class="flex-1 p-4 overflow-y-auto">
                <div class="space-y-4">
                    <!-- 地址选择区域 -->
                    <div class="bg-white rounded-lg border p-4 space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="font-medium text-base text-gray-800 truncate max-w-[280px]">杭州市西湖区文三路 478
                                号华星时代广场</div>
                            <div class="flex items-center text-blue-600 cursor-pointer">
                                <span class="text-sm mr-1">选择地址</span>
                                <i class="fas fa-chevron-right text-sm"></i>
                            </div>
                        </div>

                    </div>

                    <!-- 联系人信息 -->
                    <div class="bg-white rounded-lg border p-4 space-y-4">
                        <div class="flex items-center justify-between">
                            <label class="text-sm text-gray-500 w-20">联系人</label>
                            <input type="text" placeholder="请输入联系人姓名"
                                class="flex-1 p-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="text-sm text-gray-500 w-20">手机号码</label>
                            <input type="tel" placeholder="请输入联系电话"
                                class="flex-1 p-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>
                        <div class="flex items-center justify-between">
                            <label class="text-sm text-gray-500 w-20">备注</label>
                            <input type="text" placeholder="如：公司、家"
                                class="flex-1 p-2 border rounded-lg focus:outline-none focus:border-blue-500">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="p-4 bg-white border-t">
                <button
                    class="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    保存地址
                </button>
            </div>
        </div>
    </div>



</body>

</html>