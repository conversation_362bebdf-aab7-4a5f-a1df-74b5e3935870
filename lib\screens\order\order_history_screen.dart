//订单状态历史记录页面，展示订单的完整状态变更历史
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/constants/order_status_constants.dart';
import 'package:hyt/view_models/order_view_model.dart';
import 'package:provider/provider.dart';
import 'package:hyt/l10n/app_localizations.dart';
import 'package:hyt/data/models/order.dart';
import 'package:hyt/data/models/order_status_history.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:intl/intl.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/styles/app_theme.dart';

class OrderHistoryScreen extends StatelessWidget {
  final String orderId;

  const OrderHistoryScreen({super.key, required this.orderId});

  @override
  Widget build(BuildContext context) {
    final orderViewModel = Provider.of<OrderViewModel>(context);
    final order = orderViewModel.getOrderById(orderId);
    final statusHistory = orderViewModel.getOrderStatusHistory(orderId);

    return Scaffold(
      appBar: AppBar(
        title: const Text('订单状态历史'),
        centerTitle: true,
        elevation: 0,
      ),
      body: Column(
        children: [
          // 订单基本信息卡片
          _buildOrderInfoCard(context, order as Order),

          // 状态历史时间轴
          Expanded(
            child: statusHistory.isEmpty
                ? const Center(child: Text('暂无状态历史记录'))
                : _buildStatusTimeline(
                    statusHistory.cast<OrderStatusHistory>()),
          ),
        ],
      ),
    );
  }

  // 订单基本信息卡片
  Widget _buildOrderInfoCard(BuildContext context, Order order) {
    return AppCard(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '订单号: ${order.orderId}',
            style: AppTextStyles.withWeight(
                AppTextStyles.headline4, FontWeight.bold),
          ),
          SizedBox(height: AppInsets.gap8()),
          Row(
            children: [
              Icon(Icons.access_time,
                  size: 16.sp,
                  color: Theme.of(context).colorScheme.onSurfaceVariant),
              SizedBox(width: AppInsets.gapW4()),
              Text(
                '创建时间: ${DateFormat('yyyy-MM-dd HH:mm').format(order.createTime)}',
                style: AppTextStyles.bodySmall.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant),
              ),
            ],
          ),
          SizedBox(height: AppInsets.gap4()),
          Row(
            children: [
              Icon(Icons.local_shipping,
                  size: 16.sp,
                  color: Theme.of(context).colorScheme.onSurfaceVariant),
              SizedBox(width: AppInsets.gapW4()),
              Text(
                '${AppLocalizations.of(context)?.currentStatus ?? '当前状态'}: ${OrderStatusConstants.getStatusNameLocalized(order.statusCode, context)}',
                style: AppTextStyles.bodySmall.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 状态历史时间轴
  Widget _buildStatusTimeline(List<OrderStatusHistory> statusHistory) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      itemCount: statusHistory.length,
      itemBuilder: (context, index) {
        final history = statusHistory[index];
        final isLast = index == statusHistory.length - 1;

        // 根据状态代码设置颜色
        Color statusColor;
        switch (history.statusCode) {
          case 0: // 待接单
            statusColor = Theme.of(context).colorScheme.tertiary;
            break;
          case 1: // 进行中
            statusColor = Theme.of(context).colorScheme.primary;
            break;
          case 2: // 已完成
            statusColor = Theme.of(context).colorScheme.secondary;
            break;
          case 3: // 已取消
            statusColor = Theme.of(context).colorScheme.error;
            break;
          default:
            statusColor = Theme.of(context).colorScheme.outlineVariant;
        }

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                Container(
                  width: 24.w,
                  height: 24.w,
                  decoration: BoxDecoration(
                    color: statusColor,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.check,
                      color: Theme.of(context).colorScheme.onPrimary,
                      size: 16.sp),
                ),
                if (!isLast)
                  Container(
                    width: 2.w,
                    height: 60.h,
                    color: Theme.of(context).colorScheme.outlineVariant,
                  ),
              ],
            ),
            SizedBox(width: AppInsets.gapW16()),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    OrderStatusConstants.getStatusNameLocalized(
                        history.statusCode, context),
                    style: AppTextStyles.withWeight(
                        AppTextStyles.bodyLarge, FontWeight.bold),
                  ),
                  SizedBox(height: AppInsets.gap4()),
                  Text(
                    DateFormat('yyyy-MM-dd HH:mm').format(history.timestamp),
                    style: AppTextStyles.bodySmall.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant),
                  ),
                  if (history.remark != null && history.remark!.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: 8.h, bottom: 16.h),
                      child: Container(
                        padding: EdgeInsets.all(12.w),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          history.remark!,
                          style: AppTextStyles.bodyMedium,
                        ),
                      ),
                    ),
                  SizedBox(height: isLast ? 0 : 16.h),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
