import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hyt/data/models/order.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:intl/intl.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/l10n/app_localizations.dart';
import 'package:hyt/styles/app_theme.dart';

class OrderCard extends StatelessWidget {
  final Order order;
  final String role; // 'shipper' 或 'driver'
  final VoidCallback? onViewDetail;
  final List<Widget>? actionButtons;
  final bool compact; // 新增参数

  const OrderCard({
    super.key,
    required this.order,
    required this.role,
    this.onViewDetail,
    this.actionButtons,
    this.compact = true,
  });

  @override
  Widget build(BuildContext context) {
    final Color statusColor = order.getStatusColor(context); // 获取状态颜色
    return GestureDetector(
      onTap: onViewDetail,
      child: AppCard(
        margin: EdgeInsets.only(bottom: AppInsets.gap6()),
        padding: EdgeInsets.zero,
        borderRadius: 12,
        elevation: 0,
        outlineAlpha: 0.6,
        child: compact
            ? _buildCompactContent(context, statusColor)
            : _buildFullContent(context, statusColor),
      ),
    );
  }

  // 紧凑模式内容
  Widget _buildCompactContent(BuildContext context, Color statusColor) {
    return Padding(
      padding: AppInsets.h16().add(AppInsets.v12()),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on,
                  color: Theme.of(context).colorScheme.primary, size: 16.sp),
              SizedBox(width: 4.w),
              Expanded(
                child: Text(
                  '${order.pickupLocationName} → ${order.deliveryLocationName}',
                  style: AppTextStyles.bodyMedium
                      .copyWith(fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Icon(Icons.access_time, size: 14.sp, color: Colors.grey[500]),
              SizedBox(width: 4.w),
              Text(
                DateFormat('MM-dd HH:mm').format(order.createTime),
                style:
                    AppTextStyles.labelSmall.copyWith(color: Colors.grey[600]),
              ),
              const Spacer(),
              Text(
                '${order.price}元',
                style: AppTextStyles.headline4.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 原有完整内容
  Widget _buildFullContent(BuildContext context, Color statusColor) {
    return Padding(
      padding: AppInsets.h16().add(AppInsets.v12()),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '${AppLocalizations.of(context)?.orderNo ?? '订单号'}: ${order.orderId}',
                style: AppTextStyles.labelSmall,
              ),
              const Spacer(),
              Text(
                order.getStatusText(context),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: statusColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          _buildAddressRow(
              context,
              AppLocalizations.of(context)?.loadingAddress ?? '装',
              order.pickupLocationName,
              order.pickupAddress),
          _buildAddressConnector(),
          _buildAddressRow(
              context,
              AppLocalizations.of(context)?.unloadingAddress ?? '卸',
              order.deliveryLocationName,
              order.deliveryAddress),
          SizedBox(height: 4.h),
          _buildTimeAndPrice(context),
          if (actionButtons != null && actionButtons!.isNotEmpty) ...[
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: actionButtons!,
            ),
          ]
        ],
      ),
    );
  }

  Widget _buildAddressConnector() {
    return Padding(
      padding: EdgeInsets.only(left: 10.w, top: 2.h, bottom: 2.h),
      child: SizedBox(
        height: 10.h,
        child: VerticalDivider(
          width: 1,
          thickness: 2,
          color: Colors.grey[500],
        ),
      ),
    );
  }

  Widget _buildAddressRow(
      BuildContext context, String type, String name, String addressDetail) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: 20.w,
          height: 20.w,
          decoration: BoxDecoration(
            color: type == '装'
                ? Theme.of(context).colorScheme.primary
                : Colors.green,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              type,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        SizedBox(width: AppInsets.gapW12()),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (name.isNotEmpty)
                Text(
                  name,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              if (addressDetail.isNotEmpty)
                Text(
                  addressDetail,
                  style: AppTextStyles.labelSmall,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTimeRow(IconData icon, String label, DateTime time) {
    return Row(
      children: [
        Icon(icon, size: 11.sp, color: Colors.grey[600]),
        SizedBox(width: 4.w),
        Text(
          '$label: ${DateFormat('yyyy-MM-dd HH:mm').format(time)}',
          style: AppTextStyles.labelSmall.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildTimeAndPrice(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTimeRow(
                  FontAwesomeIcons.calendarDays, '下单时间', order.createTime),
            ],
          ),
        ),
        Text(
          '${order.price}元',
          style: AppTextStyles.headline4.copyWith(
            color: Theme.of(context).colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
