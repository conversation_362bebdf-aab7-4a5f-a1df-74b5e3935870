//认证包装器，用于根据用户角色导航到不同页面
import 'package:flutter/material.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:hyt/screens/auth/login_screen.dart';
import 'package:hyt/screens/home/<USER>';
import 'package:hyt/screens/driver/driver_nav_screen.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  @override
  void initState() {
    super.initState();
    checkLocationPermission();
  }

  void checkLocationPermission() async {
    var status = await Permission.location.status;
    if (!status.isGranted) {
      await Permission.location.request();
    }
  }

  @override
  Widget build(BuildContext context) {
    final appUserViewModel = context.read<AppUserViewModel>();

    return FutureBuilder(
        future: appUserViewModel.loadUserFromSharedPrefs(),
        builder: (context, snapshot) {
          if (snapshot.connectionState != ConnectionState.done) {
            //TODO：通过splahscreen显示加载中更合适，将来完成
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }
          if (!appUserViewModel.isLoggedIn) {
            return const LoginScreen();
          }
          if (appUserViewModel.isLoggedIn &&
              appUserViewModel.currentUser != null) {
            // 根据当前角色导航到不同主页
            switch (appUserViewModel.currentRole) {
              case 'driver':
                return const DriverNavScreen();
              default:
                return const UserNavScreen();
            }
          }
          return const LoginScreen();
        });
  }
}
