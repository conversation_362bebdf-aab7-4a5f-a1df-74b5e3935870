//登录页面
import 'package:bcrypt/bcrypt.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/data/models/appuser.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:provider/provider.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../constants/routes.dart';
import '../../utils/validators.dart';
import '../../utils/snackbar_utils.dart';
import '../../l10n/app_localizations.dart';
import '../../widgets/unified_button.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  bool _obscurePassword = true;
  bool _rememberPassword = false;
  bool _isRegisterMode = false; // 是否为注册模式
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _phoneController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = Provider.of<AppUserViewModel>(context);
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // 顶部Logo区域
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.w),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(height: 60.h),
                        // Logo
                        Container(
                          width: 100.w,
                          height: 100.w,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.local_shipping,
                            color: Colors.white,
                            size: 50.sp,
                          ),
                        ),
                        SizedBox(height: 24.h),
                        // 应用名称
                        Text(
                          AppLocalizations.of(context)?.appName ?? '快运通',
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        SizedBox(height: 8.h),
                        // 应用描述
                        Text(
                          AppLocalizations.of(context)?.appDescription ??
                              '专业的物流运输平台',
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        SizedBox(height: 40.h),
                        // 手机号输入框
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: TextFormField(
                            controller: _phoneController,
                            keyboardType: TextInputType.phone,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return AppLocalizations.of(context)
                                        ?.phoneRequired ??
                                    '请输入手机号';
                              }
                              if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                                return AppLocalizations.of(context)
                                        ?.phoneInvalid ??
                                    '请输入有效的手机号';
                              }
                              return null;
                            },
                            decoration: InputDecoration(
                              hintText:
                                  AppLocalizations.of(context)?.phoneHint ??
                                      '请输入手机号',
                              border: InputBorder.none,
                              prefixIcon: const Icon(
                                Icons.phone_android,
                                color: Colors.grey,
                                size: 20,
                              ),
                              contentPadding:
                                  EdgeInsets.symmetric(vertical: 16.h),
                            ),
                          ),
                        ),
                        SizedBox(height: 16.h),
                        if (_isRegisterMode) SizedBox(height: 16.h),
                        if (_isRegisterMode)
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: TextFormField(
                              controller: _nameController,
                              validator: _isRegisterMode
                                  ? (value) {
                                      if (value == null || value.isEmpty) {
                                        return AppLocalizations.of(context)
                                                ?.nicknameRequired ??
                                            '请输入昵称';
                                      }
                                      if (value.length < 2) {
                                        return AppLocalizations.of(context)
                                                ?.nicknameTooShort ??
                                            '昵称长度不能少于2位';
                                      }
                                      return null;
                                    }
                                  : null,
                              decoration: InputDecoration(
                                hintText: AppLocalizations.of(context)
                                        ?.nicknameHint ??
                                    '请输入昵称',
                                border: InputBorder.none,
                                prefixIcon: Icon(
                                  Icons.person_outline,
                                  color: Colors.grey,
                                  size: 20.sp,
                                ),
                                contentPadding:
                                    EdgeInsets.symmetric(vertical: 16.h),
                              ),
                            ),
                          ),
                        SizedBox(height: 16.h),
                        // 密码输入框
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: TextFormField(
                            controller: _passwordController,
                            obscureText: _obscurePassword,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return AppLocalizations.of(context)
                                        ?.passwordRequired ??
                                    '请输入密码';
                              }
                              if (value.length < 6) {
                                return AppLocalizations.of(context)
                                        ?.passwordTooShort ??
                                    '密码长度不能少于6位';
                              }
                              if (_isRegisterMode &&
                                  !RegExp(r'[a-zA-Z]').hasMatch(value)) {
                                return AppLocalizations.of(context)
                                        ?.passwordNeedLetter ??
                                    '密码需要包含字母';
                              }
                              if (_isRegisterMode &&
                                  !RegExp(r'\d').hasMatch(value)) {
                                return AppLocalizations.of(context)
                                        ?.passwordNeedNumber ??
                                    '密码需要包含数字';
                              }
                              return null;
                            },
                            decoration: InputDecoration(
                              hintText: _isRegisterMode
                                  ? (AppLocalizations.of(context)
                                          ?.passwordHintRegister ??
                                      '密码需包含字母和数字')
                                  : (AppLocalizations.of(context)
                                          ?.passwordHint ??
                                      '请输入密码'),
                              border: InputBorder.none,
                              prefixIcon: Icon(
                                Icons.lock_outline,
                                color: Colors.grey,
                                size: 20.sp,
                              ),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  _obscurePassword
                                      ? Icons.visibility_off
                                      : Icons.visibility,
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 20.sp,
                                ),
                                onPressed: () {
                                  setState(() {
                                    _obscurePassword = !_obscurePassword;
                                  });
                                },
                              ),
                              contentPadding:
                                  EdgeInsets.symmetric(vertical: 16.h),
                            ),
                          ),
                        ),
                        SizedBox(height: 16.h),
                        // 记住密码和忘记密码
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                SizedBox(
                                  width: 24.w,
                                  height: 24.w,
                                  child: Checkbox(
                                    value: _rememberPassword,
                                    onChanged: (value) {
                                      setState(() {
                                        _rememberPassword = value ?? false;
                                      });
                                    },
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                  ),
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  AppLocalizations.of(context)
                                          ?.rememberPassword ??
                                      '记住密码',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                            UnifiedButton.text(
                              text: AppLocalizations.of(context)
                                      ?.forgotPassword ??
                                  '忘记密码？',
                              onPressed: () {
                                // 忘记密码逻辑
                              },
                              size: UnifiedButtonSize.small,
                            ),
                          ],
                        ),
                        SizedBox(height: 24.h),
                        // 登录/注册按钮
                        UnifiedButton.elevated(
                          text: _isRegisterMode
                              ? (AppLocalizations.of(context)?.register ?? '注册')
                              : (AppLocalizations.of(context)?.login ?? '登录'),
                          onPressed: viewModel.isLoading
                              ? null
                              : () {
                                  if (_isRegisterMode) {
                                    _register(context);
                                  } else {
                                    _login(context);
                                  }
                                },
                          size: UnifiedButtonSize.large,
                          isFullWidth: true,
                          isLoading: viewModel.isLoading,
                        ),
                        SizedBox(height: 24.h),
                        // 社交媒体登录
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            _socialLoginButton(FontAwesomeIcons.weixin),
                            SizedBox(width: 32.w),
                            _socialLoginButton(FontAwesomeIcons.qq),
                            SizedBox(width: 32.w),
                            _socialLoginButton(FontAwesomeIcons.weibo),
                          ],
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 16.h),
                          decoration: BoxDecoration(
                            border: Border(
                              top: BorderSide(
                                color: Colors.grey[300]!,
                                width: 1,
                              ),
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                _isRegisterMode
                                    ? (AppLocalizations.of(context)
                                            ?.alreadyHaveAccount ??
                                        '已有账号？')
                                    : (AppLocalizations.of(context)
                                            ?.noAccount ??
                                        '还没有账号？'),
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.grey,
                                ),
                              ),
                              UnifiedButton.text(
                                text: _isRegisterMode
                                    ? (AppLocalizations.of(context)
                                            ?.backToLogin ??
                                        '返回登录')
                                    : (AppLocalizations.of(context)
                                            ?.registerNow ??
                                        '立即注册'),
                                onPressed: () {
                                  setState(() {
                                    _isRegisterMode = !_isRegisterMode;
                                  });
                                },
                                size: UnifiedButtonSize.small,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // 底部注册区域
            )
          ],
        ),
      ),
    );
  }

  Widget _socialLoginButton(IconData icon) {
    return InkWell(
      onTap: () {
        // 社交媒体登录逻辑
      },
      child: Icon(
        icon,
        size: 24.sp,
        color: Colors.grey,
      ),
    );
  }

  void _login(BuildContext context) async {
    // 使用表单验证
    if (!_formKey.currentState!.validate()) {
      return;
    }
    try {
      final appUserViewModel = context.read<AppUserViewModel>();
      final response = await appUserViewModel.login(
        _phoneController.text,
        _passwordController.text,
      );
      if (!response) {
        if (context.mounted) {
          SnackBarUtils.showErrorSnackBar(
              context, AppLocalizations.of(context)?.loginFailed ?? '登录失败');
        }
        return;
      }

      if (context.mounted) {
        // 登录成功
        Navigator.pushReplacementNamed(context, Routes.authWrapper);
      }
    } catch (e) {
      debugPrint('!登录失败: $e');
      if (context.mounted) {
        SnackBarUtils.showErrorSnackBar(context, '登录失败: ${e.toString()}');
      }
    } finally {}
  }

  // 注册方法
  void _register(BuildContext context) async {
    // 使用表单验证
    if (!_formKey.currentState!.validate()) {
      return;
    }
    // 额外验证手机号格式
    if (!Validators.isValidPhone(_phoneController.text)) {
      SnackBarUtils.showErrorSnackBar(context,
          AppLocalizations.of(context)?.phoneInvalidFormat ?? '请输入有效的手机号');
      return;
    }

    // 额外验证密码强度
    if (!Validators.isStrongPassword(_passwordController.text)) {
      SnackBarUtils.showErrorSnackBar(
          context,
          AppLocalizations.of(context)?.passwordStrengthRequirement ??
              '密码需要至少6位，包含字母和数字');
      return;
    }
    try {
      final viewModel = context.read<AppUserViewModel>();
      // 对密码进行哈希处理
      final hashedPassword =
          BCrypt.hashpw(_passwordController.text, BCrypt.gensalt());

      final user = AppUser(
        phone: _phoneController.text,
        passwordHash: hashedPassword,
      );
      final success = await viewModel.register(user);

      if (success && context.mounted) {
        // 注册成功后，确保更新AppUserProvider的登录状态
        final appUserViewModel = context.read<AppUserViewModel>();
        appUserViewModel.setUser(viewModel.currentUser!); //设置用户信息通知状态变化

        // 注册并登录成功，显示成功提示
        SnackBarUtils.showSuccessSnackBar(
            context, AppLocalizations.of(context)?.registerSuccess ?? '注册成功');
        // 导航到AuthWrapper处理后续路由
        Navigator.pushReplacementNamed(context, Routes.authWrapper);
      }
    } catch (e) {
      if (context.mounted) {
        // 注册失败
        SnackBarUtils.showErrorSnackBar(context,
            '${AppLocalizations.of(context)?.registerFailed ?? '注册失败'}: ${e.toString()}');
      }
    } finally {}
  }
}
