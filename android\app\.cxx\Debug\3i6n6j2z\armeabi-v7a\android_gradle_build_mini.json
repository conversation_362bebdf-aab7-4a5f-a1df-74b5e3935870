{"buildFiles": ["D:\\Android\\flutter_windows_3.29.0-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Android\\workspace\\hyt\\android\\app\\.cxx\\Debug\\3i6n6j2z\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Android\\workspace\\hyt\\android\\app\\.cxx\\Debug\\3i6n6j2z\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}