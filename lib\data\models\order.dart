import 'package:flutter/material.dart';
import 'package:hyt/utils/num_utils.dart';
import '../../constants/order_status_constants.dart';
import '../../utils/timezone_utils.dart';

//订单实体模型
class Order {
  final String? id; // 消息唯一标识符，可为空，数据库自动生成
  final String orderId; // 订单ID
  int statusCode; //订单状态
  final String pickupLocationName; // 装货地点名称
  final String pickupAddress; // 装货详细地址
  final String deliveryLocationName; // 卸货地点名称
  final String deliveryAddress; // 卸货详细地址
  final String? goodsType;
  final String? weight;
  final String? volume;
  final DateTime createTime;
  final double price;
  final String customerID;
  final String customerName;
  final String customerPhone;
  final String? remark; // 备注可能为空
  final String serviceType;
  final DateTime loadingTime;
  DateTime? cancelTime; // 取消时间
  String? cancelReason; // 取消原因
  String? cancelledBy; // 取消发起方：'shipper' 或 'driver'
  double? cancelFee; // 取消费用
  final String vehicleType;

  // 新增字段，支持司机视角
  String? driverId; // 接单司机ID
  String? driverName; // 司机姓名
  String? driverPhone; // 司机电话
  String? vehiclePlate; // 车牌号
  bool isUrgent; // 是否加急订单
  double? driverEarnings; // 司机收入
  double? platformFee; // 平台抽成
  DateTime? acceptTime; // 预计到达时间
  DateTime? estimatedArrivalTime; // 预计到达时间
  DateTime? actualArrivalTime; // 实际到达时间
  DateTime? completionTime; // 完成时间
  bool isDeletedByCustomer; // 是否被货主删除（逻辑删除，需要可变）
  bool isDeletedByDriver; // 是否被司机删除（逻辑删除，需要可变）

  Order({
    this.id,
    required this.orderId,
    required this.statusCode,
    required this.pickupLocationName,
    required this.pickupAddress,
    required this.deliveryLocationName,
    required this.deliveryAddress,
    this.goodsType,
    this.weight,
    this.volume,
    required this.createTime,
    required this.price,
    required this.customerID,
    required this.customerName,
    required this.customerPhone,
    this.remark,
    required this.serviceType,
    required this.loadingTime,
    required this.vehicleType,
    // 新增字段
    this.driverId,
    this.driverName,
    this.driverPhone,
    this.vehiclePlate,
    this.isUrgent = false,
    this.driverEarnings,
    this.platformFee,
    this.acceptTime,
    this.estimatedArrivalTime,
    this.actualArrivalTime,
    this.completionTime,
    this.cancelTime,
    this.cancelReason,
    this.cancelledBy,
    this.cancelFee,
    this.isDeletedByCustomer = false, // 默认未被删除
    this.isDeletedByDriver = false, // 默认未被删除
  });

  // 从Map创建Order对象
  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      orderId: map['order_id'] ?? 'N/A',
      statusCode: map['status_code'] ?? -1,
      pickupLocationName: map['pickup_location_name'] ?? '未知地点',
      pickupAddress: map['pickup_address'] ?? '未知地址',
      deliveryLocationName: map['delivery_location_name'] ?? '未知地点',
      deliveryAddress: map['delivery_address'] ?? '未知地址',
      goodsType: map['goods_type'] ?? '未知类型',
      weight: map['weight'] ?? '未知重量',
      volume: map['volume'] ?? '未知体积',
      createTime:
          TimezoneUtils.parseFromDatabase(map['create_time']) ?? DateTime.now(),
      price: safeToDouble(map['price']),
      customerID: map['customer_id'] ?? 'N/A',
      customerName: map['customer_name'] ?? 'N/A',
      customerPhone: map['customer_phone'] ?? 'N/A',
      remark: map['remark'],
      serviceType: map['service_type'] ?? '未知服务',
      loadingTime: TimezoneUtils.parseFromDatabase(map['loading_time']) ??
          DateTime.now(),
      vehicleType: map['vehicle_type'] ?? 'N/A',
      // 新增字段
      driverId: map['driver_id'],
      driverName: map['driver_name'],
      driverPhone: map['driver_phone'],
      vehiclePlate: map['vehicle_plate'],
      isUrgent: map['is_urgent'] ?? false,
      driverEarnings: map['driver_earnings'] != null
          ? safeToDouble(map['driver_earnings'])
          : null,
      platformFee: map['platform_fee'] != null
          ? safeToDouble(map['platform_fee'])
          : null,
      isDeletedByCustomer:
          map['is_deleted_by_customer'] ?? false, // 添加是否被货主删除字段
      isDeletedByDriver: map['is_deleted_by_driver'] ?? false, // 添加是否被司机删除字段
      acceptTime: TimezoneUtils.parseFromDatabase(map['accept_time']),
      estimatedArrivalTime:
          TimezoneUtils.parseFromDatabase(map['estimated_arrival_time']),
      actualArrivalTime:
          TimezoneUtils.parseFromDatabase(map['actual_arrival_time']),
      completionTime: TimezoneUtils.parseFromDatabase(map['completion_time']),
      cancelTime: TimezoneUtils.parseFromDatabase(map['cancel_time']),
      cancelReason: map['cancel_reason'],
      cancelledBy: map['cancelled_by'],
      cancelFee:
          map['cancel_fee'] != null ? safeToDouble(map['cancel_fee']) : null,
    );
  }

  // 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'order_id': orderId,
      'status_code': statusCode,
      'pickup_location_name': pickupLocationName,
      'pickup_address': pickupAddress,
      'delivery_location_name': deliveryLocationName,
      'delivery_address': deliveryAddress,
      'goods_type': goodsType,
      'weight': weight,
      'volume': volume,
      'create_time': TimezoneUtils.toDatabase(createTime),
      'price': price,
      'customer_id': customerID,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'remark': remark,
      'service_type': serviceType,
      'loading_time': TimezoneUtils.toDatabase(loadingTime),
      'vehicle_type': vehicleType,
      'driver_id': driverId,
      'driver_name': driverName,
      'driver_phone': driverPhone,
      'vehicle_plate': vehiclePlate,
      'is_urgent': isUrgent,
      'driver_earnings': driverEarnings,
      'platform_fee': platformFee,
      'accept_time':
          acceptTime != null ? TimezoneUtils.toDatabase(acceptTime!) : null,
      'estimated_arrival_time': estimatedArrivalTime != null
          ? TimezoneUtils.toDatabase(estimatedArrivalTime!)
          : null,
      'actual_arrival_time': actualArrivalTime != null
          ? TimezoneUtils.toDatabase(actualArrivalTime!)
          : null,
      'completion_time': completionTime != null
          ? TimezoneUtils.toDatabase(completionTime!)
          : null,
      'cancel_time':
          cancelTime != null ? TimezoneUtils.toDatabase(cancelTime!) : null,
      'cancel_reason': cancelReason,
      'cancelled_by': cancelledBy,
      'cancel_fee': cancelFee,
      'is_deleted_by_customer': isDeletedByCustomer,
      'is_deleted_by_driver': isDeletedByDriver,
    };
  }

  // 获取订单状态文本
  String getStatusText(BuildContext context) {
    return OrderStatusConstants.getStatusNameLocalized(statusCode, context);
  }

  // 获取订单状态颜色
  Color getStatusColor(BuildContext context) {
    return OrderStatusConstants.getStatusColor(statusCode, context);
  }

  // 获取订单状态图标
  IconData getStatusIcon() {
    return OrderStatusConstants.getStatusIcon(statusCode);
  }

  // 获取订单状态描述
  String getStatusDescription(BuildContext context) {
    return OrderStatusConstants.getStatusDescriptionLocalized(
        statusCode, context);
  }

  // 获取订单状态进度
  double getStatusProgress() {
    return OrderStatusConstants.getStatusProgress(statusCode);
  }
}
