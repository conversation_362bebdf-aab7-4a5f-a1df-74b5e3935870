import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/styles/app_theme.dart';

class AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  final EdgeInsetsGeometry? margin;
  final double elevation;
  final double borderRadius;
  final Color borderColor;
  final Color? shadowColor;
  final double? outlineAlpha; // 统一描边透明度，可控
  final Duration? animationDuration; // 预留动画参数

  const AppCard({
    super.key,
    required this.child,
    this.padding,
    this.color,
    this.margin,
    this.elevation = 0,
    this.borderRadius = 12,
    this.borderColor = const Color(0xFFE0E0E0),
    this.shadowColor,
    this.animationDuration,
    this.outlineAlpha = 0.6,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: margin,
      color: color ?? Theme.of(context).colorScheme.surface,
      elevation: elevation,
      surfaceTintColor: Colors.transparent,
      clipBehavior: Clip.antiAlias,
      shadowColor: shadowColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius.r),
        side: BorderSide(
          color: borderColor == const Color(0xFFE0E0E0)
              ? Theme.of(context)
                  .colorScheme
                  .outlineVariant
                  .withValues(alpha: (outlineAlpha ?? 1.0))
              : borderColor,
        ),
      ),
      child: Padding(
        padding: padding ?? AppInsets.h16().add(AppInsets.v12()),
        child: child,
      ),
    );
  }
}
