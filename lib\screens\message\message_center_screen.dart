//消息中心页面，货主和司机共用页面
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/data/models/base_message.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/utils/enums.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:hyt/view_models/message_view_model.dart';

import 'package:provider/provider.dart';
import 'package:hyt/widgets/empty_state.dart';
import 'package:hyt/widgets/error_state.dart';
import '../../l10n/app_localizations.dart';

import 'package:hyt/widgets/unified_confirm_dialog.dart';
import 'package:hyt/utils/snackbar_utils.dart';
import 'package:hyt/widgets/search_app_bar.dart';
import 'package:intl/intl.dart';
import 'package:hyt/styles/app_theme.dart';

class MessageCenterScreen extends StatefulWidget {
  const MessageCenterScreen({super.key});

  @override
  MessageCenterScreenState createState() => MessageCenterScreenState();
}

class MessageCenterScreenState extends State<MessageCenterScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late MessageViewModel _messageViewModel;
  late AppUserViewModel _appUserViewModel;

  final TextEditingController _searchController = TextEditingController();

  // 获取消息类型列表的方法
  List<String> get messageTypes => [
        AppLocalizations.of(context)!.all,
        MessageType.system.displayName(context),
        MessageType.order.displayName(context),
        MessageType.activity.displayName(context)
      ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // 初始化ViewModel实例
    _messageViewModel = context.read<MessageViewModel>();
    _appUserViewModel = context.read<AppUserViewModel>();

    // 进入页面时自动刷新一次（需要已登录用户）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final userId = _appUserViewModel.currentUser?.id;
      if (userId != null && userId.isNotEmpty) {
        _refreshMessages();
      }
    });
  }

  /// 下拉刷新方法
  Future<void> _refreshMessages() async {
    await _messageViewModel.syncPendingOperations();
    await _messageViewModel
        .refreshAllMessages(_appUserViewModel.currentUser?.id ?? '');
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final vm = context.watch<MessageViewModel>();
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: SearchAppBar(
        searchController: _searchController,
        tabController: _tabController,
        tabTitles: messageTypes,
        showSearchField: false,
        titleText: AppLocalizations.of(context)!.messageCenter,
        centerTitle: false,
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        showTabs: true,
        tabLabelStyle:
            AppTextStyles.withWeight(AppTextStyles.bodyMedium, FontWeight.bold),
        tabUnselectedLabelStyle: AppTextStyles.bodyMedium,
        tabIndicatorWeight: 3,
        tabIsScrollable: true,
        tabAlignment: TabAlignment.center,
        actions: [
          // 操作菜单按钮
          Consumer<MessageViewModel>(
            builder: (context, messageViewModel, child) {
              if (messageViewModel.allMessages.isEmpty) {
                return const SizedBox.shrink();
              }
              return PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert),
                onSelected: (value) {
                  final appUserViewModel = context.read<AppUserViewModel>();
                  switch (value) {
                    case 'mark_all_read':
                      if (messageViewModel.unreadCount > 0) {
                        messageViewModel
                            .markAllAsReadLocal(appUserViewModel.currentUser!);
                      }
                      break;
                    case 'delete_all':
                      _showDeleteAllConfirmDialog(
                          context, messageViewModel, appUserViewModel);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  if (messageViewModel.unreadCount > 0)
                    PopupMenuItem<String>(
                      value: 'mark_all_read',
                      child: Row(
                        children: [
                          const Icon(Icons.done_all, size: 20),
                          SizedBox(width: 8.w),
                          Text(AppLocalizations.of(context)!.markAllAsRead),
                        ],
                      ),
                    ),
                  PopupMenuItem<String>(
                    value: 'delete_all',
                    child: Row(
                      children: [
                        Icon(Icons.delete_sweep,
                            size: 20,
                            color: Theme.of(context).colorScheme.error),
                        SizedBox(width: 8.w),
                        Text(
                          AppLocalizations.of(context)!.deleteAll,
                          style: AppTextStyles.bodyMedium.copyWith(
                              color: Theme.of(context).colorScheme.error),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: SafeArea(
        child: vm.isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // 消息列表区域
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _refreshMessages,
                      child: TabBarView(
                        controller: _tabController,
                        children: messageTypes
                            .map((type) => _buildMessageList(type))
                            .toList(),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildMessageList(String type) {
    final vm = context.watch<MessageViewModel>();

    if (vm.error != null) {
      return ErrorState(message: vm.error!, onRetry: _refreshMessages);
    }
    final messages = context.select<MessageViewModel, List<BaseMessage>>((vm) {
      if (type == AppLocalizations.of(context)!.all) {
        return vm.allMessages;
      } else if (type == MessageType.system.displayName(context)) {
        return vm.getMessagesByType(MessageType.system);
      } else if (type == MessageType.order.displayName(context)) {
        return vm.getMessagesByType(MessageType.order);
      } else {
        return vm.getMessagesByType(MessageType.activity);
      }
    });

    return messages.isEmpty
        ? ListView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: AppInsets.pageGutter()
                .add(EdgeInsets.only(top: AppInsets.gap2())),
            children: [
              SizedBox(height: AppInsets.gap4()),
              EmptyState(
                useCard: false,
                title: AppLocalizations.of(context)?.noMessages ?? '暂无消息',
              ),
            ],
          )
        : ListView.separated(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: AppInsets.pageGutter()
                .add(EdgeInsets.only(top: AppInsets.gap2())),
            itemCount: messages.length,
            separatorBuilder: (context, index) => Divider(
              height: 1,
              thickness: 1,
              color: Theme.of(context).colorScheme.outlineVariant,
            ),
            itemBuilder: (context, index) {
              if (index >= messages.length) return const SizedBox.shrink();
              final message = messages[index];
              return _buildMessageTile(message);
            },
          );
  }

  Widget _buildMessageTile(BaseMessage message) {
    final isUnread = !message.isRead;
    final scheme = Theme.of(context).colorScheme;

    return InkWell(
      onTap: () async {
        if (isUnread) {
          _messageViewModel.markAsReadLocal(
            message.id ?? '',
            _appUserViewModel.currentUser!,
          );
        }
        await showConfirmDialog(
          context,
          title: AppLocalizations.of(context)?.messageDetail ?? '消息详情',
          content: message.content,
          okText: AppLocalizations.of(context)!.close,
        );
      },
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 10.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 左侧类型图标或未读点
            Padding(
              padding: EdgeInsets.only(top: 2.h),
              child: Icon(
                message.type.icon,
                size: 18.sp,
                color: isUnread ? scheme.primary : scheme.onSurfaceVariant,
              ),
            ),
            SizedBox(width: AppInsets.gapW12()),

            // 标题 + 摘要
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          message.type.displayName(context),
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight:
                                isUnread ? FontWeight.w600 : FontWeight.w400,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: AppInsets.gapW12()),
                      Text(
                        DateFormat('MM-dd HH:mm').format(message.createdAt),
                        style: AppTextStyles.labelSmall.copyWith(
                          color: scheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    message.content,
                    style: AppTextStyles.bodySmall.copyWith(
                      color:
                          isUnread ? scheme.onSurface : scheme.onSurfaceVariant,
                      height: 1.15,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),

            // 删除按钮
            IconButton(
              icon:
                  Icon(Icons.delete_outline, size: 18.sp, color: scheme.error),
              onPressed: () {
                _messageViewModel.deleteMessageLocal(
                  message.id ?? '',
                  _appUserViewModel.currentUser!,
                );
              },
              visualDensity: VisualDensity.compact,
              padding: EdgeInsets.zero,
              constraints: BoxConstraints(minWidth: 28.w, minHeight: 28.h),
              tooltip: AppLocalizations.of(context)?.delete ?? '删除',
            ),
          ],
        ),
      ),
    );
  }

  // 显示删除全部消息确认对话框
  Future<void> _showDeleteAllConfirmDialog(
    BuildContext context,
    MessageViewModel messageViewModel,
    AppUserViewModel appUserViewModel,
  ) async {
    final ok = await showConfirmDialog(
      context,
      title: AppLocalizations.of(context)!.confirmDeleteAll,
      content: AppLocalizations.of(context)!.deleteAllMessage,
    );
    if (!context.mounted) return;
    if (ok) {
      messageViewModel.deleteAllMessagesLocal(appUserViewModel.currentUser!);
      SnackBarUtils.showSuccessSnackBar(
          context, AppLocalizations.of(context)?.deleteAllSuccess ?? '已删除所有消息');
    }
  }
}
