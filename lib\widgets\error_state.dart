import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/l10n/app_localizations.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/styles/app_theme.dart';

class ErrorState extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final EdgeInsetsGeometry? padding;

  const ErrorState(
      {super.key, required this.message, this.onRetry, this.padding});

  @override
  Widget build(BuildContext context) {
    final scheme = Theme.of(context).colorScheme;
    final content = Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.error_outline, size: 48.sp, color: scheme.error),
        SizedBox(height: 12.h),
        Text(AppLocalizations.of(context)?.errorTitle ?? '出错了',
            style: Theme.of(context).textTheme.titleMedium),
        SizedBox(height: 4.h),
        Text(
          message,
          textAlign: TextAlign.center,
          style: Theme.of(context)
              .textTheme
              .bodyMedium
              ?.copyWith(color: scheme.onSurfaceVariant),
        ),
        if (onRetry != null) ...[
          SizedBox(height: 16.h),
          FilledButton(
            onPressed: onRetry,
            child: Text(AppLocalizations.of(context)?.retry ?? '重试'),
          )
        ]
      ],
    );

    return AppCard(
      margin: EdgeInsets.zero,
      padding: padding ?? AppInsets.h16().add(AppInsets.v12()),
      borderRadius: 12,
      elevation: 0,
      outlineAlpha: 0.6,
      child: Center(child: content),
    );
  }
}
