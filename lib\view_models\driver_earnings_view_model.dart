import 'package:hyt/data/models/driver_earnings.dart';
import 'package:hyt/data/repos/driver_earnings_repo.dart';
import 'package:hyt/view_models/base_view_model.dart';
import 'package:hyt/utils/log_utils.dart';

/// 司机收入统计视图模型
class DriverEarningsViewModel extends BaseViewModel {
  final DriverEarningsRepo _earningsRepo;
  DriverEarnings? _earnings;

  DriverEarningsViewModel(this._earningsRepo);

  /// 获取收入统计数据
  DriverEarnings? get earnings => _earnings;

  /// 加载司机收入统计
  Future<void> loadDriverEarnings(String driverId) async {
    try {
      setLoading(true);
      clearError();

      _earnings = await _earningsRepo.getDriverEarnings(driverId);
    } catch (e, st) {
      setError('加载收入统计失败: $e');
      LogUtils.e(error!, e, st);
      _earnings = null;
    } finally {
      setLoading(false);
    }
  }
}
