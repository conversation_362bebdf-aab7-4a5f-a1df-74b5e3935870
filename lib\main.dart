import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/data/repos/appuser_repo_supabase.dart';
import 'package:hyt/data/repos/base_message_repo_supabase.dart';
import 'package:hyt/data/repos/message_status_repo_supabase.dart';
import 'package:hyt/data/repos/order_repo_supabase.dart';
import 'package:hyt/data/repos/driver_earnings_repo_supabase.dart';
import 'package:hyt/view_models/address_view_model.dart';
import 'package:hyt/view_models/message_view_model.dart';
import 'package:hyt/view_models/profile_view_model.dart';
import 'package:hyt/view_models/vehicle_info_view_model.dart';
import 'package:hyt/view_models/driver_earnings_view_model.dart';
import 'package:hyt/screens/driver/driver_order_pool_screen.dart';
import 'package:hyt/screens/driver/driver_order_history_screen.dart';
import 'package:hyt/screens/order/order_detail_screen.dart';
import 'package:hyt/screens/payment/payment_screen.dart';
import 'package:hyt/data/models/order.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:hyt/view_models/order_view_model.dart';
import 'package:hyt/view_models/address_search_view_model.dart';
import 'package:provider/provider.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hyt/services/notification_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'screens/auth/auth_wrapper.dart';
import 'screens/auth/login_screen.dart';
import 'screens/home/<USER>';
import 'screens/driver/driver_nav_screen.dart';
import 'screens/settings/settings_screen.dart';
import 'constants/routes.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
// 国际化相关导入
import 'providers/language_provider.dart';
import 'l10n/app_localizations.dart';
import 'styles/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env");
  // 初始化Supabase
  await Supabase.initialize(
    url: dotenv.get('SUPABASE_URL'),
    anonKey: dotenv.get('SUPABASE_API_KEY'),
  );

  // SharedPreferences不需要初始化

  // 初始化通知服务
  final notificationService = NotificationService();
  // 设置消息仓库
  final baseMessageRepo = BaseMessageRepoSupabase(Supabase.instance.client);
  notificationService.setMessageRepo(baseMessageRepo);
  await notificationService.init();

  runApp(
    MultiProvider(
      providers: [
        // 语言提供者
        ChangeNotifierProvider(create: (_) => LanguageProvider()),

        ChangeNotifierProvider(create: (_) => VehicleInfoViewModel()),
        // 数据层
        ChangeNotifierProvider(create: (_) => AddressViewModel()),
        Provider(create: (_) => AppUserRepoSupabase(Supabase.instance.client)),
        Provider(
            create: (_) => BaseMessageRepoSupabase(Supabase.instance.client)),
        Provider(
            create: (_) => MessageStatusRepoSupabase(Supabase.instance.client)),
        Provider(create: (_) => OrderRepoSupabase(Supabase.instance.client)),
        Provider(
            create: (_) =>
                DriverEarningsRepoSupabase(Supabase.instance.client)),
        // ViewModel
        ChangeNotifierProxyProvider<AppUserRepoSupabase, AppUserViewModel>(
          create: (context) =>
              AppUserViewModel(context.read<AppUserRepoSupabase>()),
          update: (context, repo, previous) =>
              previous ?? AppUserViewModel(repo),
        ),
        ChangeNotifierProxyProvider2<OrderRepoSupabase, BaseMessageRepoSupabase,
            OrderViewModel>(
          create: (context) => OrderViewModel(context.read<OrderRepoSupabase>(),
              context.read<BaseMessageRepoSupabase>()),
          update: (context, orderRepo, messageRepo, previous) =>
              previous ?? OrderViewModel(orderRepo, messageRepo),
        ),
        ChangeNotifierProxyProvider<DriverEarningsRepoSupabase,
            DriverEarningsViewModel>(
          create: (context) => DriverEarningsViewModel(
              context.read<DriverEarningsRepoSupabase>()),
          update: (context, repo, previous) =>
              previous ?? DriverEarningsViewModel(repo),
        ),
        ChangeNotifierProxyProvider<AddressViewModel, AddressSearchViewModel>(
          create: (context) =>
              AddressSearchViewModel(context.read<AddressViewModel>()),
          update: (context, addressViewModel, previous) =>
              previous ?? AddressSearchViewModel(addressViewModel),
        ),
        ChangeNotifierProxyProvider2<BaseMessageRepoSupabase,
            MessageStatusRepoSupabase, MessageViewModel>(
          create: (context) => MessageViewModel(
              context.read<BaseMessageRepoSupabase>(),
              context.read<MessageStatusRepoSupabase>()),
          update: (context, baseRepo, statusRepo, previous) =>
              previous ?? MessageViewModel(baseRepo, statusRepo),
        ),
        ChangeNotifierProxyProvider2<AppUserViewModel, OrderRepoSupabase,
            ProfileViewModel>(
          create: (context) => ProfileViewModel(
              context.read<AppUserViewModel>(),
              context.read<OrderRepoSupabase>()),
          update: (context, appUserVM, orderRepo, previous) =>
              previous ?? ProfileViewModel(appUserVM, orderRepo),
        ),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return Consumer<LanguageProvider>(
          builder: (context, languageProvider, child) {
            return MaterialApp(
              title: '快运通',
              debugShowCheckedModeBanner: false, // 移除 debug 标签

              // 国际化配置
              locale: languageProvider.currentLocale,
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: AppLocalizations.supportedLocales,

              theme: buildLightTheme(),
              darkTheme: buildDarkTheme(),
              initialRoute: Routes.authWrapper,
              routes: {
                Routes.authWrapper: (context) => const AuthWrapper(),
                Routes.login: (context) => const LoginScreen(),
                Routes.home: (context) => const UserNavScreen(),
                Routes.driverHome: (context) => const DriverNavScreen(),
                Routes.settings: (context) => const SettingsScreen(),
                Routes.orderPool: (context) => const DriverOrderPoolScreen(),
                Routes.orderHistory: (context) =>
                    const DriverOrderHistoryScreen(),
                Routes.orderDetail: (context) => OrderDetailScreen(
                      order:
                          ModalRoute.of(context)!.settings.arguments as Order,
                    ),
                Routes.payment: (context) => PaymentScreen(
                      order:
                          ModalRoute.of(context)!.settings.arguments as Order,
                    ),
              },
              builder: EasyLoading.init(),
            );
          },
        );
      },
    );
  }
}
