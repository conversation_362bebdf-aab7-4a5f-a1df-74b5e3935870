//确认装货或者卸货地址页面，装货地址确认后返回，卸货地址确认后跳转到确认下单页面
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/view_models/address_search_view_model.dart';
import 'package:provider/provider.dart';
import 'package:hyt/screens/order/order_confirm_screen.dart';
import 'package:hyt/view_models/address_view_model.dart';
import 'package:hyt/screens/home/<USER>';
import 'package:hyt/widgets/app_card.dart';

class AddressConfirmScreen extends StatefulWidget {
  final bool isLoading; // true为装货地址，false为卸货地址

  const AddressConfirmScreen({
    super.key,
    required this.isLoading,
  });

  @override
  State<AddressConfirmScreen> createState() => _AddressConfirmScreenState();
}

class _AddressConfirmScreenState extends State<AddressConfirmScreen> {
  late AddressViewModel _addressViewModel;
  final _roomInfoController = TextEditingController();
  // Google地图控制器
  // ignore: unused_field
  GoogleMapController? _mapController;

  @override
  void initState() {
    super.initState();
    _addressViewModel = Provider.of<AddressViewModel>(context, listen: false);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 设置当前页面是否为装货地址
      _addressViewModel.setIsLoadingAddress(widget.isLoading);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // 新增：leading 属性，用于自定义左侧图标
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.black), // 使用关闭图标，颜色设为黑色
          onPressed: () {
            Navigator.of(context).pop(); // 点击时执行返回操作
          },
        ),
        // AppBar 背景设为白色，移除阴影以匹配原设计
        backgroundColor: Colors.white,
        elevation: 0, // 移除阴影
        shape:
            Border(bottom: BorderSide(color: Colors.grey.shade200, width: 1)),
      ),
      body: SafeArea(
        // 将主 Column 放入 SingleChildScrollView
        child: SingleChildScrollView(
          child: Column(
            children: [
              // 固定区域 1: 地图区域
              SizedBox(
                height: 330.h,
                child: Stack(
                  children: [
                    // 地图
                    GoogleMap(
                      initialCameraPosition: CameraPosition(
                        target: _addressViewModel.selectedPosition,
                        zoom: 15,
                      ),
                      markers: _addressViewModel.markers,
                      onMapCreated: (GoogleMapController controller) {
                        _mapController = controller;
                      },
                      myLocationEnabled: true,
                      myLocationButtonEnabled: true, // 安卓原生定位按钮
                      zoomControlsEnabled: false,
                    ),
                    // 地图中心标记
                    Center(
                      child: Icon(
                        Icons.location_on,
                        color: Colors.red,
                        size: 36.sp,
                      ),
                    ),
                  ],
                ),
              ),
              // 固定区域 2: 当前位置信息 Card
              Padding(
                padding: EdgeInsets.fromLTRB(8.w, 8.w, 8.w, 0),
                child: AppCard(
                  margin: EdgeInsets.zero,
                  child: InkWell(
                    // 添加InkWell使整个卡片可点击
                    onTap: () {
                      // 返回到地址搜索页面
                      Navigator.pop(context);
                    },
                    child: Column(
                      children: [
                        // 当前位置信息 Row
                        Container(
                          padding: EdgeInsets.all(8.w),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.location_on,
                                color: Colors.blue,
                                size: 18.sp,
                              ),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      _addressViewModel
                                              .selectedAddress?['name'] ??
                                          '',
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    Text(
                                      _addressViewModel
                                              .selectedAddress?['address'] ??
                                          '',
                                      style: TextStyle(
                                        fontSize: 12.sp,
                                        color: Colors.grey.shade600,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                              Icon(
                                Icons.chevron_right,
                                color: Colors.grey.shade400,
                                size: 20.sp,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Padding(
                padding:
                    EdgeInsets.fromLTRB(8.w, 8.w, 8.w, 8.w), // 调整顶部 padding
                child: AppCard(
                  margin: EdgeInsets.zero,
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.w),
                  child: Column(
                    // 移除 mainAxisSize: MainAxisSize.min，因为不再需要限制高度
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: 100.w, // 保持标签宽度一致
                            child: Text(
                              '门牌号(可选)',
                              style: AppTextStyles.labelMedium,
                            ),
                          ),
                          Expanded(
                            child: TextField(
                              controller: _roomInfoController,
                              decoration: InputDecoration(
                                hintText: '如8栋801',
                                hintStyle: TextStyle(
                                  fontSize: 12.sp,
                                  color: Colors.grey.shade400,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8.r),
                                  borderSide: BorderSide.none, // 移除边框
                                ),
                                isDense: true,
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12.w,
                                  vertical: 8.h, // 恢复之前的 padding
                                ),
                                filled: true, // 添加背景色
                                fillColor: Colors.grey.shade200, // 设置背景色
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // 固定区域 3: 底部按钮
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(
                      color: Colors.grey.shade200,
                      width: 1,
                    ),
                  ),
                ),
                child: ElevatedButton(
                  onPressed: () async {
                    _addressViewModel.validateAndSaveAddress(context);
                    // 将确认的地址添加到搜索的历史记录
                    final addressSearchViewModel =
                        context.read<AddressSearchViewModel>();
                    await addressSearchViewModel
                        .addToHistory(_addressViewModel.selectedAddress!);

                    if (widget.isLoading) {
                      // 确认装货地址后跳转到主页面
                      _addressViewModel.updateLoadingAddress(
                          _addressViewModel.selectedAddress!);
                      addressSearchViewModel
                          .clearSearchResults(); //选好地址后，清空搜索结果
                      if (context.mounted) {
                        Navigator.of(context).pushAndRemoveUntil(
                          MaterialPageRoute(
                            builder: (context) => const UserNavScreen(),
                          ),
                          (route) => false, // 清除所有路由历史
                        );
                      }
                    } else {
                      // 确认卸货地址后跳转到订单确认页面
                      _addressViewModel.updateUnloadingAddress(
                          _addressViewModel.selectedAddress!);
                      if (context.mounted) {
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => const OrderConfirmScreen(),
                          ),
                        );
                      }
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                    minimumSize: Size(double.infinity, 48.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(
                    widget.isLoading ? '确认装货地址' : '确认卸货地址',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
