//货主选择车型，地址和价格后的确认下单页面
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/screens/address/address_search_screen.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/view_models/order_view_model.dart';
import 'package:provider/provider.dart';
import 'package:hyt/widgets/unified_button.dart';
import 'package:hyt/view_models/address_view_model.dart';
import 'package:hyt/view_models/appuser_view_model.dart'; // 导入用户视图模型
import 'package:intl/intl.dart'; // 用于格式化日期时间
import 'package:hyt/data/models/order.dart'; // 导入 Order 模型
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
// 导入公共工具
import 'package:hyt/utils/input_formatters.dart';
import 'package:hyt/utils/snackbar_utils.dart';
import 'package:hyt/styles/app_theme.dart';

class OrderConfirmScreen extends StatefulWidget {
  const OrderConfirmScreen({super.key});

  @override
  State<OrderConfirmScreen> createState() => _OrderConfirmScreenState();
}

class _OrderConfirmScreenState extends State<OrderConfirmScreen> {
  String _selectedServiceType = 'fast'; // 默认选择快车
  final TextEditingController _remarkController = TextEditingController();
  final TextEditingController _phoneController =
      TextEditingController(text: '18867105036');
  final TextEditingController _contactNameController =
      TextEditingController(); // 添加联系人控制器
  final TextEditingController _customPriceController =
      TextEditingController(); // 添加自定义价格控制器
  final FocusNode _customPriceFocusNode = FocusNode(); // 添加焦点节点
  // 添加货物类型和重量的控制器或状态变量

  // 状态变量用于存储装货时间和车型
  DateTime selectedDateTime = DateTime.now(); // 默认选择当前日期
  String selectedVehicleType = '厢式货车 - 3.5吨'; // 默认或当前选定车型
  bool isScheduledTime = false; // 是否选择了预约时间，默认为当前时间

  // 跳转到地址选择页面
  void _navigateToAddressSelection(BuildContext context,
      {required bool isLoading}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddressSearchScreen(isLoading: isLoading),
      ),
    );
  }

  @override
  void dispose() {
    _remarkController.dispose();
    _phoneController.dispose();
    _contactNameController.dispose(); // 释放联系人控制器
    _customPriceController.dispose(); // 释放自定义价格控制器
    _customPriceFocusNode.dispose(); // 释放焦点节点
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final addressViewModel = Provider.of<AddressViewModel>(context);
    final loadingAddress =
        addressViewModel.loadingAddress ?? {'name': '', 'address': ''};
    final unloadingAddress =
        addressViewModel.unloadingAddress ?? {'name': '', 'address': ''};

    // 获取当前选定服务的价格 (需要根据 _selectedServiceType 动态获取)
    String currentPrice = '107.29'; // 默认快车价格，需要根据选择更新
    int statusCode = 0; // 默认状态码

    switch (_selectedServiceType) {
      case 'carpool':
        currentPrice = '75';
        break;
      case 'fast':
        currentPrice = '107.29';
        break;
      case 'express':
        currentPrice = '129.82';
        break;
      case 'custom':
        // 使用用户输入的自定义价格，如果为空则显示"待议价"
        currentPrice = _customPriceController.text.isNotEmpty
            ? _customPriceController.text
            : '待议价';
        break;
    }

    return Scaffold(
      // 新增：AppBar
      appBar: AppBar(
        // 设置 leading 为关闭图标
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.black), // 黑色关闭图标
          onPressed: () => Navigator.of(context)
              .popUntil((route) => route.isFirst), // 点击返回首页
        ),
        backgroundColor: Colors.white, // 背景设为白色
        elevation: 1, // 可以设置轻微阴影或 0
        // 移除 title 属性，使其不显示标题
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 主体内容区域（可滚动）
            Expanded(
              child: SingleChildScrollView(
                // 调整 SingleChildScrollView 的 padding，减少顶部和底部间距
                //padding: EdgeInsets.symmetric(vertical: 2.h),
                child: Column(
                  children: [
                    // 车型选择卡片
                    Container(
                      margin: EdgeInsets.only(left: 16.w, right: 16.w), // 左右边距
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.r), // 圆角
                        boxShadow: const [
                          // 添加阴影，与地址卡片统一
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 5,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // 已选车型展示 - 使用 ListTile 风格简化
                          ListTile(
                            dense: true, // 使 ListTile 更紧凑
                            contentPadding:
                                EdgeInsets.symmetric(horizontal: 12.w), // 统一内边距
                            leading: Icon(Icons.local_shipping,
                                color: Colors.blue.shade500, size: 20.sp),
                            title: Text(
                              selectedVehicleType, // 使用状态变量
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            trailing: Icon(Icons.chevron_right,
                                color: Colors.grey.shade400, size: 20.sp),
                            onTap: () {
                              // TODO: 实现选择车型的逻辑
                            },
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: AppInsets.gap4()), // 区块间距统一
                    // 地址选择区域
                    Container(
                      margin:
                          EdgeInsets.only(top: 2.w, left: 16.w, right: 16.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.r),
                        boxShadow: const [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 5,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // 装货地址
                          InkWell(
                            onTap: () {
                              // 跳转到装货地址选择页面
                              _navigateToAddressSelection(context,
                                  isLoading: true);
                            },
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 12.w, vertical: 12.w), // 统一内边距
                              child: Row(
                                children: [
                                  // 替换图标为加粗文字
                                  Container(
                                    width: 30.w,
                                    height: 30.w,
                                    decoration: BoxDecoration(
                                      color: Colors.blue.shade50,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: Text(
                                        '装',
                                        style: TextStyle(
                                          color: Colors.blue.shade700,
                                          fontSize: 14.sp,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 12.w),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          loadingAddress['name'] ?? '',
                                          style: TextStyle(
                                            fontSize: 14.sp,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        Text(
                                          loadingAddress['address'] ?? '',
                                          style: TextStyle(
                                            fontSize: 12.sp,
                                            color: Colors.grey.shade600,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Icon(Icons.chevron_right,
                                      color: Colors.grey.shade400, size: 20.sp),
                                ],
                              ),
                            ),
                          ),
                          // 分隔线
                          Divider(
                            height: 1,
                            thickness: 1,
                            indent: 6.w,
                            endIndent: 6.w,
                            color: Colors.grey.shade300,
                          ),

                          // 卸货地址
                          InkWell(
                            onTap: () {
                              // 跳转到卸货地址选择页面
                              _navigateToAddressSelection(context,
                                  isLoading: false);
                            },
                            child: Padding(
                              padding: EdgeInsets.all(12.w),
                              child: Row(
                                children: [
                                  // 替换图标为加粗文字
                                  Container(
                                    width: 30.w,
                                    height: 30.w,
                                    decoration: BoxDecoration(
                                      color: Colors.green.shade50,
                                      shape: BoxShape.circle,
                                    ),
                                    child: Center(
                                      child: Text(
                                        '卸',
                                        style: AppTextStyles.headline4.copyWith(
                                          color: Colors.green.shade500,
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 12.w),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        unloadingAddress['name']!.isEmpty
                                            ? Text(
                                                '点击选择卸货地址',
                                                style: TextStyle(
                                                  fontSize: 12.sp,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                              )
                                            : Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    unloadingAddress['name']!,
                                                    style: TextStyle(
                                                      fontSize: 14.sp,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                  Text(
                                                    unloadingAddress[
                                                        'address']!,
                                                    style: TextStyle(
                                                      fontSize: 12.sp,
                                                      color:
                                                          Colors.grey.shade600,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ],
                                              ),
                                      ],
                                    ),
                                  ),
                                  Icon(
                                    Icons.chevron_right,
                                    color: Colors.grey.shade400,
                                    size: 20.sp,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: AppInsets.gap4()), // 区块间距统一
                    // 联系信息区域
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 16.w),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.r),
                        boxShadow: const [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 5,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // 联系人输入（可选）
                          ListTile(
                            dense: true,
                            leading: Icon(Icons.person,
                                color: Colors.blue.shade500, size: 20.sp),
                            title: TextField(
                              controller: _contactNameController, // 使用联系人控制器
                              decoration: InputDecoration(
                                hintText: '联系人（可选）',
                                hintStyle: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.grey.shade400,
                                ),
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.zero,
                                isDense: true,
                              ),
                            ),
                          ),
                          // 分隔线
                          Divider(
                            height: 1,
                            thickness: 1,
                            indent: 16.w,
                            endIndent: 16.w,
                            color: Colors.grey.shade200,
                          ),
                          // 联系电话输入（必填）
                          ListTile(
                            dense: true,
                            leading: Icon(Icons.phone,
                                color: Colors.blue.shade500, size: 20.sp),
                            title: TextField(
                              controller: _phoneController,
                              keyboardType: TextInputType.phone,
                              style: TextStyle(fontSize: 14.sp),
                              decoration: InputDecoration(
                                hintText: '联系电话（必填）',
                                hintStyle: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.grey.shade400,
                                ),
                                border: InputBorder.none,
                                contentPadding: EdgeInsets.zero,
                                isDense: true,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: AppInsets.gap4()), // 区块间距统一
                    Container(
                      // 车型价格选择
                      //margin: EdgeInsets.symmetric(horizontal: 16.w), // 统一左右边距
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 4.h), // 调整内边距
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12.r), // 圆角
                        boxShadow: const [
                          // 添加阴影
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 5,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          _buildServiceOption(
                            title: '拼车',
                            description: '省心不急 拼车划算',
                            price: '75',
                            value: 'carpool',
                            isSelected: _selectedServiceType == 'carpool',
                          ),
                          SizedBox(height: 8.h), // 减少选项间距
                          _buildServiceOption(
                            title: '快车',
                            description: '司机多 到达快',
                            price: '107.29',
                            value: 'fast',
                            isSelected: _selectedServiceType == 'fast',
                          ),
                          SizedBox(height: 8.h), // 减少选项间距
                          _buildCustomPriceOption(),
                          SizedBox(height: 8.h), // 减少选项间距
                          _buildServiceOption(
                            title: '特快',
                            description: '急用车 速度最快',
                            price: '129.82',
                            value: 'express',
                            isSelected: _selectedServiceType == 'express',
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: AppInsets.gap4()), // 区块间距统一
                  ],
                ),
              ),
            ),
            // 底部操作区域 - 装货时间选择、价格信息和确认下单按钮
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13),
                    blurRadius: 4,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 日期和时间选择器
                  InkWell(
                    onTap: () {
                      DatePicker.showDateTimePicker(
                        context,
                        showTitleActions: true,
                        minTime: DateTime.now().add(const Duration(hours: 1)),
                        maxTime: DateTime.now().add(const Duration(days: 7)),
                        onConfirm: (datetime) {
                          setState(() {
                            selectedDateTime = datetime;
                            isScheduledTime = true;
                          });
                        },
                        locale: LocaleType.zh,
                      );
                    },
                    child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.timer,
                            color: Colors.blue.shade500,
                            size: 20.sp,
                          ),
                          Text(
                            isScheduledTime
                                ? DateFormat('yyyy-MM-dd HH:mm')
                                    .format(selectedDateTime)
                                : '选预约',
                          ),
                          Icon(
                            Icons.chevron_right,
                            color: Colors.blue.shade500,
                            size: 20.sp,
                          ),
                        ]),
                  ),
                  // 确认下单按钮
                  UnifiedButton.elevated(
                    text: '确认下单',
                    onPressed: () {
                      // 检查地址是否已选择
                      if (loadingAddress['name']!.isEmpty ||
                          unloadingAddress['name']!.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('请选择装货和卸货地址')),
                        );
                        return; // 阻止提交
                      }
                      // 验证联系电话是否已填写（必填项）
                      if (_phoneController.text.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('请输入联系电话'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      // 验证用户出价是否已填写
                      if (_selectedServiceType == 'custom' &&
                          _customPriceController.text.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('请输入出价金额'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }
                      // 获取当前用户ID
                      final appUserViewModel =
                          Provider.of<AppUserViewModel>(context, listen: false);
                      final currentUserId = appUserViewModel.currentUser?.id;

                      // 检查用户是否已登录
                      if (currentUserId == null) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('请先登录后再下单'),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      final newOrder = Order(
                        orderId:
                            'DD${DateTime.now().millisecondsSinceEpoch}', // 生成唯一ID
                        statusCode: statusCode, // 使用动态状态码
                        pickupLocationName: loadingAddress['name']!, // 装货地点名称
                        pickupAddress: loadingAddress['address']!, // 装货详细地址
                        deliveryLocationName:
                            unloadingAddress['name']!, // 卸货地点名称
                        deliveryAddress: unloadingAddress['address']!, // 卸货详细地址
                        createTime: DateTime.now(),
                        price: _selectedServiceType == 'custom'
                            ? double.tryParse(_customPriceController.text) ??
                                0.0
                            : double.parse(currentPrice), // 将价格转换为double类型
                        customerID: currentUserId, // 使用当前登录用户的ID
                        customerName: _contactNameController.text.isEmpty
                            ? _phoneController.text
                            : _contactNameController.text, // 使用联系人信息
                        customerPhone: _phoneController.text, // 从控制器获取
                        remark: _remarkController.text.trim(),
                        serviceType: _selectedServiceType, // 添加服务类型
                        loadingTime: isScheduledTime //区分预约和立即下单
                            ? selectedDateTime
                            : DateTime.now(),
                        vehicleType: selectedVehicleType,
                      );
                      // 2. 使用 order_view 添加 Order 对象
                      context.read<OrderViewModel>().createOrder(newOrder);
                      // 3. 显示成功提示
                      SnackBarUtils.showSuccessSnackBar(context, '订单创建成功');
                      // 4. 返回到订单列表页面
                      Navigator.of(context).popUntil((route) => route.isFirst);
                    },
                    color: Colors.blue.shade600,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceOption({
    required String title,
    required String description,
    required String price,
    required String value,
    bool isSelected = false,
    bool isPriceGray = false,
  }) {
    return InkWell(
      // 使用 InkWell 方便整个区域点击
      onTap: () {
        if (value != _selectedServiceType) {
          setState(() {
            _selectedServiceType = value;
          });
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h), // 减少内边距
        decoration: BoxDecoration(
          color:
              isSelected ? Colors.blue.shade50 : Colors.transparent, // 非选中时透明背景
          border: Border.all(
            color: isSelected ? Colors.blue.shade300 : Colors.grey.shade300,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            Radio<String>(
              // 将 Radio 类型改为 String?
              value: value,
              groupValue: _selectedServiceType,
              onChanged: (String? newValue) {
                // onChanged 类型改为 String?
                if (newValue != null) {
                  setState(() {
                    _selectedServiceType = newValue;
                  });
                }
              },
              activeColor: Colors.blue.shade600,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap, // 减小点击区域
            ),
            // SizedBox(width: 8.w), // Radio 自带一些边距，可能不需要额外 SizedBox
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    //style: AppTextStyles.bodyMedium,
                  ),
                  Text(description, style: AppTextStyles.labelSmall),
                ],
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              price,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
                color: isPriceGray ? Colors.grey.shade500 : Colors.red.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建用户出价选项（包含输入框）
  Widget _buildCustomPriceOption() {
    bool isSelected = _selectedServiceType == 'custom';

    return InkWell(
      onTap: () {
        if (_selectedServiceType != 'custom') {
          setState(() {
            _selectedServiceType = 'custom';
          });
          // 延迟一帧后获取焦点，确保UI已更新
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _customPriceFocusNode.requestFocus();
          });
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withAlpha(20)
              : Colors.transparent,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Colors.grey.shade300,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            Radio<String>(
              value: 'custom',
              groupValue: _selectedServiceType,
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedServiceType = newValue;
                  });
                  // 当选择用户出价时，自动获取焦点
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _customPriceFocusNode.requestFocus();
                  });
                }
              },
              activeColor: Colors.blue.shade600,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '用户出价',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '灵活议价 价格可协商',
                    style: AppTextStyles.labelSmall,
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.w),
            // 价格输入区域
            SizedBox(
              width: 80.w,
              child: TextField(
                controller: _customPriceController,
                focusNode: _customPriceFocusNode,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                textAlign: TextAlign.right,
                inputFormatters: [
                  // 使用公共的价格输入格式化器
                  InputFormatters.priceFormatter(),
                ],
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade600,
                ),
                decoration: InputDecoration(
                  hintText: '请出价',
                  hintStyle: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade500,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                  isDense: true,
                ),
                onTap: () {
                  // 点击输入框时也选择用户出价选项
                  if (_selectedServiceType != 'custom') {
                    setState(() {
                      _selectedServiceType = 'custom';
                    });
                  }
                },
                onChanged: (value) {
                  // 当输入内容变化时，触发UI更新以显示新价格
                  setState(() {});
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
