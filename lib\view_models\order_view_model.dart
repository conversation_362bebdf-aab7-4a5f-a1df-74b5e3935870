import 'package:flutter/material.dart';
import 'package:hyt/constants/order_status_constants.dart';
import 'package:hyt/data/models/base_message.dart';
import 'package:hyt/data/repos/base_message_repo.dart';
import 'package:hyt/data/repos/order_repo.dart';
import 'package:hyt/data/models/order.dart';
import 'package:hyt/data/models/order_status_history.dart';
import 'package:hyt/utils/enums.dart';
import 'package:hyt/utils/cancel_order_utils.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:hyt/data/models/reviews.dart';
import 'package:hyt/view_models/base_view_model.dart';
import 'package:hyt/utils/log_utils.dart';
import 'package:hyt/utils/app_error.dart';

/// 订单视图模型，处理订单相关的业务逻辑
class OrderViewModel extends BaseViewModel {
  final OrderRepo _orderRepository;
  final BaseMessageRepo _baseMessageRepo;
  List<Order> _myOrders = [];
  List<Order> _pendingOrders = [];
  bool _isButtonLoading = false; // 按钮操作的加载状态
  String? _message;
  Order? _currentOrder;

  final Map<String, List<OrderStatusHistory>> _orderStatusHistory = {};

  // 订单下所有评价
  List<Review> _orderReviews = [];
  List<Review> get orderReviews => _orderReviews;

  // 存储用户是否已评价某订单的状态
  final Map<String, Map<String, bool>> _hasReviewedMap = {};

  // 订单列表页面状态 - 标签标题
  final List<String> tabTitles = ['全部', '待接单', '运输中', '已送达', '已完成', '已取消'];

  String? get message => _message;
  OrderViewModel(this._orderRepository, this._baseMessageRepo);

  /// 获取订单列表
  List<Order> get myOrders => _myOrders;
  List<Order> get pendingOrders => _pendingOrders;

  /// 按钮是否正在加载
  bool get isButtonLoading => _isButtonLoading;

  /// 根据订单ID获取订单
  Future<Order?> getOrderById(String orderId) async {
    setLoading(true);
    try {
      final order = await _orderRepository.getOrderById(orderId);
      if (order != null) {
        _currentOrder = order;
        _orderStatusHistory[orderId] =
            await _orderRepository.getOrderStatusHistory(orderId);
        notifyListeners();
      }
      return order;
    } catch (e, st) {
      _message = '获取订单失败: ${e.toString()}';
      LogUtils.e(_message!, e, st);
      return null;
    } finally {
      setLoading(false);
    }
  }

  /// 司机可以看到所有与自己相关的订单，包括被用户删除的订单
  List<Order> getDriverOrders() {
    return _myOrders
        .where((order) =>
            order.driverId != null &&
            order.driverId!.isNotEmpty &&
            order.isDeletedByDriver == false)
        .toList();
  }

  /// 获取订单状态历史记录
  List<OrderStatusHistory> getOrderStatusHistory(String orderId) {
    return _orderStatusHistory[orderId] ?? [];
  }

  /// 获取司机当前进行中的订单
  List<Order> getCurrentOrders() {
    return _myOrders
        .where((order) =>
            (order.driverId != null && order.driverId!.isNotEmpty) &&
            (order.statusCode == OrderStatusConstants.STATUS_ACCEPTED ||
                order.statusCode == OrderStatusConstants.STATUS_TRANSPORTING))
        .toList();
  }

  /// 获取待接单的订单
  Future<void> getAllPendingOrders() async {
    setLoading(true);
    try {
      _pendingOrders = await _orderRepository.getAllPendingOrders();
    } catch (e, st) {
      _message = '获取待接单订单失败: $e';
      LogUtils.e(_message!, e, st);
    } finally {
      setLoading(false);
      notifyListeners();
    }
  }

  /// 添加状态历史记录
  Future<void> _addStatusHistory(String orderId, int statusCode) async {
    if (!_orderStatusHistory.containsKey(orderId)) {
      _orderStatusHistory[orderId] = [];
    }
    final historyItem = OrderStatusHistory(
      orderId: orderId,
      statusCode: statusCode,
      timestamp: DateTime.now(),
    );
    _orderStatusHistory[orderId]!.add(historyItem);

    try {
      final success =
          await _orderRepository.addOrderStatusHistory(orderId, historyItem);
      if (!success) {
        notifyListeners();
        throw AppError('保存状态历史记录到数据库失败');
      }
    } catch (e) {
      _orderStatusHistory[orderId]!.remove(historyItem);
      rethrow;
    }
  }

  /// 加载司机/货主个人订单（并发合并历史请求）
  Future<void> loadMyOrders(String? userId, {bool isDriver = false}) async {
    if (userId == null) return;
    setLoading(true);
    try {
      _myOrders = isDriver
          ? await _orderRepository.getDriverOrders(userId)
          : await _orderRepository.getShipperOrders(userId);

      // 并发获取每个订单历史
      final futures = _myOrders
          .map((o) => _orderRepository.getOrderStatusHistory(o.orderId))
          .toList();
      final histories = await Future.wait(futures);
      for (int i = 0; i < _myOrders.length; i++) {
        _orderStatusHistory[_myOrders[i].orderId] = histories[i];
      }
    } catch (e, st) {
      _message = '加载个人订单失败: $e';
      LogUtils.e(_message!, e, st);
      rethrow;
    } finally {
      setLoading(false);
      notifyListeners();
    }
  }

  /// 加载抢单大厅订单（全平台待接单）
  Future<void> loadPendingOrders() async {
    setLoading(true);
    try {
      _pendingOrders = await _orderRepository.getAllPendingOrders();
    } catch (e, st) {
      _message = '获取待接单订单失败: $e';
      LogUtils.e(_message!, e, st);
    } finally {
      setLoading(false);
      notifyListeners();
    }
  }

  /// 抢单
  Future<bool> acceptOrder(
      String orderId, AppUserViewModel appUserViewModel) async {
    final driverId = appUserViewModel.currentUser?.id;
    final driverName = appUserViewModel.currentUser?.nickName;
    final driverPhone = appUserViewModel.currentUser?.phone;
    final vehiclePlate = appUserViewModel.currentUser?.vehiclePlate;
    try {
      _isButtonLoading = true;
      notifyListeners();
      if (driverId == null ||
          driverName == null ||
          driverPhone == null ||
          vehiclePlate == null) {
        _message = '司机信息不完整，无法抢单';
        return false;
      }
      final updatedOrder = await _orderRepository.acceptOrder(
          orderId: orderId,
          driverId: driverId,
          statusCode: OrderStatusConstants.STATUS_ACCEPTED,
          driverName: driverName,
          driverPhone: driverPhone,
          vehiclePlate: vehiclePlate);
      if (updatedOrder != null) {
        _message = '抢单成功';
        _pendingOrders.removeWhere((order) => order.orderId == orderId);
        _myOrders.insert(0, updatedOrder);
        _currentOrder = updatedOrder;
        await _addStatusHistory(orderId, OrderStatusConstants.STATUS_ACCEPTED);

        // 通知双方
        final shipperId = updatedOrder.customerID;
        final shipperMsg =
            '您的订单（${updatedOrder.orderId}）已被司机$driverName：($driverPhone)接受';
        final shipperMessage = BaseMessage(
          type: MessageType.order,
          content: shipperMsg,
          createdAt: DateTime.now(),
          receiverId: shipperId,
          orderId: updatedOrder.orderId,
        );
        await _baseMessageRepo.sendOrderMessage(message: shipperMessage);

        final driverMsg =
            '您已成功接单（${updatedOrder.orderId}），请尽快联系货主${updatedOrder.customerName}（${updatedOrder.customerPhone}）';
        final driverMessage = BaseMessage(
          type: MessageType.order,
          content: driverMsg,
          createdAt: DateTime.now(),
          receiverId: driverId,
          orderId: updatedOrder.orderId,
        );
        await _baseMessageRepo.sendOrderMessage(message: driverMessage);

        notifyListeners();
        return true;
      } else {
        _message = '抢单失败';
        return false;
      }
    } catch (e, st) {
      _message = '抢单出错：${e.toString()}';
      LogUtils.e(_message!, e, st);
      return false;
    } finally {
      _isButtonLoading = false;
      notifyListeners();
    }
  }

  /// 创建新订单
  Future<bool> createOrder(Order order) async {
    setLoading(true);
    try {
      final newOrder = await _orderRepository.createOrder(order);
      _myOrders.insert(0, newOrder);
      _addStatusHistory(newOrder.orderId, OrderStatusConstants.STATUS_WAITING);
      return true;
    } catch (e, st) {
      _message = '创建订单失败: ${e.toString()}';
      LogUtils.e(_message!, e, st);
      return false;
    } finally {
      setLoading(false);
      notifyListeners();
    }
  }

  /// 取消订单
  Future<bool> cancelOrder(String orderId) async {
    try {
      _isButtonLoading = true;
      notifyListeners();
      final success = await _orderRepository.cancelOrder(
          orderId, OrderStatusConstants.STATUS_CANCELED);
      if (success) {
        _message = '订单已取消,可以重新发布';
        final index = _myOrders.indexWhere((order) => order.orderId == orderId);
        final canceledOrder = _myOrders[index];
        _myOrders[index].statusCode = OrderStatusConstants.STATUS_CANCELED;
        _addStatusHistory(orderId, OrderStatusConstants.STATUS_CANCELED);

        if (canceledOrder.driverId != null &&
            canceledOrder.driverId!.isNotEmpty) {
          final driverMsg =
              '订单（${canceledOrder.orderId}）已被货主取消。货主：${canceledOrder.customerName}，电话：${canceledOrder.customerPhone}';
          final driverMessage = BaseMessage(
            type: MessageType.order,
            content: driverMsg,
            createdAt: DateTime.now(),
            receiverId: canceledOrder.driverId!,
            orderId: canceledOrder.orderId,
          );
          await _baseMessageRepo.sendOrderMessage(message: driverMessage);
        }
      } else {
        _message = '取消订单失败，请稍后重试';
      }
      return success;
    } catch (e, st) {
      _message = '取消订单出错：${e.toString()}';
      LogUtils.e(_message!, e, st);
      return false;
    } finally {
      _isButtonLoading = false;
      notifyListeners();
    }
  }

  /// 确认取货
  Future<bool> pickupOrder(String orderId) async {
    try {
      _isButtonLoading = true;
      notifyListeners();
      final updatedOrder = await _orderRepository.pickupOrder(orderId);
      if (updatedOrder != null) {
        _message = '取货成功';
        _currentOrder = updatedOrder;
        final index = _myOrders.indexWhere((order) => order.orderId == orderId);
        if (index != -1) {
          _myOrders[index] = updatedOrder;
        }
        await _addStatusHistory(
            orderId, OrderStatusConstants.STATUS_TRANSPORTING);

        final shipperId = updatedOrder.customerID;
        final shipperMsg =
            '您的订单（${updatedOrder.orderId}）司机已装货，正在运输中。司机：${updatedOrder.driverName}，电话：${updatedOrder.driverPhone}';
        final shipperMessage = BaseMessage(
          type: MessageType.order,
          content: shipperMsg,
          createdAt: DateTime.now(),
          receiverId: shipperId,
          orderId: updatedOrder.orderId,
        );
        await _baseMessageRepo.sendOrderMessage(message: shipperMessage);

        notifyListeners();
        return true;
      } else {
        _message = '取货失败';
        return false;
      }
    } catch (e, st) {
      _message = '取货出错：${e.toString()}';
      LogUtils.e(_message!, e, st);
      return false;
    } finally {
      _isButtonLoading = false;
      notifyListeners();
    }
  }

  //订单已送达
  Future<bool> deliverOrder(
      AppUserViewModel appUserViewModel, String orderId) async {
    _isButtonLoading = true;
    notifyListeners();
    try {
      final updatedOrder = await _orderRepository.deliverOrder(orderId);
      if (updatedOrder != null) {
        _message = '已送达';
        final index = _myOrders.indexWhere((order) => order.orderId == orderId);
        if (index != -1) {
          _myOrders[index] = updatedOrder;
        }
        _currentOrder = updatedOrder;
        await _addStatusHistory(orderId, OrderStatusConstants.STATUS_DELIVERED);

        final shipperId = updatedOrder.customerID;
        final shipperMsg =
            '您的订单（${updatedOrder.orderId}）已送达目的地，请确认收货。司机：${updatedOrder.driverName}，电话：${updatedOrder.driverPhone}';
        final shipperMessage = BaseMessage(
          type: MessageType.order,
          content: shipperMsg,
          createdAt: DateTime.now(),
          receiverId: shipperId,
          orderId: updatedOrder.orderId,
        );
        await _baseMessageRepo.sendOrderMessage(message: shipperMessage);

        notifyListeners();
        return true;
      } else {
        _message = '操作失败';
        return false;
      }
    } catch (e, st) {
      _message = '操作出错：${e.toString()}';
      LogUtils.e(_message!, e, st);
      return false;
    } finally {
      _isButtonLoading = false;
      notifyListeners();
    }
  }

  /// 获取用户所有订单
  Future<void> getShipperOrders(String userId,
      {bool forceRefresh = false}) async {
    if (_myOrders.isNotEmpty && !forceRefresh) {
      return;
    }
    setLoading(true);
    try {
      _myOrders = await _orderRepository.getShipperOrders(userId);
    } catch (e, st) {
      _message = '获取订单失败: ${e.toString()}';
      LogUtils.e(_message!, e, st);
    } finally {
      setLoading(false);
      notifyListeners();
    }
  }

  /// 联系订单相关人员（司机或货主）
  Future<void> contactOrderPerson(
    BuildContext context,
    Order order, {
    required String contactType,
  }) async {
    final String? phoneNumber =
        contactType == 'driver' ? order.driverPhone : order.customerPhone;
    if (phoneNumber == null || phoneNumber.isEmpty) {
      _message = '${contactType == 'driver' ? '司机' : '货主'}电话不可用';
      return;
    }
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      _message = '无法拨打电话';
    }
  }

  /// 处理订单支付
  Future<bool> processPayment(String orderId) async {
    try {
      _isButtonLoading = true;
      notifyListeners();
      final updatedOrder = await _orderRepository.processPayment(orderId);
      if (updatedOrder != null) {
        _message = '支付成功';
        final index = _myOrders.indexWhere((order) => order.orderId == orderId);
        if (index != -1) {
          _myOrders[index] = updatedOrder;
        }
        _currentOrder = updatedOrder;
        await _addStatusHistory(orderId, OrderStatusConstants.STATUS_COMPLETED);

        if (updatedOrder.driverId != null &&
            updatedOrder.driverId!.isNotEmpty) {
          final driverMsg =
              '订单（${updatedOrder.orderId}）货主已完成支付，运费已到账。货主：${updatedOrder.customerName}，电话：${updatedOrder.customerPhone}';
          final driverMessage = BaseMessage(
            type: MessageType.order,
            content: driverMsg,
            createdAt: DateTime.now(),
            receiverId: updatedOrder.driverId!,
            orderId: updatedOrder.orderId,
          );
          await _baseMessageRepo.sendOrderMessage(message: driverMessage);
        }

        notifyListeners();
        return true;
      } else {
        _message = '支付失败';
        return false;
      }
    } catch (e, st) {
      _message = '支付出错：${e.toString()}';
      LogUtils.e(_message!, e, st);
      return false;
    } finally {
      _isButtonLoading = false;
      notifyListeners();
    }
  }

  /// 删除订单（逻辑删除，支持权限验证）
  Future<bool> deleteOrder(
      String orderId, String userId, String userRole) async {
    try {
      _isButtonLoading = true;
      notifyListeners();
      if (!canDeleteOrder(orderId, userId, userRole)) {
        _message = '您没有权限删除此订单';
        return false;
      }
      final success =
          await _orderRepository.markOrderAsDeletedByRole(orderId, userRole);
      if (success) {
        _message = '订单已删除';
        _myOrders.removeWhere((order) => order.orderId == orderId);
        if (_currentOrder?.orderId == orderId) {
          _currentOrder = null;
        }
      } else {
        _message = '删除订单失败，请稍后重试';
      }
      return success;
    } catch (e, st) {
      _message = '删除订单异常：${e.toString()}';
      LogUtils.e(_message!, e, st);
      return false;
    } finally {
      _isButtonLoading = false;
      notifyListeners();
    }
  }

  bool canDeleteOrder(String orderId, String userId, String userRole) {
    final order = _myOrders.firstWhere(
      (o) => o.orderId == orderId,
      orElse: () => _currentOrder!,
    );

    if (order.statusCode != OrderStatusConstants.STATUS_CANCELED) {
      return false;
    }
    if (userRole == 'shipper' && order.customerID != userId) {
      return false;
    }
    if (userRole == 'driver' && order.driverId != userId) {
      return false;
    }
    return true;
  }

  Order? get currentOrder => _currentOrder;
  void setCurrentOrder(Order order) {
    _currentOrder = order;
    notifyListeners();
  }

  bool hasReviewed(String orderId, String fromUserId) {
    return _hasReviewedMap[orderId]?[fromUserId] ?? false;
  }

  void setHasReviewed(String orderId, String fromUserId, bool reviewed) {
    if (!_hasReviewedMap.containsKey(orderId)) {
      _hasReviewedMap[orderId] = {};
    }
    _hasReviewedMap[orderId]![fromUserId] = reviewed;
    notifyListeners();
  }

  Future<void> getReviewsForOrder(String orderId) async {
    try {
      _orderReviews = await _orderRepository.getReviewsForOrder(orderId);
      if (!_hasReviewedMap.containsKey(orderId)) {
        _hasReviewedMap[orderId] = {};
      }
      for (final review in _orderReviews) {
        _hasReviewedMap[orderId]![review.fromUserId] = true;
      }
      notifyListeners();
    } catch (e, st) {
      _message = '获取订单评价失败: $e';
      LogUtils.e(_message!, e, st);
    }
  }

  bool canCancelOrder(String orderId, String userId, String userRole) {
    final order = _myOrders.firstWhere(
      (o) => o.orderId == orderId,
      orElse: () => _currentOrder!,
    );
    return CancelOrderUtils.canCancelOrder(order, userId, userRole);
  }

  double calculateCancelFee(String orderId, String userRole) {
    final order = _myOrders.firstWhere(
      (o) => o.orderId == orderId,
      orElse: () => _currentOrder!,
    );
    return CancelOrderUtils.calculateCancelFee(order, userRole);
  }

  String getCancelFeeDescription(String orderId, String userRole) {
    final order = _myOrders.firstWhere(
      (o) => o.orderId == orderId,
      orElse: () => _currentOrder!,
    );
    final fee = CancelOrderUtils.calculateCancelFee(order, userRole);
    return CancelOrderUtils.getCancelFeeDescription(order, userRole, fee);
  }

  Future<bool> cancelOrderSimple(
      String orderId, String userId, String userRole, String reason) async {
    try {
      _isButtonLoading = true;
      notifyListeners();
      if (!canCancelOrder(orderId, userId, userRole)) {
        _message = '当前状态不允许取消订单';
        return false;
      }
      final fee = calculateCancelFee(orderId, userRole);
      final success = await _orderRepository.cancelOrder(
          orderId, OrderStatusConstants.STATUS_CANCELED);
      if (success) {
        _updateLocalOrderStatus(orderId, OrderStatusConstants.STATUS_CANCELED, {
          'cancel_reason': reason,
          'cancelled_by': userRole,
          'cancel_fee': fee,
          'cancel_time': DateTime.now().toIso8601String(),
        });
        await _sendCancelNotification(orderId, userId, userRole, reason, fee);
        _message = '订单取消成功';
        return true;
      } else {
        _message = '订单取消失败，请重试';
        return false;
      }
    } catch (e, st) {
      _message = '取消订单时发生错误: $e';
      LogUtils.e(_message!, e, st);
      return false;
    } finally {
      _isButtonLoading = false;
      notifyListeners();
    }
  }

  void _updateLocalOrderStatus(
      String orderId, int newStatus, Map<String, dynamic> additionalData) {
    for (int i = 0; i < _myOrders.length; i++) {
      if (_myOrders[i].orderId == orderId) {
        _myOrders[i].statusCode = newStatus;
        if (additionalData['cancel_reason'] != null) {
          _myOrders[i].cancelReason = additionalData['cancel_reason'];
        }
        if (additionalData['cancelled_by'] != null) {
          _myOrders[i].cancelledBy = additionalData['cancelled_by'];
        }
        if (additionalData['cancel_fee'] != null) {
          _myOrders[i].cancelFee = additionalData['cancel_fee'];
        }
        if (additionalData['cancel_time'] != null) {
          _myOrders[i].cancelTime =
              DateTime.parse(additionalData['cancel_time']);
        }
        break;
      }
    }
    if (_currentOrder?.orderId == orderId) {
      _currentOrder!.statusCode = newStatus;
      if (additionalData['cancel_reason'] != null) {
        _currentOrder!.cancelReason = additionalData['cancel_reason'];
      }
      if (additionalData['cancelled_by'] != null) {
        _currentOrder!.cancelledBy = additionalData['cancelled_by'];
      }
      if (additionalData['cancel_fee'] != null) {
        _currentOrder!.cancelFee = additionalData['cancel_fee'];
      }
      if (additionalData['cancel_time'] != null) {
        _currentOrder!.cancelTime =
            DateTime.parse(additionalData['cancel_time']);
      }
    }
  }

  Future<void> _sendCancelNotification(String orderId, String userId,
      String userRole, String reason, double fee) async {
    try {
      final order = _myOrders.firstWhere((o) => o.orderId == orderId);
      String receiverId;
      if (userRole == 'shipper') {
        receiverId = order.driverId ?? '';
      } else {
        receiverId = order.customerID;
      }
      if (receiverId.isNotEmpty) {
        final message = BaseMessage(
          type: MessageType.order,
          content: userRole == 'shipper'
              ? '货主取消了订单，取消原因：$reason${fee > 0 ? '，空跑费：¥${fee.toStringAsFixed(2)}' : ''}'
              : '司机取消了订单，取消原因：$reason',
          receiverId: receiverId,
          orderId: orderId,
          createdAt: DateTime.now(),
          isRead: false,
        );
        await _baseMessageRepo.sendOrderMessage(message: message);
      }
    } catch (e) {
      LogUtils.e('发送取消通知失败', e);
    }
  }

  Future<bool> submitReview(Review review) async {
    setLoading(true);
    try {
      final result = await _orderRepository.submitReview(review);
      if (result) {
        await getReviewsForOrder(review.orderId ?? '');
      }
      return result;
    } catch (e, st) {
      _message = '提交评价失败: $e';
      LogUtils.e(_message!, e, st);
      return false;
    } finally {
      setLoading(false);
      notifyListeners();
    }
  }
}
