                        -HD:\Android\flutter_windows_3.29.0-stable\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=D:\Android\SDK\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\Android\SDK\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\Android\SDK\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Android\SDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Android\workspace\hyt\build\app\intermediates\cxx\Debug\3i6n6j2z\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Android\workspace\hyt\build\app\intermediates\cxx\Debug\3i6n6j2z\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BD:\Android\workspace\hyt\android\app\.cxx\Debug\3i6n6j2z\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2