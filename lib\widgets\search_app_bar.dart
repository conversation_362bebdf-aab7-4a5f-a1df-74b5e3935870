//自定义搜索AppBar组件，支持搜索框和标签页
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/styles/app_theme.dart';

class SearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String hintText;
  final TextEditingController searchController;
  final List<String> tabTitles;
  final TabController tabController;
  final Function(String)? onSearchChanged;
  final bool centerTitle;
  final double elevation;
  final Color? backgroundColor;
  final bool showTabs;
  final List<Widget>? actions; // 添加actions参数支持AppBar右侧操作按钮

  // 标题/行为可选项
  final bool showSearchField;
  final String? titleText;
  final TextStyle? titleTextStyle;
  final Widget? titleWidget;
  final Widget? leading;
  final bool? automaticallyImplyLeading;

  // Tab样式可选项
  final TextStyle? tabLabelStyle;
  final TextStyle? tabUnselectedLabelStyle;
  final double? tabIndicatorWeight;
  final bool? tabIsScrollable;
  final TabAlignment? tabAlignment;
  final Color? tabLabelColor;
  final Color? tabUnselectedLabelColor;

  const SearchAppBar({
    super.key,
    required this.searchController,
    required this.tabController,
    this.tabTitles = const [],
    this.hintText = '搜索订单号/地址/客户',
    this.onSearchChanged,
    this.centerTitle = false,
    this.elevation = 0.5,
    this.backgroundColor,
    this.showTabs = true,
    this.actions,
    // 标题/行为
    this.showSearchField = true,
    this.titleText,
    this.titleTextStyle,
    this.titleWidget,
    this.leading,
    this.automaticallyImplyLeading,
    // Tabs
    this.tabLabelStyle,
    this.tabUnselectedLabelStyle,
    this.tabIndicatorWeight,
    this.tabIsScrollable,
    this.tabAlignment,
    this.tabLabelColor,
    this.tabUnselectedLabelColor,
  });

  @override
  State<SearchAppBar> createState() => _SearchAppBarState();

  @override
  Size get preferredSize => Size.fromHeight(showTabs ? 90.h : 56.h);
}

class _SearchAppBarState extends State<SearchAppBar> {
  @override
  void initState() {
    super.initState();
    // 添加搜索框监听器
    if (widget.onSearchChanged != null) {
      widget.searchController.addListener(() {
        widget.onSearchChanged!(widget.searchController.text);
      });
    }
  }

  @override
  void dispose() {
    // 移除监听器以防止内存泄漏
    if (widget.onSearchChanged != null) {
      widget.searchController.removeListener(() {
        widget.onSearchChanged!(widget.searchController.text);
      });
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: widget.leading,
      automaticallyImplyLeading: widget.automaticallyImplyLeading ?? true,
      // 标题区域：支持搜索框或文本标题
      title: widget.showSearchField
          ? Container(
              height: 36.h,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: TextField(
                controller: widget.searchController,
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: AppTextStyles.labelMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20.sp,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                      vertical: AppInsets.gap8(),
                      horizontal: AppInsets.gapW2()),
                  isDense: true,
                ),
              ),
            )
          : (widget.titleWidget ??
              Text(
                widget.titleText ?? '',
                style: widget.titleTextStyle,
              )),
      centerTitle: widget.centerTitle, // 标题是否居中
      backgroundColor:
          widget.backgroundColor ?? Theme.of(context).colorScheme.surface,
      elevation: widget.elevation,
      actions: widget.actions, // 添加actions支持
      bottom: widget.showTabs
          ? PreferredSize(
              preferredSize: Size.fromHeight(48.h),
              child: Container(
                color: widget.backgroundColor,
                child: TabBar(
                  controller: widget.tabController,
                  tabAlignment: widget.tabAlignment ?? TabAlignment.center,
                  isScrollable: widget.tabIsScrollable ?? true,
                  labelColor: widget.tabLabelColor,
                  unselectedLabelColor: widget.tabUnselectedLabelColor,
                  labelStyle: widget.tabLabelStyle,
                  unselectedLabelStyle: widget.tabUnselectedLabelStyle,
                  indicatorWeight: (widget.tabIndicatorWeight ?? 2).h,
                  indicatorSize: TabBarIndicatorSize.label,
                  tabs: widget.tabTitles
                      .map((title) => Tab(text: title))
                      .toList(),
                ),
              ),
            )
          : null,
    );
  }
}
