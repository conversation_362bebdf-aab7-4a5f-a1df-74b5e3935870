import 'package:flutter/material.dart';
import 'package:hyt/l10n/app_localizations.dart';

enum MessageType {
  system,
  activity,
  order;

  String displayName(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    switch (this) {
      case MessageType.system:
        return l10n?.systemMessage ?? '系统消息';
      case MessageType.activity:
        return l10n?.activityMessage ?? '活动消息';
      case MessageType.order:
        return l10n?.orderMessage ?? '订单消息';
    }
  }

  // 获取消息类型对应的图标
  IconData get icon {
    switch (this) {
      case MessageType.system:
        return Icons.notifications;
      case MessageType.order:
        return Icons.local_shipping;
      case MessageType.activity:
        return Icons.verified_user;
    }
  }

  // 获取消息类型对应的主色调
  Color primaryColor(BuildContext context) {
    final scheme = Theme.of(context).colorScheme;
    switch (this) {
      case MessageType.system:
        return scheme.primary;
      case MessageType.order:
        return scheme.secondary;
      case MessageType.activity:
        return scheme.tertiary;
    }
  }

  // 获取消息类型对应的背景色
  Color backgroundColor(BuildContext context) {
    final scheme = Theme.of(context).colorScheme;
    switch (this) {
      case MessageType.system:
        return scheme.primary.withAlpha(40);
      case MessageType.order:
        return scheme.secondary.withValues(alpha: 0.12);
      case MessageType.activity:
        return scheme.tertiary.withValues(alpha: 0.12);
    }
  }

  // 获取消息类型对应的标签文本
  String tagText(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    switch (this) {
      case MessageType.system:
        return l10n?.system ?? '系统';
      case MessageType.order:
        return l10n?.orders ?? '订单';
      case MessageType.activity:
        return l10n?.activity ?? '活动';
    }
  }

  static MessageType parseMessageType(dynamic value) {
    try {
      final typeString = (value as String).toLowerCase();
      return MessageType.values.byName(typeString); // 通过名称匹配枚举
    } catch (e) {
      // 处理无效类型，返回默认值或抛出异常
      throw FormatException('无效的消息类型: $value');
    }
  }
} // 消息类型枚举
