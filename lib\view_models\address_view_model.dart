import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hyt/view_models/base_view_model.dart';

/// 地址确认页面的ViewModel，负责处理业务逻辑和状态管理
class AddressViewModel extends BaseViewModel {
  // 地址状态管理
  Map<String, String>? _loadingAddress;
  Map<String, String>? _unloadingAddress;
  Map<String, String>? _selectedAddress;

  LatLng _selectedPosition = LatLng(30.25, 120.16); // 默认位置杭州

  bool? _isLoadingAddress; // 是否为装货地址

  // 地址历史记录
  final List<Map<String, String>> _addressHistory = [];

  // 表单控制器

  // 地图标记点
  final Set<Marker> markers = {};

  // 状态管理的getter
  Map<String, String>? get loadingAddress => _loadingAddress;
  Map<String, String>? get unloadingAddress => _unloadingAddress;
  Map<String, String>? get selectedAddress => _selectedAddress;
  LatLng get selectedPosition => _selectedPosition;
  bool? get isLoadingAddress => _isLoadingAddress;
  set selectedAddress(value) => _selectedAddress = value;
  set selectedPosition(value) => _selectedPosition = value;

  // 地址历史记录getter
  List<Map<String, String>> get addressHistory => _addressHistory;

  // 构造函数
  AddressViewModel() {
    _initMarker();
  }

  /// 初始化地图标记
  void _initMarker() {
    if (_selectedAddress != null) {
      markers.clear();
      markers.add(
        Marker(
          markerId: const MarkerId('selected_location'),
          position: _selectedPosition,
          infoWindow: InfoWindow(
            title: _selectedAddress!['name'] ?? '',
            snippet: _selectedAddress!['address'] ?? '',
          ),
        ),
      );
    }
  }

  /// 设置选中的地址和位置
  void setSelectedAddress({
    required Map<String, String> address,
  }) {
    _selectedAddress = address;
    _selectedPosition =
        LatLng(double.parse(address['lat']!), double.parse(address['lng']!));
    notifyListeners();
  }

  /// 设置是否为装货地址
  void setIsLoadingAddress(bool isLoading) {
    _isLoadingAddress = isLoading;
    notifyListeners();
  }

  /// 验证并保存地址信息
  void validateAndSaveAddress(BuildContext context) {
    //Todo：给地址增加补充的门牌信息
  }

  /// 更新装货地址
  void updateLoadingAddress(Map<String, String> address) {
    _loadingAddress = address;
    notifyListeners();
  }

  /// 更新卸货地址
  void updateUnloadingAddress(Map<String, String> address) {
    _unloadingAddress = address;
    notifyListeners();
  }

  /// 清除所有地址信息
  void clearAddresses() {
    _loadingAddress = null;
    _unloadingAddress = null;
    notifyListeners();
  }
}
