import 'package:flutter/material.dart';
import 'package:hyt/widgets/app_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/providers/language_provider.dart';
import 'package:hyt/l10n/app_localizations.dart';
import 'package:hyt/services/settings_manager.dart';
import 'package:hyt/widgets/unified_button.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/styles/app_theme.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  // 设置管理器
  final SettingsManager _settingsManager = SettingsManager();

  bool _notificationEnabled = true;
  bool _soundEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  // 加载设置
  Future<void> _loadSettings() async {
    await _settingsManager.init();

    final notificationEnabled = await _settingsManager.isNotificationEnabled;
    final soundEnabled = await _settingsManager.isSoundEnabled;

    setState(() {
      _notificationEnabled = notificationEnabled;
      _soundEnabled = soundEnabled;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context)?.generalSettings ?? '通用设置',
          style: AppTextStyles.headline3.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: AppInsets.gap4()),

            // 外观设置
            _buildSettingsCard(
              title: AppLocalizations.of(context)?.appearanceSettings ?? '外观设置',
              children: [
                _buildLanguageSelector(),
              ],
            ),

            SizedBox(height: AppInsets.gap4()), // 区块间隔与 Profile 对齐

            // 通知设置
            _buildSettingsCard(
              title:
                  AppLocalizations.of(context)?.notificationSettings ?? '通知设置',
              children: [
                _buildSwitchTile(
                  icon: FontAwesomeIcons.bell,
                  title:
                      AppLocalizations.of(context)?.pushNotifications ?? '推送通知',
                  subtitle:
                      AppLocalizations.of(context)?.pushNotificationsDesc ??
                          '接收订单状态、系统消息通知',
                  value: _notificationEnabled,
                  onChanged: (value) {
                    setState(() {
                      _notificationEnabled = value;
                    });
                    _settingsManager.setNotificationEnabled(value);
                  },
                ),
                _buildSwitchTile(
                  icon: FontAwesomeIcons.volumeHigh,
                  title: AppLocalizations.of(context)?.soundAlerts ?? '声音提醒',
                  subtitle: AppLocalizations.of(context)?.soundAlertsDesc ??
                      '新消息时播放提示音',
                  value: _soundEnabled,
                  onChanged: (value) async {
                    await _settingsManager.setSoundEnabled(value);
                    setState(() {
                      _soundEnabled = value;
                    });
                  },
                ),
              ],
            ),
            SizedBox(height: AppInsets.gap4()),
            // 隐私安全
            _buildSettingsCard(
              title: AppLocalizations.of(context)?.privacySecurity ?? '隐私安全',
              children: [
                _buildActionTile(
                  icon: FontAwesomeIcons.shield,
                  iconColor: Theme.of(context).colorScheme.primary,
                  title: AppLocalizations.of(context)?.privacyPolicy ?? '隐私政策',
                  subtitle: AppLocalizations.of(context)?.privacyPolicyDesc ??
                      '查看隐私保护政策',
                  onTap: () {
                    _showInfoDialog(
                        AppLocalizations.of(context)?.privacyPolicy ?? '隐私政策',
                        AppLocalizations.of(context)?.privacyPolicyContent ??
                            '这里将显示详细的隐私政策内容...');
                  },
                ),
                _buildActionTile(
                  icon: FontAwesomeIcons.fileContract,
                  iconColor: Theme.of(context).colorScheme.primary,
                  title: AppLocalizations.of(context)?.userAgreement ?? '用户协议',
                  subtitle: AppLocalizations.of(context)?.userAgreementDesc ??
                      '查看用户服务协议',
                  onTap: () {
                    _showInfoDialog(
                        AppLocalizations.of(context)?.userAgreement ?? '用户协议',
                        AppLocalizations.of(context)?.userAgreementContent ??
                            '这里将显示详细的用户协议内容...');
                  },
                ),
                _buildActionTile(
                  icon: FontAwesomeIcons.userShield,
                  iconColor: Theme.of(context).colorScheme.primary,
                  title:
                      AppLocalizations.of(context)?.accountSecurity ?? '账户安全',
                  subtitle: AppLocalizations.of(context)?.accountSecurityDesc ??
                      '管理密码、绑定手机等',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(AppLocalizations.of(context)
                                  ?.accountSecurityDeveloping ??
                              '账户安全功能开发中...')),
                    );
                  },
                ),
              ],
            ),

            SizedBox(height: AppInsets.gap4()),

            // 关于应用
            _buildSettingsCard(
              title: AppLocalizations.of(context)?.aboutApp ?? '关于应用',
              children: [
                _buildActionTile(
                  icon: FontAwesomeIcons.circleInfo,
                  iconColor: Theme.of(context).colorScheme.primary,
                  title: AppLocalizations.of(context)?.appVersion ?? '应用版本',
                  subtitle: 'v1.0.0',
                  trailing: Text(
                    AppLocalizations.of(context)?.checkUpdate ?? '检查更新',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.blue.shade600,
                    ),
                  ),
                  onTap: _checkForUpdates,
                ),
                _buildActionTile(
                  icon: FontAwesomeIcons.star,
                  iconColor: Theme.of(context).colorScheme.primary,
                  title: AppLocalizations.of(context)?.rateApp ?? '评价应用',
                  subtitle:
                      AppLocalizations.of(context)?.rateAppDesc ?? '在应用商店给我们评分',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content: Text(
                              AppLocalizations.of(context)?.thankYouSupport ??
                                  '感谢您的支持！')),
                    );
                  },
                ),
                _buildActionTile(
                  icon: FontAwesomeIcons.envelope,
                  iconColor: Theme.of(context).colorScheme.primary,
                  title: AppLocalizations.of(context)?.feedback ?? '意见反馈',
                  subtitle: AppLocalizations.of(context)?.feedbackDesc ??
                      '向我们反馈问题或建议',
                  onTap: () {
                    _showFeedbackDialog();
                  },
                ),
              ],
            ),

            SizedBox(height: AppInsets.gap24()),
          ],
        ),
      ),
    );
  }

  // 构建设置卡片
  Widget _buildSettingsCard({
    required String title,
    required List<Widget> children,
  }) {
    return AppCard(
      margin: EdgeInsets.symmetric(horizontal: 12.w),
      padding: AppInsets.functionCardPadding(),
      borderRadius: 12,
      elevation: 0,
      outlineAlpha: 0.6,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: AppInsets.functionCardHeaderPadding(),
            child: Text(
              title,
              style: AppTextStyles.bodySmall.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  // 构建语言选择器
  Widget _buildLanguageSelector() {
    return Consumer<LanguageProvider>(
      builder: (context, languageProvider, child) {
        return _buildActionTile(
          icon: FontAwesomeIcons.language,
          iconColor: Theme.of(context).colorScheme.primary,
          title: AppLocalizations.of(context)?.languageSettings ?? '语言设置',
          subtitle:
              '${AppLocalizations.of(context)?.current ?? '当前'}：${languageProvider.getLanguageName(languageProvider.currentLocale)}',
          onTap: () => _showLanguageDialog(languageProvider),
        );
      },
    );
  }

  // 构建开关设置项
  Widget _buildSwitchTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    Widget? trailing,
  }) {
    return ListTile(
      dense: AppListTileTokens.dense,
      visualDensity: AppListTileTokens.visualDensity,
      contentPadding: AppListTileTokens.contentPadding(),
      leading: Container(
        width: AppListTileTokens.leadingSize(),
        height: AppListTileTokens.leadingSize(),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: AppListTileTokens.iconSize(),
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.bodySmall.copyWith(
          color: Colors.grey.shade600,
        ),
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (trailing != null) trailing,
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }

  // 构建操作设置项
  Widget _buildActionTile({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return ListTile(
      dense: AppListTileTokens.dense,
      visualDensity: AppListTileTokens.visualDensity,
      contentPadding: AppListTileTokens.contentPadding(),
      leading: Container(
        width: AppListTileTokens.leadingSize(),
        height: AppListTileTokens.leadingSize(),
        decoration: BoxDecoration(
          color: iconColor.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(
          icon,
          color: iconColor,
          size: AppListTileTokens.iconSize(),
        ),
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.bodySmall.copyWith(
          color: Colors.grey.shade600,
        ),
      ),
      trailing: trailing ??
          Icon(
            Icons.chevron_right,
            color: Colors.grey.shade400,
            size: AppListTileTokens.chevronSize(),
          ),
      onTap: onTap,
    );
  }

  // 显示语言选择对话框
  void _showLanguageDialog(LanguageProvider languageProvider) {
    AppDialog.showCustom(
      context: context,
      child: AlertDialog(
        title: Text(AppLocalizations.of(context)?.selectLanguage ?? '选择语言'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: LanguageProvider.supportedLocales
              .map((locale) => _buildLanguageOption(locale, languageProvider))
              .toList(),
        ),
        actions: [
          UnifiedButton.text(
            text: AppLocalizations.of(context)?.cancel ?? '取消',
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
    );
  }

  // 构建语言选项
  Widget _buildLanguageOption(
      Locale locale, LanguageProvider languageProvider) {
    return RadioListTile<Locale>(
      title: Text(languageProvider.getLanguageName(locale)),
      value: locale,
      groupValue: languageProvider.currentLocale,
      onChanged: (value) {
        if (value != null) {
          languageProvider.changeLanguage(value);
          Navigator.pop(context);
        }
      },
    );
  }

  // 检查更新
  void _checkForUpdates() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content:
            Text(AppLocalizations.of(context)?.latestVersion ?? '当前已是最新版本'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  // 显示信息对话框
  void _showInfoDialog(String title, String content) {
    AppDialog.showConfirm(
      context: context,
      title: title,
      content: content,
    );
  }

  // 显示意见反馈对话框
  void _showFeedbackDialog() {
    final TextEditingController feedbackController = TextEditingController();

    AppDialog.showCustom(
      context: context,
      child: AlertDialog(
        title: Text(AppLocalizations.of(context)?.feedback ?? '意见反馈'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: feedbackController,
              maxLines: 4,
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)?.feedbackHint ??
                    '请输入您的反馈内容...',
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          UnifiedButton.text(
            text: AppLocalizations.of(context)?.cancel ?? '取消',
            onPressed: () => Navigator.pop(context),
          ),
          UnifiedButton.text(
            text: AppLocalizations.of(context)?.submit ?? '提交',
            onPressed: () {
              if (feedbackController.text.trim().isNotEmpty) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      content: Text(
                          AppLocalizations.of(context)?.thankYouFeedback ??
                              '感谢您的反馈！')),
                );
              }
            },
            color: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }
}
