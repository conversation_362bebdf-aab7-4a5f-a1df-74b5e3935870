import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppTextStyles {
  // 标题样式（对齐 Material 3 推荐尺寸）
  // 对应关系：
  // - headline1 ≈ TitleLarge (22)
  // - headline2 ≈ TitleMedium (16)
  // - headline3 ≈ TitleSmall (14)
  // - headline4 ≈ BodyMedium (12) 加粗用作小标题
  static TextStyle get headline1 => TextStyle(
        fontSize: 22.sp,
        fontWeight: FontWeight.bold,
      );

  static TextStyle get headline2 => TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.bold,
      );

  static TextStyle get headline3 => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.bold,
      );
  static TextStyle get headline4 => TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.bold,
      );

  // 正文样式（M3：Body Large 16, Body Medium 14, Body Small 12）
  static TextStyle get bodyLarge => TextStyle(
        fontSize: 16.sp,
      );

  static TextStyle get bodyMedium => TextStyle(
        fontSize: 14.sp,
      );

  static TextStyle get bodySmall => TextStyle(
        fontSize: 12.sp,
      );
  // 标签样式（M3：Label Large 14, Label Medium 12, Label Small 11）
  static TextStyle get labelLarge => TextStyle(
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
      );

  static TextStyle get labelMedium => TextStyle(
        fontSize: 12.sp,
      );
  static TextStyle get labelHint => TextStyle(
        fontSize: 12.sp,
      );

  static TextStyle get labelSmall => TextStyle(
        fontSize: 11.sp,
      );

  // 辅助方法 - 创建样式变体
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }
}
