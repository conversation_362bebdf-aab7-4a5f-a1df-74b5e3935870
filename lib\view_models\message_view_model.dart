// 消息业务逻辑处理

import 'dart:async';
import 'package:hyt/data/models/appuser.dart';
import 'package:hyt/data/models/base_message.dart';
import 'package:hyt/data/repos/base_message_repo.dart';
import 'package:hyt/data/repos/message_status_repo.dart';
import 'package:hyt/utils/enums.dart';
import 'package:hyt/utils/log_utils.dart';
import 'package:hyt/utils/app_error.dart';
import 'package:hyt/view_models/base_view_model.dart';

class MessageViewModel extends BaseViewModel {
  final BaseMessageRepo _baseMessageRepo;
  final MessageStatusRepo _messageHistoryRepo;
  MessageViewModel(this._baseMessageRepo, this._messageHistoryRepo);
  Timer? _syncTimer; //本地消息和后台同步计时器
  bool _isSyncing = false;
  final List<BaseMessage> _allMessages = []; //本地消息列表，包含所有类型的消息
  final List<Map<String, dynamic>> _pendingSyncOperations = []; // 待同步操作队列

  // 获取所有消息
  List<BaseMessage> get allMessages => _allMessages;

  // 获取未读消息数量
  int get unreadCount => _allMessages.where((msg) => !msg.isRead).length;

  // 刷新全部消息（只请求一次后台）
  Future<void> refreshAllMessages(String userId) async {
    setLoading(true);
    clearError();
    try {
      final msgs = await _baseMessageRepo.getAllUserMessages(userId: userId);
      // 先获取新消息，然后一次性替换，避免中间状态导致的RangeError
      _allMessages.clear();
      _allMessages.addAll(msgs);
      notifyListeners();
      _startSyncTimer();
    } catch (e, st) {
      LogUtils.e('刷新消息失败', e, st);
      setError(e.toString());
      throw AppError('刷新消息失败', cause: e, stackTrace: st);
    } finally {
      setLoading(false);
    }
  }

  // 根据消息类型过滤消息
  List<BaseMessage> getMessagesByType(MessageType messageType) {
    return _allMessages.where((msg) => msg.type == messageType).toList();
  }

  // 清空所有消息（本地状态）
  void clearAllMessagesLocal() {
    if (_allMessages.isNotEmpty) {
      _allMessages.clear();
      notifyListeners();
    }
  }

  // 删除本地消息
  void deleteMessageLocal(String messageId, AppUser user) {
    final index = _allMessages.indexWhere((msg) => msg.id == messageId);
    if (index != -1) {
      _allMessages.removeAt(index);
      notifyListeners();
      // 添加到同步队列
      _pendingSyncOperations.add({
        'type': 'delete',
        'messageId': messageId,
        'userId': user.id,
        'timestamp': DateTime.now().millisecondsSinceEpoch
      });
    }
  }

  // 标记本地所有消息为已读
  void markAllAsReadLocal(AppUser user) {
    bool hasUnread = false;
    for (var message in _allMessages) {
      if (!message.isRead) {
        message.isRead = true;
        hasUnread = true;
        // 添加到同步队列
        _pendingSyncOperations.add({
          'type': 'markAsRead',
          'messageId': message.id,
          'userId': user.id,
          'timestamp': DateTime.now().millisecondsSinceEpoch
        });
      }
    }
    if (hasUnread) {
      notifyListeners();
    }
  }

  // 标记单条消息为已读
  void markAsReadLocal(String messageId, AppUser user) {
    final index = _allMessages.indexWhere((msg) => msg.id == messageId);
    if (index != -1 && !_allMessages[index].isRead) {
      _allMessages[index].isRead = true;
      notifyListeners();
      // 添加到同步队列
      _pendingSyncOperations.add({
        'type': 'markAsRead',
        'messageId': messageId,
        'userId': user.id,
        'timestamp': DateTime.now().millisecondsSinceEpoch
      });
    }
  }

  // 删除本地所有消息
  void deleteAllMessagesLocal(AppUser user) {
    if (_allMessages.isNotEmpty) {
      // 为每条消息添加删除操作到同步队列
      for (var message in _allMessages) {
        _pendingSyncOperations.add({
          'type': 'delete',
          'messageId': message.id,
          'userId': user.id,
          'timestamp': DateTime.now().millisecondsSinceEpoch
        });
      }
      // 清空本地消息列表
      _allMessages.clear();
      notifyListeners();
    }
  }

  // 删除消息状态
  Future<void> deleteMessageStatus(String userId, String messageId) async {
    await _messageHistoryRepo.deleteMessageStatus(
      userId: userId,
      messageId: messageId,
    );
  }

  // 发送订单消息（司机抢单成功后通知用户）
  Future<bool> sendOrderMessage({
    required String content,
    required String orderId,
    required String receiverId,
    required String senderId,
  }) async {
    try {
      // 创建订单消息
      final message = BaseMessage(
        type: MessageType.order,
        content: content,
        createdAt: DateTime.now(),
        receiverId: receiverId,
        orderId: orderId,
        isRead: false,
      );

      // 发送消息并保存到数据库
      final result = await _baseMessageRepo.sendOrderMessage(
        message: message,
      );

      // 如果发送成功，将消息添加到本地消息列表
      if (result) {
        _allMessages.insert(0, message); // 添加到列表开头
        notifyListeners();
      }

      return result;
    } catch (e, st) {
      LogUtils.e('发送订单消息失败', e, st);
      setError(e.toString());
      throw AppError('发送订单消息失败', cause: e, stackTrace: st);
    }
  }

  void _startSyncTimer() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(
        const Duration(minutes: 5), (_) => syncPendingOperations());
  }
  // 同步消息操作

  Future<void> syncPendingOperations() async {
    if (_isSyncing || _pendingSyncOperations.isEmpty) return;

    _isSyncing = true;
    try {
      // 复制当前待处理操作以避免并发修改
      final operationsToSync =
          List<Map<String, dynamic>>.from(_pendingSyncOperations);

      // 清空队列以允许新操作加入
      _pendingSyncOperations.clear();

      // 使用ViewModel同步操作
      final operations = List.of(operationsToSync);
      _pendingSyncOperations.clear();
      for (final op in operations) {
        switch (op['type']) {
          case 'markAsRead':
            await _messageHistoryRepo.markMessageAsRead(
                messageId: op['messageId'],
                userId: op['userId'],
                timestamp: op['timestamp']);
            break;
          case 'delete':
            await _messageHistoryRepo.deleteMessageStatus(
                messageId: op['messageId'], userId: op['userId']);
            break;
        }
      }
    } catch (e, st) {
      LogUtils.e('消息同步失败', e, st);
      // 不抛至 UI，交由后台重试或下次触发再同步
    } finally {
      _isSyncing = false;
    }
  }

  @override
  void dispose() {
    _syncTimer?.cancel();
    super.dispose();
  }
}
