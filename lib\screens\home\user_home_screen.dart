//货主首页
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:hyt/view_models/address_view_model.dart';
import 'package:hyt/screens/address/address_search_screen.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:provider/provider.dart';
// 导入新的车型数据文件
import 'package:hyt/data/vehicle_type_data.dart';
import 'package:hyt/l10n/app_localizations.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/styles/app_theme.dart';

class UserHomeScreen extends StatefulWidget {
  const UserHomeScreen({super.key});

  @override
  State<UserHomeScreen> createState() => _UserHomeScreenState();
}

class _UserHomeScreenState extends State<UserHomeScreen> {
  String _serviceType = 'local'; // 默认选择长途货运
  // 修改：使用新的数据结构
  List<VehicleTypeData> _currentVehicleTypes = [];
  String? _selectedVehicleId; // 用于存储选中的车辆 ID

  @override
  void initState() {
    super.initState();
    // 初始化时加载对应服务类型的车型
    _currentVehicleTypes = getVehicleTypesForService(_serviceType);
  }

  // 移除旧的 _updateVehicleTypes 方法

  void _switchServiceType(String type) {
    setState(() {
      _serviceType = type;
      // 修改：从公共数据源获取车型
      _currentVehicleTypes = getVehicleTypesForService(type);
      _selectedVehicleId = null; // 切换服务类型时清空车型选择
    });
  }

  void _selectVehicle(String vehicleId) {
    setState(() {
      _selectedVehicleId = vehicleId;
    });
  }

  // 新增：导航到地址选择页面的函数
  void _navigateToAddressSelection(BuildContext context,
      {required bool isLoading}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddressSearchScreen(isLoading: isLoading),
      ),
    );
    // 注意：这里不再需要手动更新状态，因为地址选择页面会通过 Provider 更新状态，
    // 而 HomePage 会通过 Provider.of<AddressViewModel>(context) 自动获取最新状态。
  }

  @override
  Widget build(BuildContext context) {
    final addressViewModel = context.watch<AddressViewModel>();
    final loadingAddress =
        addressViewModel.loadingAddress ?? {'name': '', 'address': ''};

    return Scaffold(
        body: SafeArea(
      child: Column(
        children: [
          // 服务类型选择
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(color: Colors.grey.shade200),
              ),
            ),
            child: Row(
              children: [
                _buildServiceTypeButton(
                    'local',
                    AppLocalizations.of(context)!.longDistanceFreight,
                    FontAwesomeIcons.truckMoving),
                SizedBox(width: AppInsets.gapW8()),
                _buildServiceTypeButton(
                    'long',
                    AppLocalizations.of(context)!.sameCityExpress,
                    FontAwesomeIcons.truckFast),
              ],
            ),
          ),

          // 主体内容
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // 车型选择
                  AppCard(
                    margin: EdgeInsets.symmetric(horizontal: 0.w),
                    padding: EdgeInsets.only(
                        left: 8.w, top: 8.h, bottom: 8.h, right: 8.w),
                    borderRadius: 12,
                    elevation: 0,
                    outlineAlpha: 0.6,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            crossAxisSpacing: 8.w,
                            mainAxisSpacing: 8.h,
                            childAspectRatio: 1.3,
                          ),
                          // 修改：使用 _currentVehicleTypes
                          itemCount: _currentVehicleTypes.length,
                          itemBuilder: (context, index) {
                            // 修改：使用 VehicleInfo 对象
                            final vehicle = _currentVehicleTypes[index];
                            // 修改：使用 _selectedVehicleId
                            final isSelected = _selectedVehicleId == vehicle.id;

                            return GestureDetector(
                              // 修改：传递 vehicle.id
                              onTap: () => _selectVehicle(vehicle.id),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? Colors.blue.shade100
                                      : Colors.grey.shade50,
                                  borderRadius: BorderRadius.circular(8.r),
                                  border: isSelected
                                      ? Border.all(
                                          color: Colors.blue.shade500, width: 2)
                                      : Border.all(color: Colors.grey.shade200),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      // 修改：使用 vehicle.icon
                                      vehicle.icon,
                                      color: Colors.blue.shade400,
                                      size: 16.sp,
                                    ),
                                    SizedBox(height: AppInsets.gap4()),
                                    Text(
                                      // 修改：使用 vehicle.name
                                      vehicle.name,
                                      style: AppTextStyles.bodySmall,
                                    ),
                                    Text(
                                      // 修改：使用 vehicle.description
                                      vehicle.description,
                                      style: AppTextStyles.labelSmall,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                  // 地址选择栏 - 修改这部分
                  Container(
                    margin: EdgeInsets.only(
                        left: 8.w, top: 8.h, bottom: 4.h, right: 8.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.r),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 5,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // 装货地址
                        InkWell(
                          onTap: () {
                            // 调用新添加的函数
                            _navigateToAddressSelection(context,
                                isLoading: true);
                          },
                          child: Padding(
                            padding: EdgeInsets.all(12.w),
                            child: Row(
                              children: [
                                // 替换图标为加粗文字
                                Container(
                                  width: 30.w,
                                  height: 30.w,
                                  decoration: BoxDecoration(
                                    color: Colors.blue.shade50,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Text(
                                      '装',
                                      style: AppTextStyles.headline4.copyWith(
                                        color: Colors.blue.shade500,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: AppInsets.gapW12()),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // 修改这里，显示地址名称和具体地址
                                      loadingAddress['name']!.isEmpty
                                          ? Text(
                                              AppLocalizations.of(context)!
                                                  .selectLoadingAddress,
                                              style: AppTextStyles.labelMedium
                                                  .copyWith(
                                                fontWeight: FontWeight.w500,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            )
                                          : Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  loadingAddress['name']!,
                                                  style: AppTextStyles
                                                      .bodyMedium
                                                      .copyWith(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                Text(
                                                  loadingAddress['address']!,
                                                  style:
                                                      AppTextStyles.labelMedium,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                              ],
                                            ),
                                    ],
                                  ),
                                ),
                                Icon(
                                  Icons.chevron_right,
                                  color: Colors.grey.shade400,
                                  size: 20.sp,
                                ),
                              ],
                            ),
                          ),
                        ),

                        // 分隔线
                        Divider(
                          height: 1,
                          thickness: 1,
                          indent: 6.w,
                          endIndent: 6.w,
                          color: Colors.grey.shade300,
                        ),

                        // 卸货地址
                        InkWell(
                          onTap: () {
                            // 调用新添加的函数
                            _navigateToAddressSelection(context,
                                isLoading: false);
                          },
                          child: Padding(
                            padding: EdgeInsets.all(12.w),
                            child: Row(
                              children: [
                                // 替换图标为加粗文字
                                Container(
                                  width: 30.w,
                                  height: 30.w,
                                  decoration: BoxDecoration(
                                    color: Colors.green.shade50,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Center(
                                    child: Text(
                                      '卸',
                                      style: AppTextStyles.headline4.copyWith(
                                        color: Colors.green.shade500,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(width: AppInsets.gapW12()),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        AppLocalizations.of(context)!
                                            .selectUnloadingAddress,
                                        style:
                                            AppTextStyles.labelMedium.copyWith(
                                          fontWeight: FontWeight.w500,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                                Icon(
                                  Icons.chevron_right,
                                  color: Colors.grey.shade400,
                                  size: 20.sp,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: AppInsets.gap4()),

                  // 快捷功能入口
                  Container(
                    padding: EdgeInsets.all(14.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: _buildQuickAction(
                                Icons.person_add,
                                AppLocalizations.of(context)!.driverJoin,
                                AppLocalizations.of(context)!
                                    .becomeProfessionalFleet,
                              ),
                            ),
                            SizedBox(width: AppInsets.gapW12()),
                            Expanded(
                              child: _buildQuickAction(
                                Icons.inventory,
                                AppLocalizations.of(context)!.logisticsPublish,
                                AppLocalizations.of(context)!.quickPublishCargo,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: AppInsets.gap4()),

                  // 叫车流程说明
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 12.w),
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.callCarProcess,
                              style: AppTextStyles.labelSmall.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade400,
                              ),
                            ),
                            Text(
                              AppLocalizations.of(context)!.commonQuestions,
                              style: AppTextStyles.labelSmall.copyWith(
                                color: Colors.blue.shade400,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: AppInsets.gap4()),
                        Row(
                          children: [
                            _buildStepItem(
                                AppLocalizations.of(context)!
                                    .stepSelectCarAddress,
                                true),
                            _buildStepItem(
                                AppLocalizations.of(context)!
                                    .stepFillRequirements,
                                false),
                            _buildStepItem(
                                AppLocalizations.of(context)!.stepConfirmCall,
                                false),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // 新增：广告和营销区域
                  SizedBox(height: AppInsets.gap4()), // 添加一些垂直间距
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppLocalizations.of(context)!.hotActivities, // 区域标题
                          style: AppTextStyles.labelMedium
                              .copyWith(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: AppInsets.gap8()),
                        // 使用 Row 或 GridView 来放置图片广告
                        Row(
                          children: [
                            Expanded(
                              child: _buildMarketingCard(
                                'assets/images/newbie.jpg', // 替换为你的图片路径
                                AppLocalizations.of(context)!.newbieExclusive,
                                () {
                                  // TODO: 跳转到活动1页面
                                  print('Tapped Promo 1');
                                },
                              ),
                            ),
                            SizedBox(width: AppInsets.gapW8() + 2.w),
                            Expanded(
                              child: _buildMarketingCard(
                                'assets/images/share.jpg', // 替换为你的图片路径
                                AppLocalizations.of(context)!.rechargeDiscount,
                                () {
                                  // TODO: 跳转到活动2页面
                                  print('Tapped Promo 2');
                                },
                              ),
                            ),
                          ],
                        ),
                        // 可以根据需要添加更多行或不同的布局
                        // SizedBox(height: 10.h),
                        // _buildMarketingCard(
                        //   'assets/images/banner1.png', // 替换为你的图片路径 (横幅示例)
                        //   '长途特惠',
                        //   () {
                        //     // TODO: 跳转到长途特惠页面
                        //     print('Tapped Banner 1');
                        //   },
                        //   isBanner: true, // 标记为横幅样式
                        // ),
                      ],
                    ),
                  ),
                  SizedBox(height: AppInsets.gap4()), // 底部留白
                ],
              ),
            ),
          ),
        ],
      ),
    ));
  }

  Widget _buildServiceTypeButton(String type, String label, IconData icon) {
    final isSelected = _serviceType == type;

    return GestureDetector(
      onTap: () => _switchServiceType(type),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.shade600 : Colors.transparent,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 14.sp,
              color: isSelected ? Colors.white : Colors.grey.shade500,
            ),
            SizedBox(width: AppInsets.gapW6()),
            Text(label,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: isSelected ? Colors.white : Colors.black,
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAction(IconData icon, String title, String subtitle) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: Colors.blue.shade100,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.blue.shade600,
              size: 20.sp,
            ),
          ),
          SizedBox(width: AppInsets.gapW12()),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  subtitle,
                  style: AppTextStyles.labelSmall,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepItem(String text, bool isFirst) {
    return Expanded(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (!isFirst) ...[
            SizedBox(width: AppInsets.gapW4()),
            Icon(
              FontAwesomeIcons.chevronRight,
              size: 10.sp,
              color: Colors.grey.shade400,
            ),
          ],
          Text(text, style: AppTextStyles.labelSmall),
        ],
      ),
    );
  }

  // 新增：构建营销卡片的辅助方法
  Widget _buildMarketingCard(String imagePath, String title, VoidCallback onTap,
      {bool isBanner = false}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(51),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1), // changes position of shadow
            ),
          ],
        ),
        child: ClipRRect(
          // 使用 ClipRRect 来裁剪图片圆角
          borderRadius: BorderRadius.circular(8.r),
          child: Stack(
            // Stack 的 alignment 属性现在不再重要，因为没有叠加元素了
            // alignment: isBanner ? Alignment.bottomLeft : Alignment.center,
            children: [
              // 使用 Image.asset 加载本地图片
              Container(
                height: isBanner ? 100.h : 80.h, // 横幅可以更高
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(imagePath), // 加载图片
                    fit: BoxFit.cover, // 图片填充方式
                    onError: (exception, stackTrace) {
                      print('Error loading image: $imagePath');
                    },
                  ),
                ),
              ),
              // 移除：在图片上叠加标题 (可选)
              // if (!isBanner) Container( ... ),
              // 移除：横幅标题放在左下角
              // if (isBanner) Padding( ... ),
            ],
          ),
        ),
      ),
    );
  }
}
