/// 时区处理工具类
/// 确保应用中所有时间操作的一致性
class TimezoneUtils {
  /// 从数据库字符串解析时间并转换为本地时间
  /// 数据库存储的是UTC时间，需要转换为本地时间显示
  static DateTime? parseFromDatabase(String? timeString) {
    if (timeString == null || timeString.isEmpty) {
      return null;
    }
    try {
      return DateTime.parse(timeString).toLocal();
    } catch (e) {
      print('时间解析错误: $timeString, 错误: $e');
      return null;
    }
  }

  /// 将本地时间转换为UTC时间字符串，用于保存到数据库
  /// 确保数据库中存储的都是UTC时间
  static String toDatabase(DateTime dateTime) {
    return dateTime.toUtc().toIso8601String();
  }

  /// 获取当前时间的UTC字符串，用于保存到数据库
  static String nowToDatabase() {
    return DateTime.now().toUtc().toIso8601String();
  }

  /// 获取当前本地时间
  static DateTime nowLocal() {
    return DateTime.now();
  }

  /// 检查两个时间是否在同一天（本地时间）
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// 格式化时间显示（本地时间）
  static String formatForDisplay(DateTime dateTime, {String pattern = 'yyyy-MM-dd HH:mm'}) {
    // 这里可以根据需要添加国际化支持
    final year = dateTime.year.toString();
    final month = dateTime.month.toString().padLeft(2, '0');
    final day = dateTime.day.toString().padLeft(2, '0');
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    
    switch (pattern) {
      case 'yyyy-MM-dd HH:mm':
        return '$year-$month-$day $hour:$minute';
      case 'yyyy-MM-dd':
        return '$year-$month-$day';
      case 'HH:mm':
        return '$hour:$minute';
      case 'MM-dd HH:mm':
        return '$month-$day $hour:$minute';
      default:
        return '$year-$month-$day $hour:$minute';
    }
  }

  /// 计算时间差（分钟）
  static int differenceInMinutes(DateTime startTime, DateTime endTime) {
    return endTime.difference(startTime).inMinutes;
  }

  /// 计算时间差（小时）
  static int differenceInHours(DateTime startTime, DateTime endTime) {
    return endTime.difference(startTime).inHours;
  }

  /// 计算时间差（天）
  static int differenceInDays(DateTime startTime, DateTime endTime) {
    return endTime.difference(startTime).inDays;
  }

  /// 验证时间逻辑是否正确（例如：接单时间不能早于下单时间）
  static bool validateTimeLogic({
    DateTime? createTime,
    DateTime? acceptTime,
    DateTime? pickupTime,
    DateTime? deliveryTime,
    DateTime? completionTime,
  }) {
    final times = <DateTime?>[];
    
    if (createTime != null) times.add(createTime);
    if (acceptTime != null) times.add(acceptTime);
    if (pickupTime != null) times.add(pickupTime);
    if (deliveryTime != null) times.add(deliveryTime);
    if (completionTime != null) times.add(completionTime);

    // 检查时间顺序是否正确
    for (int i = 0; i < times.length - 1; i++) {
      if (times[i] != null && times[i + 1] != null) {
        if (times[i]!.isAfter(times[i + 1]!)) {
          print('时间逻辑错误: ${times[i]} 晚于 ${times[i + 1]}');
          return false;
        }
      }
    }
    
    return true;
  }

  /// 调试用：打印时间信息
  static void debugPrintTimeInfo(String label, DateTime? dateTime) {
    if (dateTime == null) {
      print('$label: null');
      return;
    }
    
    print('$label:');
    print('  本地时间: ${formatForDisplay(dateTime)}');
    print('  UTC时间: ${dateTime.toUtc().toIso8601String()}');
    print('  时区偏移: ${dateTime.timeZoneOffset}');
  }
}
