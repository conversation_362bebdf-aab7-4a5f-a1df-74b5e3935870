//地址选择页面，分为装货和卸货两个不同类型
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/screens/address/address_confirm_screen.dart';
import 'package:hyt/view_models/address_search_view_model.dart';
import 'package:provider/provider.dart';
import 'package:hyt/widgets/empty_state.dart';
import 'package:hyt/styles/app_theme.dart';

class AddressSearchScreen extends StatefulWidget {
  final bool isLoading; // true为装货地址，false为卸货地址

  const AddressSearchScreen({
    super.key,
    required this.isLoading,
  });

  @override
  State<AddressSearchScreen> createState() => _AddressSearchScreenState();
}

class _AddressSearchScreenState extends State<AddressSearchScreen> {
  // ViewModel实例
  late AddressSearchViewModel _addressSearchViewModel;

  @override
  void initState() {
    super.initState();

    // 初始化ViewModel
    _addressSearchViewModel =
        Provider.of<AddressSearchViewModel>(context, listen: false);

    // 加载地址历史记录
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _addressSearchViewModel.loadAddressHistory();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 执行地址搜索 - 直接调用ViewModel中的方法
  Future<void> _searchPlaces(String query) async {
    try {
      await _addressSearchViewModel.searchPlaces(query);
    } catch (e) {
      debugPrint('Error searching places: $e');
    }
  } // 清空历史记录

  void _clearHistory() async {
    await _addressSearchViewModel.clearHistory();
  }

  @override
  Widget build(BuildContext context) {
    // 使用Consumer监听ViewModel的变化
    return Consumer<AddressSearchViewModel>(
        builder: (context, viewModel, child) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          automaticallyImplyLeading: false,
          title: Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: TextField(
              controller: viewModel.searchController,
              decoration: InputDecoration(
                hintText: widget.isLoading ? '输入装货地址' : '输入卸货地址',
                hintStyle: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 14.sp,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: Colors.grey.shade600,
                  size: 20.sp,
                ),
                suffixIcon: viewModel.searchController.text.isNotEmpty
                    ? IconButton(
                        icon: Icon(Icons.clear, size: 18.sp),
                        onPressed: () {
                          viewModel.searchController.clear();
                          viewModel.loadAddressHistory();
                          viewModel.clearSearchResults();
                        },
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: AppInsets.gapW8(),
                  vertical: 0, // 减小垂直内边距（按需保持 0）
                ),
                // 添加垂直对齐属性，使提示文本垂直居中
                isDense: true,
                alignLabelWithHint: true,
              ),
              textAlignVertical: TextAlignVertical.center, // 文本垂直居中
              onChanged: (value) {
                _searchPlaces(value);
              },
              textInputAction: TextInputAction.search,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                viewModel.clearSearchResults();
                viewModel.searchController.clear();
                Navigator.pop(context);
              },
              child: Text(
                '取消',
                style: TextStyle(
                  color: Colors.black87,
                  fontSize: 14.sp,
                ),
              ),
            ),
          ],
        ),
        body: Container(
          color: Colors.grey.shade50,
          child: viewModel.isSearching
              // 正在搜索，显示进度条
              ? const Center(child: CircularProgressIndicator())
              // 搜索结果列表
              : viewModel.searchResults.isNotEmpty
                  ? ListView.builder(
                      itemCount: viewModel.searchResults.length,
                      itemBuilder: (context, index) {
                        final result = viewModel.searchResults[index];
                        return Container(
                          margin: EdgeInsets.symmetric(vertical: 4.h),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          child: ListTile(
                            leading: Icon(
                              Icons.location_on,
                              color: Colors.orange,
                              size: 24.sp,
                            ),
                            title: Text(
                              result.primaryText,
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            subtitle: Text(
                              result.fullText,
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey.shade600,
                              ),
                            ),
                            onTap: () {
                              viewModel.onPlaceSelected(
                                  result, widget.isLoading);
                              // 导航到地址确认页面
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => AddressConfirmScreen(
                                      isLoading: widget.isLoading),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    )
                  // 历史记录列表
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 8.h),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.access_time,
                                    size: 18.sp,
                                    color: Colors.grey.shade600,
                                  ),
                                  SizedBox(width: 8.w),
                                  Text(
                                    '历史记录',
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.grey.shade800,
                                    ),
                                  ),
                                ],
                              ),
                              if (viewModel.addressHistory.isNotEmpty)
                                TextButton(
                                  onPressed: _clearHistory,
                                  child: Text(
                                    '清空',
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: viewModel.addressHistory.isEmpty
                              ? ListView(
                                  padding: AppInsets.pageGutter().add(
                                      EdgeInsets.only(top: AppInsets.gap8())),
                                  children: [
                                    SizedBox(height: AppInsets.gap12()),
                                    EmptyState(
                                      useCard: true,
                                      cardMargin: EdgeInsets.zero,
                                      title: '暂无历史记录',
                                    ),
                                  ],
                                )
                              : ListView.builder(
                                  itemCount: viewModel.addressHistory.length,
                                  itemBuilder: (context, index) {
                                    final address =
                                        viewModel.addressHistory[index];
                                    return Dismissible(
                                      key: Key(
                                          'history_${index}_${address['name']}'),
                                      background: Container(
                                        color: Colors.red,
                                        alignment: Alignment.centerRight,
                                        padding: EdgeInsets.only(right: 16.w),
                                        child: Icon(
                                          Icons.delete,
                                          color: Colors.white,
                                          size: 24.sp,
                                        ),
                                      ),
                                      direction: DismissDirection.endToStart,
                                      onDismissed: (direction) {
                                        // 左滑删除历史记录
                                        viewModel.removeFromHistory(address);
                                      },
                                      child: Container(
                                        margin:
                                            EdgeInsets.symmetric(vertical: 4.h),
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(4.r),
                                        ),
                                        child: ListTile(
                                          leading: Icon(
                                            Icons.history,
                                            color: Colors.blue,
                                            size: 24.sp,
                                          ),
                                          title: Text(
                                            address['name'] ?? '',
                                            style: TextStyle(
                                              fontSize: 14.sp,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                          subtitle: Text(
                                            address['address'] ?? '',
                                            style: TextStyle(
                                              fontSize: 12.sp,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                          onTap: () {
                                            // 选择历史记录
                                            viewModel.onHistoryItemSelected(
                                              address,
                                            );
                                            // 导航到地址确认页面
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    AddressConfirmScreen(
                                                        isLoading:
                                                            widget.isLoading),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    );
                                  },
                                ),
                        ),
                      ],
                    ),
        ),
      );
    });
  }
}
