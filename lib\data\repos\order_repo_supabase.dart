import 'package:hyt/constants/order_status_constants.dart';
import 'package:hyt/data/models/order_status_history.dart';
import 'package:hyt/data/models/reviews.dart';
import 'package:hyt/utils/timezone_utils.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hyt/utils/num_utils.dart';
import '../models/order.dart';
import 'order_repo.dart';

/// 基于Supabase的订单仓库实现
class OrderRepoSupabase implements OrderRepo {
  final SupabaseClient _supabaseClient;
  final String _tableName = 'orders';
  final String _reviewTableName = 'reviews';

  OrderRepoSupabase(this._supabaseClient);

  @override
  Future<Order?> getOrderById(String orderId) async {
    try {
      final data = await _supabaseClient
          .from(_tableName)
          .select()
          .eq('order_id', orderId)
          .maybeSingle();

      return data != null ? Order.fromMap(data) : null;
    } catch (e) {
      print('获取订单详情失败: $e');
      return null;
    }
  }

  @override
  Future<Order> createOrder(Order order) async {
    try {
      final data = await _supabaseClient
          .from(_tableName)
          .insert(order.toMap())
          .select()
          .single();

      return Order.fromMap(data);
    } catch (e) {
      print('创建订单失败: $e');
      rethrow;
    }
  }

  @override
  Future<bool> deleteOrder(String orderId) async {
    try {
      await _supabaseClient.from(_tableName).delete().eq('order_id', orderId);
      return true;
    } catch (e) {
      print('删除订单失败: $e');
      rethrow;
    }
  }

  @override
  Future<bool> markOrderAsDeletedByRole(String orderId, String role) async {
    try {
      // 根据角色将订单标记为已删除（逻辑删除）
      if (role == 'shipper') {
        await _supabaseClient
            .from(_tableName)
            .update({'is_deleted_by_customer': true}).eq('order_id', orderId);
        return true;
      } else if (role == 'driver') {
        await _supabaseClient
            .from(_tableName)
            .update({'is_deleted_by_driver': true}).eq('order_id', orderId);
        return true;
      }
      return false;
    } catch (e) {
      print('标记订单为用户已删除失败: $e');
      return false;
    }
  }

  @override
  Future<Order?> processPayment(String orderId) async {
    try {
      // 1. 检查订单状态是否允许支付
      final orderData = await _supabaseClient
          .from(_tableName)
          .select('status_code, price')
          .eq('order_id', orderId)
          .maybeSingle();

      if (orderData == null ||
          orderData['status_code'] != OrderStatusConstants.STATUS_DELIVERED) {
        return null; // 订单不存在或状态不允许支付
      }

      // 2. 调用支付接口
      // 模拟支付接口调用
      await Future.delayed(const Duration(seconds: 1));
      //模拟支付成功，更新司机收入、平台费用字段，暂定10%平台费
      final double price = safeToDouble(orderData['price']);
      await _supabaseClient.from(_tableName).update({
        'driver_earnings': price * 0.9,
        'platform_fee': price * 0.1
      }).eq('order_id', orderId);

      // final paymentResult =
      //     await _supabaseClient.rpc('process_payment', params: {
      //   'order_id': orderId,
      //   'amount': orderData['price'],
      // });

      // if (paymentResult == null || paymentResult['success'] != true) {
      //   return null; // 支付失败
      // }

      // 3. 更新订单状态和支付时间，并返回更新后的订单
      final updatedData = await _supabaseClient
          .from(_tableName)
          .update({
            'status_code': OrderStatusConstants.STATUS_COMPLETED,
            'completion_time': TimezoneUtils.nowToDatabase(),
          })
          .eq('order_id', orderId)
          .select()
          .single();

      return Order.fromMap(updatedData);
    } catch (e) {
      print('订单支付处理失败: $e');
      return null;
    }
  }

  @override
  Future<Order?> acceptOrder(
      {required String orderId,
      required String driverId,
      required int statusCode,
      required String driverName,
      required String driverPhone,
      required String vehiclePlate}) async {
    try {
      // 首先检查订单状态是否为可抢单状态(0)
      final orderData = await _supabaseClient
          .from(_tableName)
          .select('status_code')
          .eq('order_id', orderId)
          .maybeSingle();

      if (orderData == null || orderData['status_code'] != 0) {
        return null;
      }

      // 更新订单信息并返回更新后的订单
      final updatedData = await _supabaseClient
          .from(_tableName)
          .update({
            'status_code': 1,
            'driver_id': driverId,
            'driver_name': driverName,
            'driver_phone': driverPhone,
            'vehicle_plate': vehiclePlate,
            'accept_time': TimezoneUtils.nowToDatabase()
          })
          .eq('order_id', orderId)
          .select()
          .single();

      return Order.fromMap(updatedData);
    } catch (e) {
      print('抢单操作失败: $e');
      return null;
    }
  }

  @override
  Future<List<Order>> getDriverOrders(String driverId) async {
    try {
      final response = await _supabaseClient
          .from(_tableName)
          .select()
          .eq('driver_id', driverId)
          .eq('is_deleted_by_driver', false) // 过滤掉被司机删除的订单
          .order('create_time', ascending: false);

      return response.map((data) => Order.fromMap(data)).toList();
    } catch (e) {
      print('获取司机订单失败: $e');
      return [];
    }
  }

  @override
  Future<List<Order>> getShipperOrders(String shipperId) async {
    try {
      final response = await _supabaseClient
          .from(_tableName)
          .select()
          .eq('customer_id', shipperId)
          .eq('is_deleted_by_customer', false)
          .order('create_time', ascending: false);

      return response.map((data) => Order.fromMap(data)).toList();
    } catch (e) {
      print('获取货主订单失败: $e');
      return [];
    }
  }

  @override
  Future<bool> cancelOrder(String orderId, int statusCode) async {
    try {
      await _supabaseClient.from(_tableName).update({
        'status_code': statusCode,
        'cancel_time': TimezoneUtils.nowToDatabase()
      }).eq('order_id', orderId);
      return true;
    } catch (e) {
      print('取消订单失败: $e');
      return false;
    }
  }

  @override
  Future<Order?> deliverOrder(String orderId) async {
    try {
      // 首先检查订单状态是否为可完成状态(11)
      final orderData = await _supabaseClient
          .from(_tableName)
          .select('status_code')
          .eq('order_id', orderId)
          .maybeSingle();
      if (orderData == null ||
          orderData['status_code'] !=
              OrderStatusConstants.STATUS_TRANSPORTING) {
        return null; // 订单不存在或状态不允许送达
      }

      // 更新订单状态并返回更新后的订单
      final updatedData = await _supabaseClient
          .from(_tableName)
          .update({
            'status_code': OrderStatusConstants.STATUS_DELIVERED,
            'actual_arrival_time': TimezoneUtils.nowToDatabase(),
          })
          .eq('order_id', orderId)
          .select()
          .single();

      return Order.fromMap(updatedData);
    } catch (e) {
      print('确认送达失败: $e');
      return null;
    }
  }

  @override
  Future<Order?> pickupOrder(String orderId) async {
    try {
      // 更新订单状态并返回更新后的订单
      final updatedData = await _supabaseClient
          .from(_tableName)
          .update({
            'status_code': OrderStatusConstants.STATUS_TRANSPORTING,
            'updated_at': TimezoneUtils.nowToDatabase(),
          })
          .eq('order_id', orderId)
          .select()
          .single();

      return Order.fromMap(updatedData);
    } catch (e) {
      print('确认取货失败: $e');
      return null;
    }
  }

  @override
  Future<List<OrderStatusHistory>> getOrderStatusHistory(String orderId) async {
    try {
      final data = await _supabaseClient
          .from('order_status_history')
          .select()
          .eq('order_id', orderId)
          .order('timestamp', ascending: false);

      return data
          .map<OrderStatusHistory>((json) => OrderStatusHistory.fromMap(json))
          .toList();
    } catch (e) {
      print('获取订单状态历史失败: $e');
      return [];
    }
  }

  @override
  Future<List<Order>> getAllPendingOrders() async {
    try {
      final response = await _supabaseClient
          .from(_tableName)
          .select()
          .eq('status_code', 0)
          .order('create_time', ascending: false);

      return response.map((data) => Order.fromMap(data)).toList();
    } catch (e) {
      print('获取所有待接单订单失败: $e');
      return [];
    }
  }

  @override
  Future<bool> evaluateOrder(String orderId, String rating, String comment) {
    throw UnimplementedError();
  }

  @override
  Future<bool> addOrderStatusHistory(
      String orderId, OrderStatusHistory historyItem) async {
    try {
      await _supabaseClient.from('order_status_history').insert({
        'order_id': orderId,
        'status_code': historyItem.statusCode,
        'remark': historyItem.remark,
      });
      return true;
    } catch (e) {
      print('添加订单状态历史记录失败: $e');
      return false;
    }
  }

  @override
  Future<bool> submitReview(Review review) async {
    try {
      // 验证：不能给自己评价
      if (review.fromUserId == review.toUserId) {
        print('提交评价失败: 不能给自己评价');
        return false;
      }

      // 验证：必须有有效的用户ID
      if (review.fromUserId.isEmpty || review.toUserId.isEmpty) {
        print('提交评价失败: 用户ID不能为空');
        return false;
      }

      await _supabaseClient.from(_reviewTableName).insert(review.toMap());
      return true;
    } catch (e) {
      print('提交评价失败: $e');
      return false;
    }
  }

  @override
  Future<List<Review>> getReviewsForOrder(String orderId) async {
    try {
      final data = await _supabaseClient
          .from(_reviewTableName)
          .select()
          .eq('order_id', orderId)
          .order('created_at', ascending: false);
      return data.map<Review>((json) => Review.fromMap(json)).toList();
    } catch (e) {
      print('获取订单评价失败: $e');
      return [];
    }
  }

  @override
  Future<List<Review>> getReviewsForUser(String userId, {String? role}) async {
    try {
      var query = _supabaseClient
          .from(_reviewTableName)
          .select()
          .eq('to_user_id', userId);
      if (role != null) {
        query = query.eq('to_user_role', role);
      }
      final data = await query.order('created_at', ascending: false);
      return data.map<Review>((json) => Review.fromMap(json)).toList();
    } catch (e) {
      print('获取用户评价失败: $e');
      return [];
    }
  }
}
