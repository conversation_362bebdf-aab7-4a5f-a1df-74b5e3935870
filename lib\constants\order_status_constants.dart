import 'package:flutter/material.dart';
import 'package:hyt/l10n/app_localizations.dart';

/// 订单状态常量类
/// 用于统一管理订单状态相关的颜色、图标和描述信息
class OrderStatusConstants {
  // 状态码常量定义
  static const int STATUS_WAITING = 0; // 待接单
  static const int STATUS_ACCEPTED = 1; // 已接单
  static const int STATUS_TRANSPORTING = 11; // 运输中
  static const int STATUS_DELIVERED = 12; // 已送达
  static const int STATUS_COMPLETED = 2; // 已完成
  static const int STATUS_CANCELED = 3; // 已取消

  /// 订单状态码对应的颜色
  static Map<int, Color> statusColors(BuildContext context) => {
        STATUS_WAITING: Theme.of(context).colorScheme.tertiary,
        STATUS_ACCEPTED: Theme.of(context).colorScheme.primary,
        STATUS_TRANSPORTING: Theme.of(context).colorScheme.primary,
        STATUS_DELIVERED: Theme.of(context).colorScheme.primary,
        STATUS_COMPLETED: Theme.of(context).colorScheme.secondary,
        STATUS_CANCELED: Theme.of(context).colorScheme.error,
      };

  /// 订单状态码对应的状态名称
  static const Map<int, String> statusName = {
    STATUS_WAITING: '待接单',
    STATUS_ACCEPTED: '已接单',
    STATUS_TRANSPORTING: '运输中',
    STATUS_DELIVERED: '已送达',
    STATUS_COMPLETED: '已完成',
    STATUS_CANCELED: '已取消',
  };

  /// 订单状态码对应的图标
  static const Map<int, IconData> statusIcons = {
    STATUS_WAITING: Icons.access_time,
    STATUS_ACCEPTED: Icons.local_shipping,
    STATUS_TRANSPORTING: Icons.local_shipping,
    STATUS_DELIVERED: Icons.location_on,
    STATUS_COMPLETED: Icons.check_circle,
    STATUS_CANCELED: Icons.cancel,
  };

  /// 订单状态码对应的描述
  static const Map<int, String> statusDescriptions = {
    STATUS_WAITING: '等待司机接单中，请耐心等待',
    STATUS_ACCEPTED: '司机已接单，正在前往取货地点',
    STATUS_TRANSPORTING: '货物正在运输中',
    STATUS_DELIVERED: '货物已送达目的地',
    STATUS_COMPLETED: '订单已完成',
    STATUS_CANCELED: '订单已取消',
  };

  /// 获取订单状态对应的颜色
  static Color getStatusColor(int statusCode, BuildContext context) {
    return statusColors(context)[statusCode] ??
        Theme.of(context).colorScheme.outlineVariant;
  }

  /// 获取订单状态对应的图标
  static IconData getStatusIcon(int statusCode) {
    return statusIcons[statusCode] ?? Icons.help_outline;
  }

  /// 获取订单状态对应的描述（本地化）
  static String getStatusDescriptionLocalized(
      int statusCode, BuildContext context) {
    final l10n = AppLocalizations.of(context);
    switch (statusCode) {
      case STATUS_WAITING:
        return l10n?.descStatusWaiting ?? statusDescriptions[STATUS_WAITING]!;
      case STATUS_ACCEPTED:
        return l10n?.descStatusAccepted ?? statusDescriptions[STATUS_ACCEPTED]!;
      case STATUS_TRANSPORTING:
        return l10n?.descStatusTransporting ??
            statusDescriptions[STATUS_TRANSPORTING]!;
      case STATUS_DELIVERED:
        return l10n?.descStatusDelivered ??
            statusDescriptions[STATUS_DELIVERED]!;
      case STATUS_COMPLETED:
        return l10n?.descStatusCompleted ??
            statusDescriptions[STATUS_COMPLETED]!;
      case STATUS_CANCELED:
        return l10n?.descStatusCanceled ?? statusDescriptions[STATUS_CANCELED]!;
      default:
        return '未知状态';
    }
  }

  /// 获取订单状态名称（本地化）
  static String getStatusNameLocalized(int statusCode, BuildContext context) {
    final l10n = AppLocalizations.of(context);
    switch (statusCode) {
      case STATUS_WAITING:
        return l10n?.statusWaiting ?? statusName[STATUS_WAITING]!;
      case STATUS_ACCEPTED:
        return l10n?.statusAccepted ?? statusName[STATUS_ACCEPTED]!;
      case STATUS_TRANSPORTING:
        return l10n?.statusTransporting ?? statusName[STATUS_TRANSPORTING]!;
      case STATUS_DELIVERED:
        return l10n?.statusDelivered ?? statusName[STATUS_DELIVERED]!;
      case STATUS_COMPLETED:
        return l10n?.statusCompleted ?? statusName[STATUS_COMPLETED]!;
      case STATUS_CANCELED:
        return l10n?.statusCanceled ?? statusName[STATUS_CANCELED]!;
      default:
        return '未知状态';
    }
  }

  /// 订单状态码列表（按照流程顺序）
  static const List<int> orderStatusSequence = [
    STATUS_WAITING,
    STATUS_ACCEPTED,
    STATUS_TRANSPORTING,
    STATUS_DELIVERED,
    STATUS_COMPLETED
  ];

  /// 获取当前状态在流程中的进度（0-1之间）
  static double getStatusProgress(int statusCode) {
    if (statusCode == 3) return 0; // 已取消订单没有进度
    final index = orderStatusSequence.indexOf(statusCode);
    if (index == -1) return 0;
    return (index + 1) / orderStatusSequence.length;
  }
}
