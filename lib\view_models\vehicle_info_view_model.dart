//车辆数据状态管理
import 'package:flutter/material.dart';
import 'package:hyt/data/models/vehicle_info.dart';
import 'package:hyt/view_models/base_view_model.dart';
import 'package:hyt/utils/log_utils.dart';

class VehicleInfoViewModel extends BaseViewModel {
  late VehicleInfo _vehicleInfo;

  // Controllers for text fields
  late TextEditingController _plateNumberController;
  late TextEditingController _vehicleTypeController;
  late TextEditingController _vehicleLengthController;
  late TextEditingController _loadCapacityController;
  late TextEditingController _vehicleVolumeController;
  late TextEditingController _lastInspectionDateController;

  // Getters for UI access
  VehicleInfo get vehicleInfo => _vehicleInfo;
  TextEditingController get plateNumberController => _plateNumberController;
  TextEditingController get vehicleTypeController => _vehicleTypeController;
  TextEditingController get vehicleLengthController => _vehicleLengthController;
  TextEditingController get loadCapacityController => _loadCapacityController;
  TextEditingController get vehicleVolumeController => _vehicleVolumeController;
  TextEditingController get lastInspectionDateController =>
      _lastInspectionDateController;

  // Flag to indicate if data has been loaded
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  // 构造函数
  VehicleInfoViewModel() {
    // 初始化一个空的 VehicleInfo 对象，避免空指针异常
    _vehicleInfo = VehicleInfo.fromMap({
      'plateNumber': '',
      'vehicleType': '',
      'vehicleLength': '',
      'loadCapacity': '',
      'vehicleVolume': '',
      'drivingLicense': '未上传',
      'vehicleLicense': '未上传',
      'insuranceInfo': '未上传',
      'lastInspectionDate': '',
    });
    _initializeControllers();
  }

  void _initializeControllers() {
    // 确保 controller 初始化时有默认值
    _plateNumberController =
        TextEditingController(text: _vehicleInfo.plateNumber);
    _vehicleTypeController =
        TextEditingController(text: _vehicleInfo.vehicleType);
    _vehicleLengthController =
        TextEditingController(text: _vehicleInfo.vehicleLength);
    _loadCapacityController =
        TextEditingController(text: _vehicleInfo.loadCapacity);
    _vehicleVolumeController =
        TextEditingController(text: _vehicleInfo.vehicleVolume);
    _lastInspectionDateController =
        TextEditingController(text: _vehicleInfo.lastInspectionDate);
  }

  // 异步加载数据方法
  Future<void> loadInitialData() async {
    try {
      setLoading(true);
      clearError();

      // 模拟网络请求
      await Future.delayed(const Duration(milliseconds: 500));

      // 模拟获取的数据
      final initialData = {
        'plateNumber': '京A12345',
        'vehicleType': '金杯面包车',
        'vehicleLength': '4.2',
        'loadCapacity': '1.5',
        'vehicleVolume': '12',
        'drivingLicense': '已上传',
        'vehicleLicense': '已上传',
        'insuranceInfo': '已上传',
        'lastInspectionDate': '2023-12-15',
      };

      _vehicleInfo = VehicleInfo.fromMap(initialData);

      // 更新控制器
      _plateNumberController.text = _vehicleInfo.plateNumber;
      _vehicleTypeController.text = _vehicleInfo.vehicleType;
      _vehicleLengthController.text = _vehicleInfo.vehicleLength;
      _loadCapacityController.text = _vehicleInfo.loadCapacity;
      _vehicleVolumeController.text = _vehicleInfo.vehicleVolume;
      _lastInspectionDateController.text = _vehicleInfo.lastInspectionDate;

      _isInitialized = true;
    } catch (e, st) {
      setError('加载车辆信息失败: $e');
      LogUtils.e(error!, e, st);
    } finally {
      setLoading(false);
    }
  }

  // Method to save changes
  void saveChanges() {
    // Update the model from controllers
    _vehicleInfo.plateNumber = _plateNumberController.text;
    _vehicleInfo.vehicleType = _vehicleTypeController.text;
    _vehicleInfo.vehicleLength = _vehicleLengthController.text;
    _vehicleInfo.loadCapacity = _loadCapacityController.text;
    _vehicleInfo.vehicleVolume = _vehicleVolumeController.text;
    _vehicleInfo.lastInspectionDate = _lastInspectionDateController.text;

    // Here you would typically call an API to save the data
    print('Saving data via Provider: ${_vehicleInfo.toMap()}');

    // Notify listeners about the change (optional, if UI needs immediate reflection beyond controllers)
    notifyListeners();

    // Optionally show feedback (can be done in UI layer as well)
  }

  // Example method to update a document status
  void updateDrivingLicenseStatus(String newStatus) {
    if (_vehicleInfo.drivingLicenseStatus != newStatus) {
      _vehicleInfo.drivingLicenseStatus = newStatus;
      print('Driving License Status Updated: $newStatus');
      notifyListeners(); // Notify UI to rebuild
    }
  }

  // Add similar methods for other documents if needed
  void updateVehicleLicenseStatus(String newStatus) {
    if (_vehicleInfo.vehicleLicenseStatus != newStatus) {
      _vehicleInfo.vehicleLicenseStatus = newStatus;
      print('Vehicle License Status Updated: $newStatus');
      notifyListeners();
    }
  }

  void updateInsuranceInfoStatus(String newStatus) {
    if (_vehicleInfo.insuranceInfoStatus != newStatus) {
      _vehicleInfo.insuranceInfoStatus = newStatus;
      print('Insurance Info Status Updated: $newStatus');
      notifyListeners();
    }
  }

  // Dispose controllers when the provider is disposed
  @override
  void dispose() {
    _plateNumberController.dispose();
    _vehicleTypeController.dispose();
    _vehicleLengthController.dispose();
    _loadCapacityController.dispose();
    _vehicleVolumeController.dispose();
    _lastInspectionDateController.dispose();
    super.dispose();
  }
}
