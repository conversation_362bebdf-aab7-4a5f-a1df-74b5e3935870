import 'package:hyt/utils/enums.dart';
import 'package:hyt/utils/timezone_utils.dart';

/// 基础消息类
class BaseMessage {
  final String? id; // 消息唯一标识符，可为空，数据库自动生成
  final MessageType type; // 消息类型
  final String content; // 消息内容
  final DateTime createdAt; // 消息创建时间
  final String? receiverId; // 接收者ID
  final String? orderId; // 订单ID
  bool isRead; // 是否未读
  bool isDeleted; // 是否已删除

  BaseMessage({
    this.id,
    required this.type,
    required this.content,
    required this.createdAt,
    this.receiverId,
    this.orderId,
    this.isRead = false,
    this.isDeleted = false,
  });
  // 从Map创建BaseMessage对象
  factory BaseMessage.fromMap(Map<String, dynamic> map) {
    return BaseMessage(
      id: map['id'] as String?,
      type: MessageType.parseMessageType(map['type']),
      content: map['content'] as String,
      createdAt:
          TimezoneUtils.parseFromDatabase(map['created_at']) ?? DateTime.now(),
      receiverId: map['receiver_id'] as String?,
      orderId: map['order_id'] as String?,
      isRead: map['is_read'] as bool,
      isDeleted: map['is_deleted'] as bool,
    );
  }
  // 将BaseMessage对象转换为Map
  Map<String, dynamic> toMap({bool forInsert = false}) {
    final map = {
      'type': type.name,
      'content': content,
      'created_at': TimezoneUtils.toDatabase(createdAt),
      'receiver_id': receiverId,
      'order_id': orderId,
      'is_read': isRead,
      'is_deleted': isDeleted,
    };
    if (!forInsert && id != null) {
      map['id'] = id;
    }
    return map;
  }
}
