import 'package:hyt/data/models/appuser.dart';
import 'package:bcrypt/bcrypt.dart';
import 'package:hyt/data/repos/appuser_repo.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hyt/view_models/base_view_model.dart';
import 'package:hyt/utils/log_utils.dart';

/// 用户视图模型，处理用户相关的业务逻辑
class AppUserViewModel extends BaseViewModel {
  final AppUserRepo _userRepo;
  bool _isLoggedIn = false;

  AppUser? _currentUser;
  String _currentRole = 'shipper';

  bool get isLoggedIn => _isLoggedIn;
  AppUser? get currentUser => _currentUser;
  String get currentRole => _currentRole;

  AppUserViewModel(this._userRepo);

  set isLoggedIn(bool value) {
    _isLoggedIn = value;
    notifyListeners();
  }

  /// 用户注册
  Future<bool> register(AppUser user) async {
    try {
      setLoading(true);
      clearError();

      // 调用Repository处理用户注册
      final newUser = await _userRepo.createUser(user);
      _currentUser = newUser;
      return true;
    } catch (e, st) {
      setError('注册失败: $e');
      LogUtils.e(error!, e, st);
      return false;
    } finally {
      setLoading(false);
    }
  }

  /// 用户登录
  Future<bool> login(String phone, String password) async {
    try {
      setLoading(true);
      clearError();

      final user = await _userRepo.getUserByPhone(phone);
      if (user == null) {
        setError('用户不存在');
        return false;
      }

      // 验证密码哈希
      if (!BCrypt.checkpw(password, user.passwordHash)) {
        setError('密码错误');
        return false;
      }
      _currentUser = user;
      // 优先使用用户的当前角色，如果为空则使用第一个角色
      _currentRole = user.userRole.isNotEmpty
          ? user.userRole
          : (user.roles.isNotEmpty ? user.roles.first : 'shipper');
      _isLoggedIn = true;
      if (user.id != null) {
        await saveUserToSharedPrefs(user.id); // 登录成功后保存用户信息
      }

      return true;
    } catch (e, st) {
      setError('登录失败: $e');
      LogUtils.e(error!, e, st);
      return false;
    } finally {
      setLoading(false);
    }
  }

  /// 用户登出
  Future<void> logout() async {
    _currentUser = null;
    _isLoggedIn = false;
    _currentRole = 'shipper';
    clearSharedPrefsUser(); // 登出时清除本地存储的用户信息
    notifyListeners();
  }

  /// 更新用户信息
  Future<bool> updateUserInfo(AppUser user) async {
    try {
      setLoading(true);
      clearError();

      final updatedUser = await _userRepo.updateUser(user);
      _currentUser = updatedUser;
      _currentRole = user.roles.isNotEmpty ? user.roles.first : 'shipper';
      return true;
    } catch (e, st) {
      setError('更新用户信息失败: $e');
      LogUtils.e(error!, e, st);
      return false;
    } finally {
      setLoading(false);
    }
  }

  Future<bool> switchRole(String newRole) async {
    try {
      if (_currentRole == newRole) return true; // 已经是目标角色
      setLoading(true);
      clearError();
      if (_currentUser == null || !_currentUser!.roles.contains(newRole)) {
        setError('无权限切换到此角色');
        return false;
      }
      final updatedUser =
          await _userRepo.updateRole(_currentUser!.id!, newRole);
      _currentUser = updatedUser;
      _currentRole = newRole;
      //保存当前角色到SharedPreferences
      await saveUserToSharedPrefs(_currentUser!.id!);
      notifyListeners(); // 确保通知UI更新
      return true;
    } catch (e, st) {
      setError('角色切换失败: $e');
      LogUtils.e(error!, e, st);
      return false;
    } finally {
      setLoading(false);
    }
  }

  // 设置当前用户
  void setUser(AppUser user) {
    _currentUser = user;
    _isLoggedIn = true;
    // 优先使用用户的当前角色，确保角色设置合法
    if (user.userRole.isNotEmpty && user.roles.contains(user.userRole)) {
      _currentRole = user.userRole;
    } else if (user.roles.isNotEmpty && user.roles.contains(_currentRole)) {
      // 保留当前角色
    } else {
      _currentRole = user.roles.isNotEmpty ? user.roles.first : 'shipper';
    }
    notifyListeners();
  }

  // 保存用户信息到SharedPreferences
  Future<void> saveUserToSharedPrefs(String? userId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('userId', userId!);
    // 同时保存当前角色状态
    await prefs.setString('currentRole', _currentRole);
  }

  // 从SharedPreferences加载用户信息
  Future<void> loadUserFromSharedPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final userId = prefs.getString('userId'); // 使用统一的键名
    if (userId != null) {
      _currentUser = await _userRepo.getUserById(userId);
      _isLoggedIn = true;
      // 从SharedPreferences加载当前角色状态
      final savedRole = prefs.getString('currentRole');
      _currentRole = savedRole ??
          (_currentUser!.roles.isNotEmpty
              ? _currentUser!.roles.first
              : 'shipper');
      notifyListeners();
    }
  }

  // 清除SharedPreferences中的用户信息
  Future<void> clearSharedPrefsUser() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('userId'); // 使用统一的键名
    await prefs.remove('currentRole'); // 同时清除角色状态
  }

  /// 刷新当前用户信息
  Future<void> refreshCurrentUser() async {
    try {
      setLoading(true);
      clearError();

      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('userId');

      if (userId != null) {
        _currentUser = await _userRepo.getUserById(userId);
        _isLoggedIn = true;
        // 从SharedPreferences加载当前角色状态，确保刷新时角色不丢失
        final savedRole = prefs.getString('currentRole');
        _currentRole = savedRole ??
            (_currentUser!.roles.isNotEmpty
                ? _currentUser!.roles.first
                : 'shipper');
      } else {
        _currentUser = null;
        _isLoggedIn = false;
        _currentRole = 'shipper';
      }
    } catch (e, st) {
      setError('刷新用户信息失败: $e');
      LogUtils.e(error!, e, st);
      // 刷新失败时，如果用户数据为空，也要确保状态正确
      if (_currentUser == null) {
        _isLoggedIn = false;
        _currentRole = 'shipper';
      }
    } finally {
      setLoading(false);
    }
  }

  /// 根据用户ID获取用户信息（用于获取司机详细信息）
  Future<AppUser?> getUserById(String userId) async {
    try {
      return await _userRepo.getUserById(userId);
    } catch (e, st) {
      setError('获取用户信息失败: $e');
      LogUtils.e(error!, e, st);
      return null;
    }
  }
}
