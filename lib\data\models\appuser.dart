//用户实体模型
import 'package:hyt/utils/num_utils.dart';

class AppUser {
  final String? id;
  final String? nickName;
  final String phone;
  final String? avatar;
  final List<String> roles; // 用户角色列表，可以包含 "driver"(司机) 和 "shipper"(货主)
  String userRole; // 当前角色
  final bool isVerified; // 是否已实名认证

  // 司机特有属性
  final String? vehicleId; // 关联的车辆ID
  final double? rating; // 评分
  final int? completedOrders; // 已完成订单数
  final String? driverLicenseStatus; // 驾驶证状态
  final String? vehiclePlate; // 车牌号

  // 货主特有属性
  final String? companyName; // 公司名称（如果是企业用户）
  final String? userType; // 用户类型：个人/商户/企业

  // 用户密码哈希值
  final String passwordHash;

  AppUser({
    this.id,
    this.nickName,
    required this.phone,
    this.avatar,
    this.roles = const ['shipper'],
    this.userRole = 'shipper', // 默认角色
    this.isVerified = false,
    this.vehicleId,
    this.rating,
    this.completedOrders,
    this.driverLicenseStatus,
    this.vehiclePlate,
    this.companyName,
    this.userType,
    required this.passwordHash,
  });

  // 判断用户是否为司机
  bool get isDriver => roles.contains('driver');

  // 判断用户是否为货主
  bool get isShipper => roles.contains('shipper');

  // 从Map创建User对象
  factory AppUser.fromMap(Map<String, dynamic> map) {
    return AppUser(
      id: map['id'] ?? '',
      userRole: map['user_role'] ?? 'shipper',
      nickName: map['nick_name'] ?? '',
      phone: map['phone'] ?? '',
      avatar: map['avatar'] ?? '',
      roles: List<String>.from(map['roles'] ?? []),
      isVerified: map['is_verified'] ?? false,
      vehicleId: map['vehicle_id'],
      rating: safeToDouble(map['rating']),
      completedOrders: map['completed_orders'],
      vehiclePlate: map['vehicle_plate'],
      driverLicenseStatus: map['driver_license_status'],
      companyName: map['company_name'],
      userType: map['user_type'],
      passwordHash: map['password_hash'] ?? '',
    );
  }

  // 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nick_name': nickName,
      'phone': phone,
      'avatar': avatar,
      'roles': roles,
      'is_verified': isVerified,
      'user_role': userRole,
      'vehicle_id': vehicleId,
      'rating': rating,
      'completed_orders': completedOrders,
      'vehicle_plate': vehiclePlate,
      'driver_license_status': driverLicenseStatus,
      'company_name': companyName,
      'user_type': userType,
      'password_hash': passwordHash,
    };
  }

  copyWith({required String passwordHash}) {}
}
