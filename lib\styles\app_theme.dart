import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 全局主题与设计令牌（Material 3 风格）
class AppColors {
  static const brand = Color(0xFF1E88E5);
  static const danger = Color(0xFFD32F2F);
  static const success = Color(0xFF2E7D32);
  static const warning = Color(0xFFF57C00);
}

class AppRadius {
  static BorderRadius small = BorderRadius.circular(8.r);
  static BorderRadius medium = BorderRadius.circular(10.r);
  static BorderRadius large = BorderRadius.circular(12.r);
}

/// 全局统一的间距与内边距
class AppInsets {
  // 功能卡片：与 Profile 对齐的卡片内边距（M3）
  static EdgeInsets functionCardPadding() => EdgeInsets.only(bottom: 4.h);

  // 功能卡片标题区：与 Profile 对齐的标题内边距（M3）
  static EdgeInsets functionCardHeaderPadding() =>
      EdgeInsets.fromLTRB(12.w, 12.h, 16.w, 4.h);

  // —— 基于 Material 3 的 4dp 网格：标准间距（优先使用这些 gap）——
  static double gap2() => 2.h; // 极小间隔（紧凑列表）
  static double gap4() => 4.h; // 小间隔（卡片分组/区块间）
  static double gap6() => 6.h; // 卡片之间的默认间隔
  static double gap8() => 8.h; // 列表项之间/表单项之间
  static double gap12() => 12.h; // 区块内容内的段落间
  static double gap16() => 16.h; // 主要分区/卡片内主要内容间
  static double gap20() => 20.h; // 次要大间隔
  static double gap24() => 24.h; // 大区块间

  static double gap32() => 32.h; // 特大区块间

  // —— 通用 EdgeInsets 工厂（优先水平 12.w，与现有页面对齐）——
  static EdgeInsets all8() => EdgeInsets.all(8.w);
  static EdgeInsets all12() => EdgeInsets.all(12.w);
  static EdgeInsets all16() => EdgeInsets.all(16.w);
  static EdgeInsets h12() => EdgeInsets.symmetric(horizontal: 12.w);
  static EdgeInsets h16() => EdgeInsets.symmetric(horizontal: 16.w);
  static EdgeInsets v8() => EdgeInsets.symmetric(vertical: 8.h);
  static EdgeInsets v12() => EdgeInsets.symmetric(vertical: 12.h);

  // —— 横向 gap（4dp 网格，单位 w）——
  static double gapW2() => 2.w;
  static double gapW4() => 4.w;
  static double gapW6() => 6.w;
  static double gapW8() => 8.w;
  static double gapW12() => 12.w;
  static double gapW16() => 16.w;
  static double gapW20() => 20.w;
  static double gapW24() => 24.w;

  // 页面统一左右留白（与现有 12.w gutter 对齐）
  static EdgeInsets pageGutter() => EdgeInsets.symmetric(horizontal: 12.w);
}

/// 列表项（ListTile）统一尺寸与密度 - 与 Profile 功能卡片保持一致
class AppListTileTokens {
  AppListTileTokens._();
  static const VisualDensity visualDensity = VisualDensity.compact;
  static bool get dense => true;
  static EdgeInsets contentPadding() =>
      EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h);
  static double leadingSize() => 30.w;
  static double iconSize() => 16.w;
  static double chevronSize() => 22.sp;
}

ThemeData buildLightTheme() {
  final seed = AppColors.brand;
  final colorScheme =
      ColorScheme.fromSeed(seedColor: seed, brightness: Brightness.light);

  return ThemeData(
    useMaterial3: true,
    colorScheme: colorScheme,
    fontFamily: 'AlibabaPuHuiTi',

    // AppBar 主题（统一表层与前景色）
    appBarTheme: AppBarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      elevation: 0,
      centerTitle: false,
      iconTheme: IconThemeData(color: colorScheme.onSurface),
    ),

    // TabBar 主题（统一标签色、字体与指示器）
    tabBarTheme: TabBarTheme(
      labelColor: colorScheme.primary,
      unselectedLabelColor: colorScheme.onSurfaceVariant,
      indicatorColor: colorScheme.primary,
      indicatorSize: TabBarIndicatorSize.label,
      indicator: UnderlineTabIndicator(
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      dividerColor: Colors.transparent,
      labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
      unselectedLabelStyle: TextStyle(fontSize: 14.sp),
    ),

    // 输入框主题（统一填充、边框与提示色）
    inputDecorationTheme: InputDecorationTheme(
      isDense: true,
      filled: true,
      fillColor: colorScheme.surface,
      hintStyle: TextStyle(color: colorScheme.onSurfaceVariant),
      border: OutlineInputBorder(
        borderRadius: AppRadius.medium,
        borderSide: BorderSide(color: colorScheme.outlineVariant),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: AppRadius.medium,
        borderSide: BorderSide(color: colorScheme.outlineVariant),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: AppRadius.medium,
        borderSide: BorderSide(color: colorScheme.primary, width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: AppRadius.medium,
        borderSide: BorderSide(color: colorScheme.error),
      ),
      contentPadding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 12.w),
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: AppRadius.medium),
        minimumSize: Size(0, 40.h),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        shape: RoundedRectangleBorder(borderRadius: AppRadius.medium),
        side: BorderSide(color: colorScheme.primary),
        minimumSize: Size(0, 40.h),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        shape: RoundedRectangleBorder(borderRadius: AppRadius.medium),
        minimumSize: Size(0, 40.h),
      ),
    ),
    cardTheme: CardTheme(
      elevation: 1,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: AppRadius.small,
        side: BorderSide(color: colorScheme.outlineVariant),
      ),
    ),
    dialogTheme: DialogTheme(
      shape: RoundedRectangleBorder(borderRadius: AppRadius.large),
    ),
    snackBarTheme: SnackBarThemeData(
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: AppRadius.small),
    ),
  );
}

ThemeData buildDarkTheme() {
  final seed = AppColors.brand;
  final colorScheme =
      ColorScheme.fromSeed(seedColor: seed, brightness: Brightness.dark);

  return ThemeData(
    useMaterial3: true,
    colorScheme: colorScheme,
    fontFamily: 'AlibabaPuHuiTi',
    appBarTheme: AppBarTheme(
      backgroundColor: colorScheme.surface,
      foregroundColor: colorScheme.onSurface,
      elevation: 0,
      centerTitle: false,
      iconTheme: IconThemeData(color: colorScheme.onSurface),
    ),
    tabBarTheme: TabBarTheme(
      labelColor: colorScheme.primary,
      unselectedLabelColor: colorScheme.onSurfaceVariant,
      indicatorColor: colorScheme.primary,
      indicatorSize: TabBarIndicatorSize.label,
      dividerColor: Colors.transparent,
      labelStyle: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600),
      unselectedLabelStyle: TextStyle(fontSize: 14.sp),
    ),
    inputDecorationTheme: InputDecorationTheme(
      isDense: true,
      filled: true,
      fillColor: colorScheme.surface,
      hintStyle: TextStyle(color: colorScheme.onSurfaceVariant),
      border: OutlineInputBorder(
        borderRadius: AppRadius.medium,
        borderSide: BorderSide(color: colorScheme.outlineVariant),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: AppRadius.medium,
        borderSide: BorderSide(color: colorScheme.outlineVariant),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: AppRadius.medium,
        borderSide: BorderSide(color: colorScheme.primary, width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: AppRadius.medium,
        borderSide: BorderSide(color: colorScheme.error),
      ),
      contentPadding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 12.w),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: AppRadius.medium),
        minimumSize: Size(0, 40.h),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        shape: RoundedRectangleBorder(borderRadius: AppRadius.medium),
        side: BorderSide(color: colorScheme.primary),
        minimumSize: Size(0, 40.h),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        shape: RoundedRectangleBorder(borderRadius: AppRadius.medium),
        minimumSize: Size(0, 40.h),
      ),
    ),
    cardTheme: CardTheme(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: AppRadius.small,
        side: BorderSide(color: colorScheme.outlineVariant),
      ),
    ),
    dialogTheme: DialogTheme(
      shape: RoundedRectangleBorder(borderRadius: AppRadius.large),
    ),
    snackBarTheme: SnackBarThemeData(
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(borderRadius: AppRadius.small),
    ),
  );
}
