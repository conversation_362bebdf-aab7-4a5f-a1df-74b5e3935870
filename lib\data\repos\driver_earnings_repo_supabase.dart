import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:hyt/utils/num_utils.dart';
import 'package:hyt/data/models/driver_earnings.dart';
import 'package:hyt/data/repos/driver_earnings_repo.dart';

class DriverEarningsRepoSupabase implements DriverEarningsRepo {
  final SupabaseClient _supabaseClient;
  DriverEarningsRepoSupabase(this._supabaseClient);

  @override
  Future<DriverEarnings> getDriverEarnings(String driverId) async {
    try {
      final response = await _supabaseClient.rpc('get_driver_earnings_stats',
          params: {'driver_id_param': driverId}).single();

      final todayEarnings = safeToDouble(response['today_earnings']);
      final weekEarnings = safeToDouble(response['week_earnings']);
      final monthEarnings = safeToDouble(response['month_earnings']);
      final totalEarnings = safeToDouble(response['total_earnings']);
      final completedOrders = response['completed_orders'] ?? 0;

      return DriverEarnings(
        todayEarnings: todayEarnings,
        weekEarnings: weekEarnings,
        monthEarnings: monthEarnings,
        totalEarnings: totalEarnings,
        completedOrders: completedOrders,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      print('获取司机收入统计失败: $e');
      return DriverEarnings.empty();
    }
  }
}
