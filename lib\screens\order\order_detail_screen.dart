//订单详情页面，展示订单的详细信息和状态历史
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/data/models/appuser.dart';
import 'package:hyt/view_models/order_view_model.dart';
import 'package:hyt/widgets/order_card.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/styles/app_theme.dart';
import 'package:hyt/widgets/cancel_order_dialog.dart';
import 'package:provider/provider.dart';
import 'package:hyt/data/models/order.dart';
import 'package:hyt/data/models/order_status_history.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/constants/order_status_constants.dart';
import 'package:intl/intl.dart';
import 'package:hyt/constants/routes.dart';
import 'package:hyt/styles/app_theme.dart';

import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:hyt/widgets/order_action_buttons.dart';
import 'package:hyt/data/models/reviews.dart'; // 导入Review模型
import 'package:hyt/widgets/unified_confirm_dialog.dart';

class OrderDetailScreen extends StatefulWidget {
  final Order order; // 初始订单数据

  const OrderDetailScreen({
    super.key,
    required this.order,
  });

  @override
  State<OrderDetailScreen> createState() => _OrderDetailScreenState();
}

class _OrderDetailScreenState extends State<OrderDetailScreen> {
  bool _isStatusExpanded = false; // 运输进度卡片展开状态
  AppUser? driverUser; // 司机用户信息

  @override
  void initState() {
    super.initState();
    // 初始化时设置当前订单并自动刷新数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshOrderData();
    });
  }

  /// 刷新订单数据（包括订单详情、评价和司机信息）
  Future<void> _refreshOrderData() async {
    final orderViewModel = context.read<OrderViewModel>();

    try {
      // 设置当前订单
      orderViewModel.setCurrentOrder(widget.order);

      // 并行刷新订单相关数据
      await Future.wait([
        orderViewModel.getOrderById(widget.order.orderId), // 获取最新订单数据
        orderViewModel.getReviewsForOrder(widget.order.orderId), // 加载订单评价
      ]);

      // 获取司机详细信息
      await _loadDriverInfo();
    } catch (e) {
      debugPrint('刷新订单数据失败: $e');
    }
  }

  /// 加载司机详细信息（包括评分）
  Future<void> _loadDriverInfo() async {
    final appUserViewModel = context.read<AppUserViewModel>();
    final currentRole = appUserViewModel.currentRole;

    // 如果是货主视角且有司机ID，获取司机详细信息
    if (currentRole == 'shipper' && widget.order.driverId != null) {
      try {
        final driver =
            await appUserViewModel.getUserById(widget.order.driverId!);
        setState(() {
          driverUser = driver;
        });
      } catch (e) {
        debugPrint('获取司机信息失败: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用Consumer监听订单状态变化
    return Consumer2<OrderViewModel, AppUserViewModel>(
      // 修改为Consumer2
      builder: (context, orderViewModel, appUserViewModel, child) {
        // 添加appUserViewModel
        // 使用ViewModel中的当前订单状态，如果没有则使用传入的订单
        final currentOrder = orderViewModel.currentOrder ?? widget.order;
        final statusHistory =
            orderViewModel.getOrderStatusHistory(currentOrder.orderId);
        final role = appUserViewModel.currentRole; // 获取当前用户角色
        final myId = appUserViewModel.currentUser?.id ?? ''; // 获取当前用户ID

        // 确定评价对象ID和角色
        String toUserId = '';
        String toUserRole = '';
        if (role == 'shipper') {
          // 如果当前用户是货主，则评价司机
          toUserId = currentOrder.driverId ?? '';
          toUserRole = 'driver';
        } else if (role == 'driver') {
          // 如果当前用户是司机，则评价货主
          toUserId = currentOrder.customerID;
          toUserRole = 'shipper';
        }

        // 判断当前用户是否已评价过本单的对方
        final hasUserReviewed =
            orderViewModel.hasReviewed(currentOrder.orderId, myId);

        // 验证评价对象是否有效（不能给自己评价）
        final canReview = toUserId.isNotEmpty && toUserId != myId;

        return Scaffold(
          backgroundColor: Theme.of(context).colorScheme.surface,
          appBar: AppBar(
            title: const Text('订单详情'),
            centerTitle: true, // 走 AppBarTheme 但保留居中
            elevation: 0, // 由主题控制
            backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
            foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
          ),
          body: RefreshIndicator(
            // 下拉刷新时调用统一的数据刷新方法
            onRefresh: _refreshOrderData,
            child: Stack(
              children: [
                SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 订单状态卡片
                      _buildOrderStatusCard(currentOrder),

                      // 地图区域 (仅在特定状态下显示)
                      if (currentOrder.statusCode ==
                              OrderStatusConstants.STATUS_ACCEPTED ||
                          currentOrder.statusCode ==
                              OrderStatusConstants
                                  .STATUS_TRANSPORTING) // 在这些状态下显示地图
                        _buildMapSection(),

                      // 订单状态跟踪
                      _buildStatusHistoryCard(statusHistory),

                      // 角色判断：司机看到货主信息，货主看到司机信息
                      Builder(builder: (context) {
                        if (role == 'driver' &&
                            currentOrder.customerID.toString().isNotEmpty) {
                          return _buildShipperInfoCard(currentOrder);
                        } else if (role == 'shipper' &&
                            currentOrder.driverId != null &&
                            currentOrder.driverId.toString().isNotEmpty) {
                          return _buildDriverInfoCard(currentOrder);
                        } else {
                          return const SizedBox.shrink();
                        }
                      }),
                      SizedBox(height: AppInsets.gap4()),
                      // 底部操作按钮
                      _buildBottomActions(currentOrder, hasUserReviewed),

                      // 评价输入卡片 (已完成订单且未评价时显示)
                      if (currentOrder.statusCode ==
                              OrderStatusConstants.STATUS_COMPLETED &&
                          canReview &&
                          !hasUserReviewed) // 只有订单完成，可以评价且未评价时显示
                        _buildReviewInputCard(
                            currentOrder, myId, toUserId, toUserRole),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 订单状态卡片
  Widget _buildOrderStatusCard(Order order) {
    // 复用公共的 OrderCard，但外部统一使用页面左右边距与卡片间距
    return Padding(
      padding: AppInsets.pageGutter(),
      child: OrderCard(
        order: order,
        role: 'driver', // 这里可根据实际角色动态传递
        onViewDetail: null, // 详情页无需点击事件
        compact: false, // 非紧凑模式
      ),
    );
  }

  // 地图区域
  Widget _buildMapSection() {
    return AppCard(
      margin:
          AppInsets.pageGutter().add(EdgeInsets.only(bottom: AppInsets.gap6())),
      padding: EdgeInsets.zero,
      borderRadius: 12,
      elevation: 0,
      outlineAlpha: 0.6,
      child: SizedBox(
        height: 200.h,
        // 使用卡片容器承载地图占位，背景与圆角保持一致
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Stack(
            children: [
              // 地图占位图
              Center(
                child: Icon(
                  Icons.map,
                  size: 48.sp,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              // 实时跟踪按钮
              Positioned(
                bottom: 8.h,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(20.r),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context)
                              .colorScheme
                              .shadow
                              .withAlpha(13),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.directions_car,
                            color: Theme.of(context).colorScheme.primary,
                            size: 16.sp),
                        SizedBox(width: AppInsets.gapW4()),
                        Text(
                          '实时跟踪',
                          style: AppTextStyles.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 订单状态跟踪卡片
  Widget _buildStatusHistoryCard(List<OrderStatusHistory> statusHistory) {
    if (statusHistory.isEmpty) {
      return const SizedBox.shrink();
    }
    final sortedHistory = List<OrderStatusHistory>.from(statusHistory)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
    final displayHistory =
        _isStatusExpanded ? sortedHistory : [sortedHistory.first];
    return AppCard(
      margin:
          AppInsets.pageGutter().add(EdgeInsets.only(bottom: AppInsets.gap6())),
      padding: AppInsets.h16().add(AppInsets.v12()),
      borderRadius: 12,
      elevation: 0,
      outlineAlpha: 0.6,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '运输进度',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  setState(() {
                    _isStatusExpanded = !_isStatusExpanded;
                  });
                },
                child: Icon(
                  _isStatusExpanded ? Icons.expand_less : Icons.expand_more,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 20.sp,
                ),
              ),
            ],
          ),
          SizedBox(height: AppInsets.gap8()),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: displayHistory.length,
            itemBuilder: (context, index) {
              final status = displayHistory[index];
              final isLast = index == displayHistory.length - 1;
              final isCurrent = index == 0;
              return Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Column(
                    children: [
                      Container(
                        width: 12.w,
                        height: 12.w,
                        decoration: BoxDecoration(
                          color: isCurrent
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.outlineVariant,
                          shape: BoxShape.circle,
                          border: Border.all(
                              color: Theme.of(context).colorScheme.surface,
                              width: 2),
                        ),
                      ),
                      if (!isLast)
                        Container(
                          width: 2.w,
                          height: 8.h,
                          color: Theme.of(context).colorScheme.outlineVariant,
                        ),
                    ],
                  ),
                  SizedBox(width: AppInsets.gapW8()),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              OrderStatusConstants.getStatusNameLocalized(
                                  status.statusCode, context),
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: isCurrent
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(context)
                                        .colorScheme
                                        .onSurfaceVariant,
                                fontWeight: isCurrent
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                            SizedBox(width: AppInsets.gapW8()),
                            Text(
                              DateFormat('yyyy-MM-dd HH:mm')
                                  .format(status.timestamp),
                              style: AppTextStyles.bodySmall.copyWith(
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                        if (status.remark != null &&
                            status.remark!.isNotEmpty) ...[
                          SizedBox(height: AppInsets.gap2()),
                          Text(
                            status.remark!,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                          ),
                        ],
                        if (!isLast) SizedBox(height: AppInsets.gap8()),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  // 司机信息卡片
  Widget _buildDriverInfoCard(Order order) {
    return AppCard(
      margin:
          AppInsets.pageGutter().add(EdgeInsets.only(bottom: AppInsets.gap6())),
      padding: AppInsets.h16().add(AppInsets.v12()),
      borderRadius: 12,
      elevation: 0,
      outlineAlpha: 0.6,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '司机信息',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: AppInsets.gap12()),
          Row(
            children: [
              CircleAvatar(
                radius: 22.r,
                backgroundColor:
                    Theme.of(context).colorScheme.surfaceContainerHighest,
                child: Icon(Icons.person,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 24.sp),
              ),
              SizedBox(width: AppInsets.gapW12()),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        Text(
                          order.driverName ?? '未知司机',
                          style: AppTextStyles.bodyMedium
                              .copyWith(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(width: AppInsets.gapW8()),
                        Row(
                          children: [
                            Icon(Icons.star, color: Colors.amber, size: 16.sp),
                            SizedBox(width: 2.w),
                            Text(
                              driverUser?.rating?.toStringAsFixed(1) ?? '暂无评分',
                              style: AppTextStyles.bodySmall
                                  .copyWith(color: Colors.amber), // 保留星星金色
                            ),
                          ],
                        ),
                        SizedBox(width: AppInsets.gapW8()),
                        Expanded(
                          child: Text(
                            '${order.vehicleType} · ${order.vehiclePlate}',
                            style: AppTextStyles.bodySmall.copyWith(
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurfaceVariant),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      order.driverPhone ?? '',
                      style: AppTextStyles.bodySmall.copyWith(
                          color:
                              Theme.of(context).colorScheme.onSurfaceVariant),
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(Icons.phone,
                    color: Theme.of(context).colorScheme.primary, size: 22.sp),
                onPressed: () {
                  // TODO: 实现拨打电话功能
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 货主信息卡片
  Widget _buildShipperInfoCard(Order order) {
    return AppCard(
      margin:
          AppInsets.pageGutter().add(EdgeInsets.only(bottom: AppInsets.gap6())),
      padding: AppInsets.h16().add(AppInsets.v12()),
      borderRadius: 12,
      elevation: 0,
      outlineAlpha: 0.6,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '货主信息',
            style:
                AppTextStyles.bodyMedium.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              CircleAvatar(
                radius: 22.r,
                backgroundColor:
                    Theme.of(context).colorScheme.surfaceContainerHighest,
                child: Icon(Icons.person,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 24.sp),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        Text(
                          order.customerName.isNotEmpty == true
                              ? order.customerName
                              : '货主',
                          style: AppTextStyles.bodyMedium
                              .copyWith(fontWeight: FontWeight.bold),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      order.customerPhone,
                      style: AppTextStyles.bodySmall
                          .copyWith(color: Colors.grey.shade600),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(Icons.phone,
                    color: Theme.of(context).colorScheme.primary, size: 22.sp),
                onPressed: () {
                  // TODO: 实现拨打电话功能
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 底部操作按钮
  Widget _buildBottomActions(Order order, bool hasUserReviewed) {
    return Consumer2<OrderViewModel, AppUserViewModel>(
      builder: (context, orderViewModel, appUserViewModel, _) {
        final String role = appUserViewModel.currentRole;
        // 按钮加载状态只应该在按钮操作时显示，不应该在页面刷新时显示
        final bool isButtonLoading = orderViewModel.isButtonLoading;

        // 判断当前订单和角色下是否有按钮可显示
        final List<bool> btnVisible = [
          // 司机按钮
          if (role == 'driver') ...[
            order.statusCode == OrderStatusConstants.STATUS_WAITING, // 立即抢单
            order.statusCode ==
                OrderStatusConstants.STATUS_ACCEPTED, // 已装货 + 取消订单
            order.statusCode == OrderStatusConstants.STATUS_TRANSPORTING, // 已送达
            order.statusCode == OrderStatusConstants.STATUS_COMPLETED &&
                hasUserReviewed, // 已完成且已评价时显示删除订单
            order.statusCode == OrderStatusConstants.STATUS_CANCELED, // 删除订单
          ],
          // 货主按钮
          if (role == 'shipper') ...[
            order.statusCode == OrderStatusConstants.STATUS_WAITING, // 取消订单
            order.statusCode == OrderStatusConstants.STATUS_ACCEPTED, // 取消订单
            order.statusCode == OrderStatusConstants.STATUS_DELIVERED, // 立即支付
            order.statusCode == OrderStatusConstants.STATUS_COMPLETED &&
                hasUserReviewed, // 已完成且已评价时显示删除订单
            order.statusCode == OrderStatusConstants.STATUS_CANCELED, // 删除订单
          ],
        ];
        final hasAction = btnVisible.any((v) => v);
        if (!hasAction) return const SizedBox.shrink();

        return SafeArea(
          top: false,
          child: Container(
            padding: AppInsets.h16().add(AppInsets.v12()),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.shadow.withAlpha(13),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: OrderActionButtons(
              order: order,
              role: role,
              scene: 'detail',
              isLoading: isButtonLoading,
              onAcceptOrder: () async {
                final appUserViewModel = context.read<AppUserViewModel>();
                final confirmed = await _showConfirmDialog('确定要接单吗？');
                if (confirmed) {
                  final result = await orderViewModel.acceptOrder(
                      order.orderId, appUserViewModel);
                  if (!mounted) return;
                  ScaffoldMessenger.of(this.context).showSnackBar(
                    SnackBar(
                      content: Text(
                          orderViewModel.message ?? (result ? '操作成功' : '操作失败')),
                      backgroundColor: result
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.error,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }
              },
              onPay: () async {
                final confirmed = await _showConfirmDialog('确定要支付该订单吗？');
                if (confirmed) {
                  final result =
                      await orderViewModel.processPayment(order.orderId);
                  if (!mounted) return;
                  ScaffoldMessenger.of(this.context).showSnackBar(
                    SnackBar(
                      content: Text(
                          orderViewModel.message ?? (result ? '操作成功' : '操作失败')),
                      backgroundColor: result
                          ? Theme.of(this.context).colorScheme.primary
                          : Theme.of(this.context).colorScheme.error,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }
              },
              onContact: () {
                Navigator.pushNamed(
                  context,
                  Routes.message,
                  arguments: {'orderId': order.orderId},
                );
              },
              onDelete: () async {
                final confirmed =
                    await _showConfirmDialog('确定要删除该订单吗？删除后无法恢复。');
                if (confirmed) {
                  final userId = appUserViewModel.currentUser?.id ?? '';
                  final success = await orderViewModel.deleteOrder(
                      order.orderId, userId, role);
                  if (!mounted) return;
                  ScaffoldMessenger.of(this.context).showSnackBar(
                    SnackBar(
                      content: Text(orderViewModel.message ??
                          (success ? '操作成功' : '操作失败')),
                      backgroundColor: success
                          ? Theme.of(this.context).colorScheme.primary
                          : Theme.of(this.context).colorScheme.error,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                  if (success) {
                    Navigator.of(this.context).pop();
                  }
                }
              },
              onPickup: () async {
                final confirmed = await _showConfirmDialog('确认已装货？');
                if (confirmed) {
                  final result =
                      await orderViewModel.pickupOrder(order.orderId);
                  if (!mounted) return;
                  ScaffoldMessenger.of(this.context).showSnackBar(
                    SnackBar(
                      content: Text(
                          orderViewModel.message ?? (result ? '操作成功' : '操作失败')),
                      backgroundColor: result
                          ? Theme.of(this.context).colorScheme.primary
                          : Theme.of(this.context).colorScheme.error,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }
              },
              onDeliver: () async {
                final appUserViewModel = context.read<AppUserViewModel>();
                final confirmed = await _showConfirmDialog('确认货物已送达？');
                if (confirmed) {
                  final result = await orderViewModel.deliverOrder(
                      appUserViewModel, order.orderId);
                  if (!mounted) return;
                  ScaffoldMessenger.of(this.context).showSnackBar(
                    SnackBar(
                      content: Text(
                          orderViewModel.message ?? (result ? '操作成功' : '操作失败')),
                      backgroundColor: result
                          ? Theme.of(this.context).colorScheme.primary
                          : Theme.of(this.context).colorScheme.error,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }
              },
              onCancel: () async {
                // 调用新的取消订单对话框
                final result = await showDialog<bool>(
                  context: context,
                  builder: (context) => CancelOrderDialog(order: order),
                );

                // 如果取消成功，刷新页面数据
                if (result == true) {
                  await _refreshOrderData();
                }
              },
            ),
          ),
        );
      },
    );
  }

  // 二次确认弹窗
  Future<bool> _showConfirmDialog(String content) async {
    return await showConfirmDialog(
      context,
      title: '请确认',
      content: content,
    );
  }

  // 评价输入卡片
  Widget _buildReviewInputCard(
      Order order, String fromUserId, String toUserId, String toUserRole) {
    final orderViewModel = context.read<OrderViewModel>();
    int rating = 5; // 默认5星
    String comment = '';
    bool submitting = false;

    return StatefulBuilder(
      builder: (stfContext, setState) {
        return AppCard(
          margin: EdgeInsets.only(bottom: AppInsets.gap6()),
          padding: AppInsets.h16().add(AppInsets.v12()),
          borderRadius: 12,
          elevation: 0,
          outlineAlpha: 0.6,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '评价${toUserRole == 'driver' ? '司机' : '货主'}',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 2.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                    5,
                    (index) => IconButton(
                          icon: Icon(
                            index < rating ? Icons.star : Icons.star_border,
                            color: Colors.amber,
                            size: 24.sp,
                          ),
                          onPressed: () {
                            setState(() => rating = index + 1);
                          },
                        )),
              ),
              SizedBox(height: 2.h),
              TextField(
                decoration: const InputDecoration(
                  hintText: '写下你的评价...',
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                ),
                maxLines: 3,
                onChanged: (v) => comment = v,
                style: AppTextStyles.bodyMedium,
              ),
              SizedBox(height: 12.h),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: submitting
                      ? null
                      : () async {
                          if (comment.trim().isEmpty) {
                            ScaffoldMessenger.of(stfContext).showSnackBar(
                              const SnackBar(content: Text('请输入评价内容')),
                            );
                            return;
                          }

                          if (rating == 0) {
                            ScaffoldMessenger.of(stfContext).showSnackBar(
                              const SnackBar(content: Text('请选择评分')),
                            );
                            return;
                          }

                          // 验证评价对象
                          if (toUserId.isEmpty || toUserId == fromUserId) {
                            ScaffoldMessenger.of(stfContext).showSnackBar(
                              const SnackBar(content: Text('评价对象无效，无法提交评价')),
                            );
                            return;
                          }

                          setState(() => submitting = true);
                          final review = Review(
                            fromUserId: fromUserId,
                            toUserId: toUserId,
                            toUserRole: toUserRole,
                            rating: rating,
                            comment: comment,
                            orderId: order.orderId, // 关联订单ID
                            createdAt: DateTime.now(),
                          );
                          final result =
                              await orderViewModel.submitReview(review);
                          setState(() => submitting = false);
                          if (result) {
                            // 提交成功后立即标记为已评价，避免UI闪烁
                            orderViewModel.setHasReviewed(
                                order.orderId, fromUserId, true);
                            if (!stfContext.mounted) return;
                            ScaffoldMessenger.of(stfContext)
                                .showSnackBar(SnackBar(
                              content: const Text('评价提交成功'),
                              backgroundColor:
                                  Theme.of(stfContext).colorScheme.primary,
                            ));
                          } else {
                            if (!stfContext.mounted) return;
                            ScaffoldMessenger.of(stfContext).showSnackBar(
                                SnackBar(
                                    content:
                                        Text(orderViewModel.message ?? '提交失败'),
                                    backgroundColor: Theme.of(stfContext)
                                        .colorScheme
                                        .error));
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.amber,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r)),
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                  ),
                  child: submitting
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text('提交评价'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
