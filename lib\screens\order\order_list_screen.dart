//货主角色的订单列表页面，可以查看不同类型的订单
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/constants/order_status_constants.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:hyt/view_models/order_view_model.dart';
import 'package:provider/provider.dart';
import 'package:hyt/data/models/order.dart'; // 导入 Order 模型
import 'package:hyt/widgets/search_app_bar.dart'; // 导入自定义搜索AppBar组件
import 'package:hyt/widgets/order_card.dart'; // 导入订单卡片组件
import 'package:hyt/screens/order/order_detail_screen.dart'; // 导入订单详情页面
import 'package:hyt/widgets/order_action_buttons.dart';
import 'package:hyt/l10n/app_localizations.dart';
import 'package:hyt/widgets/unified_confirm_dialog.dart';
import 'package:hyt/widgets/empty_state.dart';
import 'package:hyt/styles/app_theme.dart';
import 'package:hyt/widgets/error_state.dart';
import 'package:hyt/styles/text_styles.dart';

class OrderListScreen extends StatefulWidget {
  const OrderListScreen({super.key});

  @override
  State<OrderListScreen> createState() => _OrderListScreenState();
}

class _OrderListScreenState extends State<OrderListScreen>
    with SingleTickerProviderStateMixin {
  late OrderViewModel _orderViewModel;
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // 保存ViewModel引用
    _orderViewModel = context.read<OrderViewModel>();
    // 在UI层初始化TabController
    _tabController =
        TabController(length: _orderViewModel.tabTitles.length, vsync: this);
    // 添加搜索监听器，当搜索内容变化时刷新UI
    _searchController.addListener(() {
      setState(() {});
    });

    // 初始化时延迟加载订单数据，强制刷新确保数据最新
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadOrders(forceRefresh: true);
    });
  }

  // 加载订单数据的方法
  // [forceRefresh] 是否强制刷新数据，默认为false
  Future<void> _loadOrders({bool forceRefresh = false}) async {
    final appUserViewModel = context.read<AppUserViewModel>();
    if (appUserViewModel.currentUser != null) {
      await _orderViewModel.getShipperOrders(
        appUserViewModel.currentUser!.id!,
        forceRefresh: forceRefresh,
      );
    }
  }

  @override
  void dispose() {
    // 释放资源
    _searchController.removeListener(() {
      setState(() {});
    });
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 使用read获取ViewModel实例，避免整个build方法重建
    return Consumer<OrderViewModel>(
      builder: (context, orderViewModel, child) {
        // 获取 Order 类型的列表
        final List<Order> myOrders = orderViewModel.myOrders;

        return Scaffold(
          backgroundColor: Theme.of(context).colorScheme.surface,
          appBar: SearchAppBar(
            searchController: _searchController,
            tabController: _tabController,
            tabTitles: [
              AppLocalizations.of(context)!.all,
              AppLocalizations.of(context)!.statusWaiting,
              AppLocalizations.of(context)!.statusTransporting,
              AppLocalizations.of(context)!.statusDelivered,
              AppLocalizations.of(context)!.statusCompleted,
              AppLocalizations.of(context)!.statusCanceled,
            ],
            hintText:
                AppLocalizations.of(context)?.searchOrderHint ?? '搜索订单号/地址/客户',
            centerTitle: false,
            elevation: 0.5,
            backgroundColor: Theme.of(context).colorScheme.surface,
            // 统一 Tab 样式，参照消息中心
            tabLabelStyle: AppTextStyles.withWeight(
                AppTextStyles.bodyMedium, FontWeight.bold),
            tabUnselectedLabelStyle: AppTextStyles.bodyMedium,
            tabIndicatorWeight: 3,
            tabIsScrollable: true,
            tabAlignment: TabAlignment.center,
            actions: [
              IconButton(
                icon: const Icon(Icons.refresh),
                tooltip:
                    AppLocalizations.of(context)?.refreshOrderData ?? '刷新订单数据',
                onPressed: () => _loadOrders(forceRefresh: true),
              ),
            ],
          ),
          body: RefreshIndicator(
            onRefresh: () => _loadOrders(forceRefresh: true),
            color: Theme.of(context).colorScheme.primary,
            child: TabBarView(
              controller: _tabController,
              children: [
                {'code': null},
                {'code': OrderStatusConstants.STATUS_WAITING},
                {'code': OrderStatusConstants.STATUS_TRANSPORTING},
                {'code': OrderStatusConstants.STATUS_DELIVERED},
                {'code': OrderStatusConstants.STATUS_COMPLETED},
                {'code': OrderStatusConstants.STATUS_CANCELED},
              ].map((cfg) {
                // 根据标签和搜索关键字筛选订单
                List<Order> filteredOrders = myOrders;
                final int? code = cfg['code'];
                // 1. 按 Tab 筛选（根据状态码而非文字）
                if (code != null) {
                  filteredOrders = myOrders
                      .where((order) => order.statusCode == code)
                      .toList();
                }
                // 2. 按搜索关键字筛选 (在 Tab 筛选结果的基础上进行)
                String searchTerm = _searchController.text.trim().toLowerCase();
                if (searchTerm.isNotEmpty) {
                  filteredOrders = filteredOrders.where((order) {
                    final id = order.orderId.toLowerCase();
                    final pickup = order.pickupAddress.toLowerCase();
                    final delivery = order.deliveryAddress.toLowerCase();
                    final customer = order.customerName.toLowerCase();
                    return id.contains(searchTerm) ||
                        pickup.contains(searchTerm) ||
                        delivery.contains(searchTerm) ||
                        customer.contains(searchTerm);
                  }).toList();
                }
                return _buildOrderList(filteredOrders);
              }).toList(),
            ),
          ),
        );
      },
    );
  }

  // 构建订单列表，接收 List<Order>
  Widget _buildOrderList(List<Order> orders) {
    // 显示加载指示器
    if (_orderViewModel.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_orderViewModel.error != null) {
      return ErrorState(
        message: _orderViewModel.error!,
        onRetry: () => _loadOrders(forceRefresh: true),
      );
    }

    if (orders.isEmpty) {
      return ListView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding:
            AppInsets.pageGutter().add(EdgeInsets.only(top: AppInsets.gap2())),
        children: [
          SizedBox(height: AppInsets.gap4()),
          EmptyState(
            useCard: true,
            cardMargin: EdgeInsets.zero,
            title: AppLocalizations.of(context)?.noOrders ?? '暂无订单',
            description:
                AppLocalizations.of(context)?.orderListDesc ?? '这里会展示您的订单列表',
          ),
        ],
      );
    }

    return ListView.builder(
      padding:
          AppInsets.pageGutter().add(EdgeInsets.only(top: AppInsets.gap2())),
      itemCount: orders.length,
      // 增加预加载区域以提升滚动性能
      cacheExtent: 120.h,
      itemBuilder: (context, index) {
        // 修复: 传递 Order 对象
        final order = orders[index];
        return _buildOrderCard(context, order);
      },
    );
  }

  // 构建订单卡片，接收 Order 对象
  Widget _buildOrderCard(BuildContext context, Order order) {
    final appUserViewModel = context.watch<AppUserViewModel>();
    final currentRole = appUserViewModel.currentRole;

    return OrderCard(
      order: order,
      role: currentRole,
      onViewDetail: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => OrderDetailScreen(order: order),
          ),
        );
      },
      actionButtons: [
        OrderActionButtons(
          order: order,
          role: currentRole,
          scene: 'list',
          onDelete: () async {
            final confirmed =
                await _showConfirmDialog(context, '确定要删除该订单吗？删除后无法恢复。');
            if (confirmed) {
              final userId = appUserViewModel.currentUser?.id ?? '';
              final success = await _orderViewModel.deleteOrder(
                  order.orderId, userId, currentRole);
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        _orderViewModel.message ?? (success ? '操作成功' : '操作失败')),
                    backgroundColor: success ? Colors.green : Colors.red,
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            }
          },
        ),
      ],
    );
  }

  // 二次确认弹窗
  Future<bool> _showConfirmDialog(BuildContext context, String content) async {
    return await showConfirmDialog(
      context,
      title: '请确认',
      content: content,
    );
  }
}
