# 快运通应用 分层架构

## 架构概述

基于 Provider 的分层架构模式。这种架构模式有以下优势：

1.清晰的职责分离：各层仅关注自身逻辑
2.高效的状态管理：Provider 自动处理依赖和重建
3.强健的错误处理：统一捕获并显示异常
4.可扩展的架构：轻松添加新功能（如第三方登录）

## 目录结构

lib/
├──constants/              # 常量（路由名、正则表达式等）
│  utils/                  # 工具类（验证、导航等）
├──data/
│   ├── models/             # 数据模型（Model）
│   └── repositories/       # repository仓库负责和数据库交互
├──view_models/             # ViewModel处理数据交互和业务逻辑
└──screens/                 # UI页面
    ├── widgets/            # 公共等组件


## MVVC 架构各层职责

### Model 层
- 定义数据结构和基本操作
- 不包含业务逻辑和UI相关代码
- 位于 `lib/data/models/` 目录

### View 层
- 专注于UI展示
- 通过Provider监听ViewModel的变化
- 不直接操作Model或Service
- 位于 `lib/screens/` 目录

### ViewModel 层
- 连接Model和View
- 处理数据转换和格式化
- 提供View所需的数据和方法
- 使用Provider实现状态管理
- 位于 `lib/view_models/` 目录

### Repository 层
- 抽象数据源操作
- 提供统一的数据访问接口
- 处理本地缓存和远程数据同步
- 位于 `lib/data/repositories/` 目录



## 示例代码
以下示例代码是一个典型的用户注册流程的示例：
1. 数据模型 (Model)
定义用户注册所需的数据结构：
// data/models/user_model.dart
@JsonSerializable()
class UserModel {
  final String email;
  final String password;
  final String? username;

  UserModel({
    required this.email,
    required this.password,
    this.username,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}

2. Repository 层
处理用户注册逻辑，包含 Supabase 认证和数据存储：
// data/repositories/auth_repository.dart
abstract class AuthRepository {
  Future<void> register(UserModel user);
  Future<void> saveUserProfile(UserModel user);
}

// data/repositories/auth_repository_impl.dart
class AuthRepositoryImpl implements AuthRepository {
  final SupabaseClient _supabase;

  AuthRepositoryImpl(this._supabase);

  @override
  Future<void> register(UserModel user) async {
    final response = await _supabase.auth.signUp(
      email: user.email,
      password: user.password,
      data: {'username': user.username},
    );
    if (response.user == null) throw Exception('注册失败');
  }

  @override
  Future<void> saveUserProfile(UserModel user) async {
    await _supabase.from('profiles').upsert({
      'user_id': _supabase.auth.currentUser!.id,
      'email': user.email,
      'username': user.username,
    });
  }
}

3. ViewModel 层
处理表单验证、状态管理及业务逻辑：
// domain/view_models/register_view_model.dart
import 'package:flutter/foundation.dart';

class RegisterViewModel extends ChangeNotifier {
  final AuthRepository _authRepo;
  
  RegisterViewModel(this._authRepo);

  // 注册状态：idle | loading | success | error
  String _status = 'idle';
  String get status => _status;
  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  Future<void> register(UserModel user) async {
    try {
      _status = 'loading';
      notifyListeners();

      // 表单验证
      if (!_validateEmail(user.email)) throw '邮箱格式错误';
      if (!_validatePassword(user.password)) throw '密码至少6位';

      // 执行注册
      await _authRepo.register(user);
      await _authRepo.saveUserProfile(user);

      _status = 'success';
    } catch (e) {
      _status = 'error';
      _errorMessage = e.toString();
    } finally {
      notifyListeners();
    }
  }

  bool _validateEmail(String email) => RegExp(r'^.+@[a-zA-Z]+\.{1}[a-zA-Z]+(\.{0,1}[a-zA-Z]+)$').hasMatch(email);
  bool _validatePassword(String password) => password.length >= 6;
}
4. Provider 配置
注册全局依赖：
// presentation/providers/app_providers.dart
final supabaseProvider = Provider<SupabaseClient>((_) => Supabase.instance.client);

final authRepositoryProvider = Provider<AuthRepository>(
  (ref) => AuthRepositoryImpl(ref.watch(supabaseProvider)),
);

final registerViewModelProvider = ChangeNotifierProvider(
  (ref) => RegisterViewModel(ref.watch(authRepositoryProvider)),
);

5. UI 页面
使用 Provider 获取 ViewModel，并在 UI 中监听状态变化：
// presentation/pages/register_page.dart
class RegisterPage extends StatelessWidget {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _usernameController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final viewModel = context.watch<RegisterViewModel>();

    return Scaffold(
      appBar: AppBar(title: Text('用户注册')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _emailController,
                decoration: InputDecoration(labelText: '邮箱'),
                validator: (v) => v!.isEmpty ? '必填字段' : null,
              ),
              TextFormField(
                controller: _passwordController,
                obscureText: true,
                decoration: InputDecoration(labelText: '密码'),
                validator: (v) => v!.isEmpty ? '必填字段' : null,
              ),
              TextFormField(
                controller: _usernameController,
                decoration: InputDecoration(labelText: '用户名（可选）'),
              ),
              SizedBox(height: 20),
              _buildStatusWidget(viewModel),
              ElevatedButton(
                onPressed: viewModel.status == 'loading' ? null : _submitForm,
                child: Text('立即注册'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusWidget(RegisterViewModel vm) {
    if (vm.status == 'loading') return CircularProgressIndicator();
    if (vm.status == 'error') return Text(vm.errorMessage!, style: TextStyle(color: Colors.red));
    if (vm.status == 'success') return Text('注册成功！', style: TextStyle(color: Colors.green));
    return SizedBox.shrink();
  }

  void _submitForm() {
    if (!_formKey.currentState!.validate()) return;
    
    final user = UserModel(
      email: _emailController.text,
      password: _passwordController.text,
      username: _usernameController.text,
    );
    
    context.read<RegisterViewModel>().register(user);
  }
}