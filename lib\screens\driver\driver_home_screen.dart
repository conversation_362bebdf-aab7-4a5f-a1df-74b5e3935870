//司机首页
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/constants/routes.dart';
import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/view_models/order_view_model.dart';
import 'package:provider/provider.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:hyt/view_models/driver_earnings_view_model.dart';
import 'package:hyt/widgets/order_card.dart';
import 'package:hyt/data/models/order.dart';
import 'package:intl/intl.dart';
import 'package:hyt/widgets/app_card.dart';
import 'package:hyt/styles/app_theme.dart';
import 'package:hyt/widgets/unified_button.dart';

class DriverHomeScreen extends StatefulWidget {
  const DriverHomeScreen({super.key});

  @override
  State<DriverHomeScreen> createState() => _DriverHomeScreenState();
}

class _DriverHomeScreenState extends State<DriverHomeScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    // 页面初始化时自动刷新数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshData();
    });
  }

  /// 刷新司机数据（订单和收入统计）
  Future<void> _refreshData() async {
    final appUserViewModel = context.read<AppUserViewModel>();
    final orderViewModel = context.read<OrderViewModel>();
    final earningsViewModel = context.read<DriverEarningsViewModel>();
    final userId = appUserViewModel.currentUser?.id;

    if (userId != null) {
      try {
        await Future.wait([
          orderViewModel.loadMyOrders(userId, isDriver: true), // 加载司机订单
          orderViewModel.loadPendingOrders(), // 加载待接单订单（附近货源）
          earningsViewModel.loadDriverEarnings(userId), // 加载收入统计
        ]);
      } catch (e) {
        debugPrint('刷新司机数据失败: $e');
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: RefreshIndicator(
          // 下拉刷新时调用统一的数据刷新方法
          onRefresh: _refreshData,
          child: ListView(
            physics: const AlwaysScrollableScrollPhysics(),
            children: [
              _buildHeaderSection(),
              SizedBox(height: AppInsets.gap4()),
              // Tab + 内容卡片（合并为一个整体）
              AppCard(
                margin: AppInsets.pageGutter(),
                padding: EdgeInsets.zero,
                borderRadius: 12,
                elevation: 0,
                outlineAlpha: 0.6,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Tab栏
                    Padding(
                      padding: AppInsets.h16().add(AppInsets.v8()),
                      child: TabBar(
                        controller: _tabController,
                        onTap: (index) {
                          setState(() {
                            _currentTabIndex = index;
                          });
                        },
                        tabs: const [
                          Tab(text: '进行中订单'),
                          Tab(text: '附近货源'),
                        ],
                      ),
                    ),
                    // 内容
                    Padding(
                      padding: AppInsets.h12().add(AppInsets.v8()),
                      child: _currentTabIndex == 0
                          ? _buildMyOrdersContent(context)
                          : _buildNearbyOrdersContent(context),
                    ),
                    // Tab卡片下方居中操作按钮
                    Padding(
                      padding: AppInsets.h16().add(AppInsets.v8()),
                      child: Center(
                        child: FilledButton.tonal(
                          onPressed: () => _currentTabIndex == 0
                              ? Navigator.pushNamed(
                                  context, Routes.orderHistory)
                              : Navigator.pushNamed(context, Routes.orderPool),
                          child: Text(
                            '查看全部',
                            style: AppTextStyles.bodyMedium.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: AppInsets.gap4()),
              _buildQuickActionsCard(),
              SizedBox(height: AppInsets.gap4()),
              _buildAnnouncementCard(),
              SizedBox(height: 12.h),
            ],
          ),
        ),
      ),
    );
  }

  // 顶部工作状态和收入概览
  Widget _buildHeaderSection() {
    return Consumer<DriverEarningsViewModel?>(
      builder: (context, earningsViewModel, child) {
        if (earningsViewModel == null) {
          return const Center(
            child: Text('收入统计服务未初始化'),
          );
        }

        final earnings = earningsViewModel.earnings;
        final isLoading = earningsViewModel.isLoading;
        final error = earningsViewModel.error;

        // 格式化金额
        final formatter = NumberFormat.currency(
          symbol: '¥',
          decimalDigits: 2,
        );

        return Column(
          children: [
            // 收入概览卡片
            AppCard(
              margin: EdgeInsets.symmetric(horizontal: 12.w),
              padding: EdgeInsets.all(16.w),
              borderRadius: 12,
              elevation: 0,
              outlineAlpha: 0.6,
              child: Column(
                children: [
                  if (isLoading)
                    Center(
                      child: Text(
                        '加载中...',
                        style: AppTextStyles.bodySmall.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant),
                      ),
                    )
                  else if (error != null)
                    Center(
                      child: Text(
                        error,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                    )
                  else if (earnings != null)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '今日收入',
                          style: AppTextStyles.bodyMedium,
                        ),
                        Text(
                          formatter.format(earnings.todayEarnings),
                          style: AppTextStyles.headline2.copyWith(
                            color: Theme.of(context).colorScheme.error,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  if (!isLoading && error == null && earnings != null) ...[
                    SizedBox(height: 16.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildIncomeItem(
                          '本月收入',
                          formatter.format(earnings.monthEarnings),
                        ),
                        _buildIncomeItem(
                          '累计收入',
                          formatter.format(earnings.totalEarnings),
                        ),
                        _buildIncomeItem(
                          '总单数',
                          '${earnings.completedOrders}单',
                        ),
                      ],
                    ),
                  ],
                  if (!isLoading && error == null && earnings == null)
                    const Center(
                      child: Text('暂无收入数据，请下拉刷新获取最新收入'),
                    ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  // 收入项
  Widget _buildIncomeItem(String title, String value) {
    return Column(
      children: [
        Text(
          value,
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 4.h),
        Text(
          title,
          style: AppTextStyles.bodySmall.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  // 快捷功能卡片
  Widget _buildQuickActionsCard() {
    return Container(
      margin: AppInsets.pageGutter(),
      padding: AppInsets.all16(),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withAlpha(13),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快捷功能',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAction(
                Icons.history,
                '历史订单',
                () => Navigator.pushNamed(context, Routes.orderHistory),
              ),
              _buildQuickAction(
                Icons.directions_car,
                '车辆信息',
                () => Navigator.pushNamed(context, '/vehicle_info'),
              ),
              _buildQuickAction(
                Icons.account_balance_wallet,
                '我的钱包',
                () => Navigator.pushNamed(context, '/wallet'),
              ),
              _buildQuickAction(
                Icons.person,
                '个人中心',
                () => Navigator.pushNamed(context, Routes.profile),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 快捷功能项
  Widget _buildQuickAction(IconData icon, String title, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 50.w,
            height: 50.w,
            decoration: BoxDecoration(
              color:
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.08),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 24.sp,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            title,
            style: AppTextStyles.bodySmall,
          ),
        ],
      ),
    );
  }

  // 我的订单内容（用于合并卡片内）
  Widget _buildMyOrdersContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Consumer<OrderViewModel>(
          builder: (context, orderViewModel, child) {
            final currentOrders = orderViewModel.getCurrentOrders();
            if (currentOrders.isEmpty) {
              return Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 20.h),
                  child: Text(
                    '暂无进行中的订单',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              );
            }
            return Column(
              children: List.generate(currentOrders.length, (index) {
                final order = currentOrders[index];
                return Padding(
                  padding: EdgeInsets.only(bottom: 2.h),
                  child: _buildOrderCard(context, order),
                );
              }),
            );
          },
        ),
      ],
    );
    // 下方按钮放在卡片内容区域之后，由父级添加
  }

  // 附近货源内容（用于合并卡片内）
  Widget _buildNearbyOrdersContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Consumer<OrderViewModel>(
          builder: (context, orderViewModel, child) {
            final pendingOrders = orderViewModel.pendingOrders;
            if (pendingOrders.isEmpty) {
              return Center(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 20.h),
                  child: Text(
                    '暂无可接单的货源',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              );
            }
            final displayOrders = pendingOrders.take(3).toList();
            return Column(
              children: List.generate(displayOrders.length, (index) {
                final order = displayOrders[index];
                return Padding(
                  padding: EdgeInsets.only(bottom: 2.h),
                  child: _buildOrderCard(context, order),
                );
              }),
            );
          },
        ),
      ],
    );
  }

  // 货源项
  Widget _buildOrderCard(BuildContext context, Order order) {
    return OrderCard(
      order: order,
      role: 'driver',
      //简洁模式
      compact: true,
      onViewDetail: () {
        Navigator.pushNamed(
          context,
          Routes.orderDetail,
          arguments: order,
        );
      },
    );
  }

  // 公告卡片
  Widget _buildAnnouncementCard() {
    return AppCard(
      margin: EdgeInsets.symmetric(horizontal: 12.w),
      padding: EdgeInsets.all(12.w),
      borderRadius: 12,
      elevation: 0,
      outlineAlpha: 0.6,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '司机公告',
            style: AppTextStyles.bodySmall.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '· 新的运费结算规则已更新，请查看详情',
            style: AppTextStyles.bodySmall,
          ),
          SizedBox(height: 4.h),
          Text(
            '· 平台将于6月1日进行系统维护，请留意',
            style: AppTextStyles.bodySmall,
          ),
          SizedBox(height: 4.h),
          Text(
            '· 司机认证流程优化，现在可以更快完成认证',
            style: AppTextStyles.bodySmall,
          ),
        ],
      ),
    );
  }
}
