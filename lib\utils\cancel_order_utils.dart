import '../data/models/order.dart';

/// 取消订单工具类
class CancelOrderUtils {
  // 空跑费计算配置
  static const double _feePercentage = 0.1; // 订单金额的10%
  static const double _minFee = 20.0; // 最低费用20元
  static const double _maxFee = 100.0; // 最高费用100元
  static const int _driverFreeCancelMinutes = 30; // 司机免费取消时间窗口（分钟）

  /// 检查订单是否可以取消
  static bool canCancelOrder(Order order, String userId, String userRole) {
    // 运输中、已送达、已完成状态不允许取消
    if (order.statusCode == 11 ||
        order.statusCode == 12 ||
        order.statusCode == 2) {
      return false;
    }

    // 检查用户权限
    if (userRole == 'shipper' && order.customerID != userId) {
      return false; // 不是订单的货主
    }
    if (userRole == 'driver' && order.driverId != userId) {
      return false; // 不是订单的司机
    }

    return true;
  }

  /// 计算取消费用
  static double calculateCancelFee(Order order, String userRole) {
    // 只有已接单状态才可能产生费用
    if (order.statusCode != 1) {
      return 0.0;
    }

    if (userRole == 'shipper') {
      // 货主取消需要支付空跑费
      double baseFee = order.price * _feePercentage;
      return double.parse(baseFee.clamp(_minFee, _maxFee).toStringAsFixed(2));
    } else if (userRole == 'driver') {
      // 司机取消：检查是否在免费时间窗口内
      if (order.acceptTime != null) {
        int minutesSinceAccept =
            DateTime.now().difference(order.acceptTime!).inMinutes;
        if (minutesSinceAccept <= _driverFreeCancelMinutes) {
          return 0.0; // 免费取消
        }
      }
      return 0.0; // 司机超时取消暂不收费，但影响信用
    }

    return 0.0;
  }

  /// 获取取消费用说明
  static String getCancelFeeDescription(
      Order order, String userRole, double fee) {
    if (userRole == 'shipper') {
      if (order.statusCode == 1) {
        return fee > 0 ? '需要支付司机空跑费：¥${fee.toStringAsFixed(2)}' : '免费取消';
      } else {
        return '当前状态可免费取消';
      }
    } else if (userRole == 'driver') {
      if (order.statusCode == 1) {
        if (order.acceptTime != null) {
          int minutesSinceAccept =
              DateTime.now().difference(order.acceptTime!).inMinutes;

          if (minutesSinceAccept <= _driverFreeCancelMinutes) {
            int remaining = _driverFreeCancelMinutes - minutesSinceAccept;
            // 确保剩余时间不为负数
            remaining = remaining > 0 ? remaining : 0;
            return '免费取消窗口（剩余$remaining分钟）';
          } else {
            return '超时取消，可能影响信用评分';
          }
        }
        return '免费取消';
      } else {
        return '当前状态可免费取消';
      }
    }
    return '无法计算取消费用';
  }

  /// 获取取消原因选项
  static List<String> getCancelReasons(String userRole) {
    if (userRole == 'shipper') {
      return [
        '临时改变计划',
        '找到更便宜的运输方案',
        '货物有问题',
        '时间不合适',
        '其他原因',
      ];
    } else if (userRole == 'driver') {
      return [
        '车辆故障',
        '紧急情况',
        '路线不熟悉',
        '时间冲突',
        '其他原因',
      ];
    }
    return ['其他原因'];
  }

  /// 检查是否需要对方确认
  static bool needsConfirmation(Order order, String userRole) {
    // 已接单状态下的取消需要对方确认
    return order.statusCode == 1;
  }

  /// 获取确认超时时间（小时）
  static int getConfirmationTimeoutHours() {
    return 24;
  }
}
