//司机抢单大厅页面，展示可供抢单的订单列表
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hyt/widgets/empty_state.dart';
import 'package:hyt/data/models/order.dart';
import 'package:hyt/view_models/appuser_view_model.dart';
import 'package:hyt/view_models/order_view_model.dart';
import 'package:hyt/widgets/order_card.dart';
import 'package:provider/provider.dart';
import 'package:hyt/widgets/order_action_buttons.dart';
import 'dart:async';
import 'package:hyt/constants/routes.dart';
import 'package:hyt/widgets/search_app_bar.dart'; // 导入自定义搜索AppBar组件
import 'package:hyt/l10n/app_localizations.dart';

import 'package:hyt/styles/text_styles.dart';
import 'package:hyt/styles/app_theme.dart';

class DriverOrderPoolScreen extends StatefulWidget {
  const DriverOrderPoolScreen({super.key});
  @override
  State<DriverOrderPoolScreen> createState() => _DriverOrderPoolScreenState();
}

class _DriverOrderPoolScreenState extends State<DriverOrderPoolScreen>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  late TabController _tabController;
  final List<String> _tabTitles = ['全部', '附近', '同城', '跨城', '加急'];
  final TextEditingController _searchController = TextEditingController();
  Timer? _timer; // 定时刷新用
  DateTime? _lastRefreshTime; // 最后刷新时间

  @override
  void initState() {
    super.initState();
    // 添加应用生命周期监听
    WidgetsBinding.instance.addObserver(this);

    _tabController = TabController(length: _tabTitles.length, vsync: this);
    _searchController.addListener(() {
      setState(() {}); // 搜索框变化触发过滤
    });

    // 页面进入时自动刷新
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshOrders();
    });

    // 定时刷新 - 减少间隔到15秒，因为订单可能被其他司机抢走
    _timer = Timer.periodic(const Duration(seconds: 15), (timer) {
      if (mounted) {
        _refreshOrders();
      }
    });
  }

  // 统一的刷新方法
  void _refreshOrders() {
    final appUserViewModel =
        Provider.of<AppUserViewModel>(context, listen: false);
    final orderViewModel = Provider.of<OrderViewModel>(context, listen: false);
    final userId = appUserViewModel.currentUser?.id;
    if (userId != null) {
      orderViewModel.loadPendingOrders();
      setState(() {
        _lastRefreshTime = DateTime.now();
      });
      debugPrint('刷新待接单订单列表');
    }
  }

  // 监听应用生命周期变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用从后台回到前台时，立即刷新数据
    if (state == AppLifecycleState.resumed && mounted) {
      debugPrint('应用回到前台，刷新抢单数据');
      _refreshOrders();
    }
  }

  @override
  void dispose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    _tabController.dispose();
    _searchController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final orderViewModel = Provider.of<OrderViewModel>(context);
    final List<Order> availableOrders =
        orderViewModel.pendingOrders; // 只用pendingOrders

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: SearchAppBar(
        searchController: _searchController,
        tabController: _tabController,
        tabTitles: _tabTitles,
        hintText:
            AppLocalizations.of(context)?.searchOrderHint ?? '搜索订单号/地址/货物',
        centerTitle: true,
        elevation: 0.5,
        backgroundColor: Theme.of(context).colorScheme.surface,
        showTabs: true,
        // 对齐消息中心Tab的UI与字号
        tabLabelStyle:
            AppTextStyles.withWeight(AppTextStyles.bodyMedium, FontWeight.bold),
        tabUnselectedLabelStyle: AppTextStyles.bodyMedium,
        tabIndicatorWeight: 3,
        tabIsScrollable: true,
        tabAlignment: TabAlignment.center,
        actions: const [],
      ),
      body: TabBarView(
        controller: _tabController,
        children: _tabTitles.map((title) {
          // 根据标签和搜索关键字筛选订单
          List<Order> filteredOrders = availableOrders;
          // 1. 按 Tab 筛选
          if (title != '全部') {
            // 根据标签筛选，这里可以根据实际需求实现
            if (title == '附近') {
              // 筛选附近订单的逻辑
            } else if (title == '同城') {
              // 筛选同城订单的逻辑
            } else if (title == '跨城') {
              // 筛选跨城订单的逻辑
            } else if (title == '加急') {
              // 筛选加急订单的逻辑
            }
          }
          // 2. 按搜索关键字筛选
          String searchTerm = _searchController.text.trim().toLowerCase();
          if (searchTerm.isNotEmpty) {
            filteredOrders = filteredOrders.where((order) {
              final id = order.orderId.toLowerCase();
              final pickup = order.pickupAddress.toLowerCase();
              final delivery = order.deliveryAddress.toLowerCase();

              return id.contains(searchTerm) ||
                  pickup.contains(searchTerm) ||
                  delivery.contains(searchTerm);
            }).toList();
          }
          // 将筛选后的订单列表传递给构建方法
          return _buildOrderList(filteredOrders);
        }).toList(),
      ),
    );
  }

  // 构建订单列表
  Widget _buildOrderList(List<Order>? orders) {
    final safeOrders = orders ?? [];
    if (safeOrders.isEmpty) {
      return RefreshIndicator(
        onRefresh: () async {
          await Provider.of<OrderViewModel>(context, listen: false)
              .loadPendingOrders();
        },
        child: ListView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: AppInsets.pageGutter()
              .add(EdgeInsets.only(top: AppInsets.gap8())),
          children: [
            SizedBox(height: AppInsets.gap12()),
            EmptyState(
              useCard: true,
              cardMargin: EdgeInsets.zero,
              title: AppLocalizations.of(context)?.noOrders ?? '暂无可抢订单',
              description:
                  AppLocalizations.of(context)?.orderListDesc ?? '敬请期待更多货源',
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _refreshOrders();
        // 等待加载完成
        await Future.delayed(const Duration(milliseconds: 500));
      },
      child: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: AppInsets.pageGutter()
                  .add(EdgeInsets.only(top: AppInsets.gap2())),
              itemCount: safeOrders.length,
              cacheExtent: 120.h,
              addAutomaticKeepAlives: false,
              physics: const AlwaysScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                final order = safeOrders[index];
                return _buildOrderCard(order);
              },
            ),
          ),
          // 显示最后刷新时间
          if (_lastRefreshTime != null)
            Container(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              child: Text(
                '最后更新: ${_formatRefreshTime(_lastRefreshTime!)}。下拉立即刷新',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 格式化刷新时间
  String _formatRefreshTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inSeconds < 60) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }

  // 构建订单卡片
  Widget _buildOrderCard(Order order) {
    final orderViewModel = context.read<OrderViewModel>();
    return OrderCard(
      order: order,
      role: 'driver',
      onViewDetail: () {
        // 跳转到订单详情页
        Navigator.pushNamed(
          context,
          Routes.orderDetail,
          arguments: order,
        );
      },
      actionButtons: [
        OrderActionButtons(
            order: order,
            role: 'driver',
            scene: 'list',
            onAcceptOrder: () {
              final appUserViewModel = context.read<AppUserViewModel>();
              orderViewModel.acceptOrder(order.orderId, appUserViewModel);
            },
            onContact: () => orderViewModel.contactOrderPerson(context, order,
                contactType: 'shipper'),
            onPickup: () => orderViewModel.pickupOrder(order.orderId),
            onDeliver: () {
              final appUserViewModel = context.read<AppUserViewModel>();
              orderViewModel.deliverOrder(appUserViewModel, order.orderId);
            }),
      ],
    );
  }
}
