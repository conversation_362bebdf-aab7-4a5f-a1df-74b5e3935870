// 评价实体模型
import 'package:hyt/utils/timezone_utils.dart';

class Review {
  final String? id; // 数据库主键，可为空
  final String fromUserId; // 评价人ID
  final String toUserId; // 被评价人ID
  final String toUserRole; // 被评价人角色 'driver' 或 'shipper'
  final int rating; // 星级 1~5
  final String comment; // 评价内容
  final String? orderId; // 关联订单ID，可为空
  final DateTime createdAt; // 评价时间

  Review({
    this.id,
    required this.fromUserId,
    required this.toUserId,
    required this.toUserRole,
    required this.rating,
    required this.comment,
    this.orderId,
    required this.createdAt,
  });

  // 从Map创建Review对象
  factory Review.fromMap(Map<String, dynamic> map) {
    return Review(
      id: map['id'],
      fromUserId: map['from_user_id'] ?? '',
      toUserId: map['to_user_id'] ?? '',
      toUserRole: map['to_user_role'] ?? '',
      rating: map['rating'] ?? 5.0,
      comment: map['comment'] ?? '',
      orderId: map['order_id'],
      createdAt:
          TimezoneUtils.parseFromDatabase(map['created_at']) ?? DateTime.now(),
    );
  }

  // 转换为Map
  Map<String, dynamic> toMap() {
    final map = {
      'from_user_id': fromUserId,
      'to_user_id': toUserId,
      'to_user_role': toUserRole,
      'rating': rating,
      'comment': comment,
      'order_id': orderId,
      'created_at': TimezoneUtils.toDatabase(createdAt),
    };
    if (id != null) {
      map['id'] = id;
    }
    return map;
  }
}
